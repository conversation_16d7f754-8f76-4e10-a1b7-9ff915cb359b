{"name": "@apps/intentnow-server", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"dev": "nest start --debug --watch", "shopify:dev": "caddy reverse-proxy --from localhost:4001 --to localhost:4000 & pnpm run dev", "build": "nest build", "publish-dto": "pnpm build && ./publish-dto.sh", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest --force-exit --detect<PERSON><PERSON><PERSON><PERSON><PERSON>", "test:watch": "jest --watch --<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "test:cov": "jest --coverage --force-exit --detect<PERSON><PERSON><PERSON><PERSON><PERSON>", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typecheck": "tsc --noEmit", "graphql-codegen": "graphql-codegen && cp ./src/types/shopify-api/admin.types.d.ts ./src/types/shopify-api/admin.types.ts"}, "dependencies": {"@amplitude/analytics-node": "^1.3.6", "@clerk/backend": "^2.0.0", "@clerk/express": "^1.7.6", "@codebrew/nestjs-storage": "^0.1.7", "@eropple/nestjs-correlation-id": "^1.0.1", "@google-cloud/firestore": "^7.9.0", "@nestjs/cache-manager": "^2.3.0", "@nestjs/common": "^11.1.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.1", "@nestjs/mongoose": "^11.0.3", "@nestjs/platform-express": "^11.1.1", "@nestjs/swagger": "^11.2.0", "@opentelemetry/api": "^1.9.0", "@opentelemetry/auto-instrumentations-node": "^0.56.0", "@opentelemetry/exporter-metrics-otlp-grpc": "^0.57.2", "@opentelemetry/exporter-prometheus": "^0.57.2", "@opentelemetry/exporter-trace-otlp-grpc": "^0.57.2", "@opentelemetry/instrumentation-express": "^0.47.0", "@opentelemetry/instrumentation-http": "^0.57.2", "@opentelemetry/instrumentation-mongoose": "^0.49.0", "@opentelemetry/instrumentation-nestjs-core": "^0.44.0", "@opentelemetry/instrumentation-pino": "^0.46.0", "@opentelemetry/instrumentation-undici": "^0.10.1", "@opentelemetry/sdk-metrics": "^1.30.1", "@opentelemetry/sdk-node": "^0.57.2", "@opentelemetry/sdk-trace-base": "^1.30.1", "@packages/shared-entities": "workspace:^", "@shopify/graphql-client": "^1.1.0", "@shopify/shopify-api": "^11.2.0", "@shopify/shopify-app-express": "^5.0.3", "@shopify/shopify-app-session-storage": "^3.0.3", "@shopify/shopify-app-session-storage-memory": "^4.0.3", "@shopify/shopify-app-session-storage-redis": "^4.0.3", "@shopify/web-pixels-extension": "^2.18.0", "@slynova/flydrive": "^1.0.3", "@slynova/flydrive-gcs": "^1.0.3", "@tkashiro/shopify-app-session-storage-firestore": "^1.0.1", "cache-manager": "^5.7.6", "cache-manager-redis-yet": "^5.1.5", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cryptojs": "^2.5.3", "dotenv": "^16.4.5", "firebase-admin": "^13.1.0", "lodash": "^4.17.21", "mongoose": "^8.9.2", "mongoose-paginate-v2": "^1.9.1", "multer": "1.4.5-lts.2", "murlock": "^4.0.0", "nanoid": "^3.3.7", "nestjs-cls": "^6.0.1", "nestjs-form-data": "^1.9.93", "nestjs-otel": "^6.1.2", "nestjs-pino": "^4.1.0", "pino-http": "^10.2.0", "pino-pretty": "^11.2.2", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "sharp": "^0.34.3", "statsig-node": "^6.0.1"}, "devDependencies": {"@graphql-codegen/cli": "^5.0.2", "@nestjs/cli": "^11.0.7", "@nestjs/schematics": "^11.0.5", "@nestjs/testing": "^11.1.1", "@shopify/api-codegen-preset": "^1.0.1", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/lodash": "^4.17.16", "@types/multer": "^1.4.12", "@types/node": "^20.3.1", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "mongodb": "^6.18.0", "mongodb-memory-server": "^10.1.4", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node", "modulePaths": ["<rootDir>"]}}