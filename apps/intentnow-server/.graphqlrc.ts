import { shopifyApiProject, ApiType } from '@shopify/api-codegen-preset'

export default {
  // For syntax highlighting / auto-complete when writing operations
  schema: 'https://shopify.dev/admin-graphql-direct-proxy/2024-07',
  documents: ['./src/shopify/**/*.{js,ts}'],
  projects: {
    // To produce variable / return types for Admin API operations
    default: shopifyApiProject({
      apiType: ApiType.Admin,
      //Need to also upate shopify-config.ts to match this version
      apiVersion: '2024-07',
      documents: ['./src/shopify/**/*.{js,ts}', './src/intentnow/**/*.{js,ts}'],
      outputDir: './src/types/shopify-api',
    }),
  },
}
