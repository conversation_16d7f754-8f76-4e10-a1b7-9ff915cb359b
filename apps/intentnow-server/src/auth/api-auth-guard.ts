import {
  CanActivate,
  ExecutionContext,
  Inject,
  Injectable,
  Logger,
  UnauthorizedException,
} from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { Request } from 'express'
import { ClerkConfig, ServerConfig } from 'src/config/config'
import { UserContext, UserRequest, AuthType } from './entities/user-context'
import { FIREBASE_ADMIN_INJECT, FirebaseAdminSDK } from 'src/firebase-admin'
import { auth } from 'firebase-admin'
import { Firestore } from '@google-cloud/firestore'
import { Cache } from 'cache-manager'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import {
  ClerkSessionPayload,
  shoipifyStoresCollectionPath,
  ShopifyStore,
} from '@packages/shared-entities'
import { ShopifyRequestContext } from 'src/shopify/entities/shopify'
import { ClerkClient, createClerkClient, verifyToken } from '@clerk/backend'
import { ClsService } from 'nestjs-cls'

export function validateIsAdmin(requester: UserContext | undefined) {
  if (requester?.roles?.admin) {
    return requester!
  }

  throw new UnauthorizedException('Admin permission required')
}

export function validateShopifyRequest(
  requester: UserContext | undefined,
  requiresAdmin = false
): ShopifyRequestContext {
  if (!requester) {
    throw new UnauthorizedException('User required')
  }

  const request = requester.shopifyContext
  if (!request) {
    throw new UnauthorizedException('Shopify request context required')
  }

  if (!request.shop) {
    throw new UnauthorizedException('Shop required')
  }

  if (requester.roles?.admin) {
    return request
  }

  if (requiresAdmin) {
    throw new UnauthorizedException('Admin required')
  }

  if (
    requester.authType === AuthType.shopifyAdmin ||
    requester.authType === AuthType.shopifyProxy
  ) {
    if (requester.userId !== request.shop) {
      throw new UnauthorizedException('Mismatched shop in session')
    }
  }
  return request
}

@Injectable()
export class ApiAuthGuard implements CanActivate {
  private readonly logger = new Logger(ApiAuthGuard.name)
  private readonly robotTokens: string[] | undefined
  private readonly allowedEmails: string[] | undefined
  private readonly shopifyAppHandles: string[] | undefined
  private readonly appName: string
  private readonly clerkConfig: ClerkConfig | undefined
  private readonly clerkClient: ClerkClient | undefined

  constructor(
    configService: ConfigService,
    @Inject(FIREBASE_ADMIN_INJECT)
    private readonly firebaseAdmin: FirebaseAdminSDK,
    @Inject('FIRESTORE_CLIENT') private readonly firestore: Firestore,
    @Inject(CACHE_MANAGER) private readonly cacheManager: Cache,
    private readonly cls: ClsService
  ) {
    this.robotTokens =
      configService.get<ServerConfig['auth']>('auth')?.robotTokens
    this.allowedEmails =
      configService.get<ServerConfig['auth']>('auth')?.allowedUserEmails

    this.shopifyAppHandles = configService
      .get<ServerConfig['shopify']>('shopify')
      ?.shopifyAppConfigs?.map((config) => config.shopifyAppHandle)
    this.appName = configService.get<ServerConfig['app']>('app')!.name

    this.clerkConfig = configService.get<ClerkConfig>('clerk')
    this.clerkClient = this.clerkConfig
      ? createClerkClient({
          secretKey: this.clerkConfig.secretKey,
          publishableKey: this.clerkConfig.publishableKey,
        })
      : undefined
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<UserRequest>()
    const { authProvider, impersonated, token } =
      this.extractTokenFromHeader(request)
    if (!token) {
      throw new UnauthorizedException('auth/none-token')
    }

    let userContext: UserContext | undefined
    if (this.robotTokens && this.robotTokens.includes(token)) {
      this.logger.log('canActivate: robot token found, access granted')
      userContext = this.robotUser(impersonated)
    } else if (authProvider === 'CLERK') {
      //Validate Clerk Auth token
      if (!this.clerkClient || !this.clerkConfig) {
        throw new UnauthorizedException('auth/clerk-not-configured')
      }

      try {
        const jwt: ClerkSessionPayload = await verifyToken(token, {
          jwtKey: this.clerkConfig.jwtKey,
        })
        const roles: string[] = (jwt.metadata as any)?.roles ?? ['merchant']

        userContext = {
          authType: AuthType.clerk,
          userId: jwt.sub,
          userInfo: {
            email: jwt.metadata?.userInfo?.email,
            displayName: jwt.metadata?.userInfo?.fullName,
            imageUrl: jwt.metadata?.userInfo?.imageUrl,
          },
          roles: {
            admin: roles.includes('admin'),
            merchant: roles.includes('merchant'),
          },
        } satisfies UserContext
      } catch (e: any) {
        this.logger.warn(
          {
            reason: e.reason,
          },
          'Failed to validate Clerk token'
        )
        throw new UnauthorizedException(`auth/${e.reason}`)
      }
    } else if (authProvider === 'FIREBASE') {
      //Validate Firebase Auth token
      try {
        const payload = await this.firebaseAdmin
          .auth()
          .verifyIdToken(token, true)
        const user = await auth().getUser(payload.uid)

        if (!user.email || !this.allowedEmails?.includes(user.email)) {
          this.logger.error(`User email not allowed: ${user.email}`)
          throw new UnauthorizedException('auth/invalid-email')
        }

        userContext = {
          authType: AuthType.firebase,
          userId: user.uid,
          userInfo: {
            email: user.email,
            displayName: user.displayName,
          },
          roles: {
            admin: true,
            merchant: false,
          },
        } satisfies UserContext
      } catch (e: any) {
        if (e?.errorInfo?.code === 'auth/id-token-expired') {
          this.logger.warn('Expired Firebase token', e)
        } else {
          this.logger.warn('Failed to validate Firebase token', e)
        }
        throw new UnauthorizedException(e?.errorInfo?.code)
      }
    } else {
      throw new UnauthorizedException('auth/invalid-auth-provider')
    }

    // if (request.params.storeId) {
    //   const { shop, appHandle } = await this.getConfigByStoreId(
    //     request.params.storeId
    //   )
    //   if (!shop || !appHandle) {
    //     throw new UnauthorizedException('auth/invalid-store-id')
    //   }

    //   userContext.shopifyContext = {
    //     shop,
    //     appHandle: appHandle,
    //   }
    // }

    this.cls.set('user', userContext)
    request['user'] = userContext

    return true
  }

  useRobotUser() {
    this.cls.set('user', this.robotUser())
  }

  robotUser(impersonated?: string): UserContext {
    if (impersonated) {
      return {
        authType: AuthType.robot,
        userId: impersonated,
        roles: {
          admin: false,
          merchant: true,
        },
        //impersonatedBy: 'X-ROBOT-USER',
      } satisfies UserContext
    } else {
      return {
        authType: AuthType.robot,
        userId: 'X-ROBOT-USER',
        roles: {
          admin: true,
          merchant: false,
        },
      }
    }
  }

  async robotUserForShop(shop: string) {
    const user = this.robotUser()
    await this.appendShopifyContext(user, shop)
    return user
  }

  async appendShopifyContext(user: UserContext | undefined, shop: string) {
    if (!user) {
      this.logger.warn({ shop }, `appendShopifyContext: no user`)
      return
    }

    const { appHandle } = await this.getConfigByShop(shop)
    if (!appHandle) {
      this.logger.warn({ shop }, `appendShopifyContext: store not found`)
      return
    }

    user.shopifyContext = {
      shop,
      appHandle,
    }
  }

  private extractTokenFromHeader(request: Request) {
    const [type, tokenInHeader] =
      request.headers.authorization?.split(' ') ?? []
    const token = type === 'Bearer' ? tokenInHeader : undefined
    const authProviderHeader = request.headers['x-auth-provider'] as
      | string
      | undefined
    const impersonated = request.headers['x-impersonate'] as string | undefined

    let authProvider: string | undefined
    let authTenant: string | undefined
    if (authProviderHeader) {
      const splits = authProviderHeader.split(':', 2)
      authProvider = splits[0]
      authTenant = splits[1]
    }

    return {
      authProvider,
      authTenant,
      impersonated,
      token,
    }
  }

  private async getConfigByStoreId(storeId: string) {
    const cacheKey = `intentnow-auth-configByStoreId:${this.appName}:${storeId}`
    let storeData = await this.cacheManager.get<{
      shop?: string
      appHandle?: string
    }>(cacheKey)

    if (!storeData) {
      const snap = await this.firestore
        .collection(shoipifyStoresCollectionPath)
        .doc(storeId)
        .get()
      if (snap.exists) {
        const store = {
          ...snap.data(),
          _id: snap.id,
        } as ShopifyStore
        storeData = {
          shop: store.shop,
          //Fall back to the first app handle if not specified in the config (for test stores that normally have multiple apps installed)
          appHandle: store.appHandle ?? this.shopifyAppHandles?.[0],
        }
      } else {
        this.logger.warn({ storeId }, `getConfigByStoreId: store not found`)
        storeData = { shop: undefined, appHandle: undefined }
      }

      await this.cacheManager.set(cacheKey, storeData, 600000)
    }

    return storeData
  }

  private async getConfigByShop(shop: string) {
    const cacheKey = `intentnow-auth-configByShop:${this.appName}:${shop}`
    let storeData = await this.cacheManager.get<{
      appHandle?: string
    }>(cacheKey)

    if (!storeData) {
      const snap = await this.firestore
        .collection(shoipifyStoresCollectionPath)
        .where('shop', '==', shop)
        .limit(1)
        .get()
      if (!snap.empty) {
        const store = {
          ...snap.docs[0].data(),
          _id: snap.docs[0].id,
        } as ShopifyStore

        let appHandle = store.appHandle
        if (
          this.shopifyAppHandles &&
          (!appHandle || !this.shopifyAppHandles.includes(appHandle))
        ) {
          this.logger.warn(
            { shop },
            `getConfigByShop: invalid app handle in config, falling back to first app handle in list`
          )
          //Fall back to the first app handle if not specified in the config (for test stores that normally have multiple apps installed)
          appHandle = this.shopifyAppHandles[0]
        }

        storeData = {
          appHandle,
        }
      } else {
        this.logger.warn({ shop }, `getConfigByShop: store not found`)
        storeData = { appHandle: undefined }
      }

      await this.cacheManager.set(cacheKey, storeData, 600000)
    }

    return storeData
  }
}
