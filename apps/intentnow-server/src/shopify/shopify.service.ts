import { Inject, Injectable, Logger } from '@nestjs/common'
import { shopifyApp, ShopifyApp } from '@shopify/shopify-app-express'
import {
  ShopifyServerConfig,
  ShopifySessionStorageType,
} from 'src/config/shopify-config'
import { RedisSessionStorage } from '@shopify/shopify-app-session-storage-redis'
import { PageInfo } from '@shopify/shopify-api'
import { SessionStorage } from '@shopify/shopify-app-session-storage'
import { ConfigService } from '@nestjs/config'
import { GQLExtensions, ResponseErrors } from '@shopify/graphql-client'
import {
  GetAppInstallationQuery,
  GetAppInstallationQueryVariables,
  GetShopInfoQuery,
  GetShopInfoQueryVariables,
  SetMetafildsMutation,
  SetMetafildsMutationVariables,
} from 'src/types/shopify-api/admin.generated'
import { ServerConfig } from 'src/config/config'
import { MemorySessionStorage } from '@shopify/shopify-app-session-storage-memory'
import { ShopifyRequestContext } from './entities/shopify'
import { Firestore } from '@google-cloud/firestore'
import { FirestoreSessionStorage } from '@tkashiro/shopify-app-session-storage-firestore'
import { MetafieldsSetInput } from 'src/types/shopify-api/admin.types'
import {
  validateIsAdmin,
  validateShopifyRequest,
} from 'src/auth/api-auth-guard'
import { UserContext } from 'src/auth/entities/user-context'

export enum ShopifyRestOp {
  get = 'get',
  post = 'post',
  put = 'put',
  delete = 'delete',
}

@Injectable()
export class ShopifyService {
  private readonly logger = new Logger(ShopifyService.name)
  private readonly shopifyApps: {
    [handle: string]: {
      shopifyApp: ShopifyApp
      shopifyAppHandle: string
      shopifyAppHandle2?: string
      restApiPath: string
      promoEmbedId?: string
    }
  }

  private readonly shopifyServerConfig: ShopifyServerConfig

  constructor(
    configService: ConfigService,
    @Inject('FIRESTORE_CLIENT') private readonly firestore: Firestore
  ) {
    const shopifyCfg = configService.get<ShopifyServerConfig>('shopify')
    const cacheCfg = configService.get<ServerConfig['cache']>('cache')
    if (!shopifyCfg) {
      throw Error('Shopify config not found')
    }
    this.shopifyServerConfig = shopifyCfg

    this.shopifyApps = {}

    this.shopifyServerConfig.shopifyAppConfigs.forEach((cfg) => {
      let sessionStorage: SessionStorage
      if (
        this.shopifyServerConfig.sessionStorageType ===
        ShopifySessionStorageType.firestore
      ) {
        sessionStorage = new FirestoreSessionStorage({
          firestore: this.firestore,
          collectionName: `ShopifyApps/${cfg.shopifyAppHandle}/ShopifySessions`,
        })
      } else if (
        this.shopifyServerConfig.sessionStorageType ===
        ShopifySessionStorageType.redis
      ) {
        const redisUrl = cacheCfg!.redisUrl!
        sessionStorage = new RedisSessionStorage(redisUrl)
      } else if (
        this.shopifyServerConfig.sessionStorageType ===
        ShopifySessionStorageType.memory
      ) {
        sessionStorage = new MemorySessionStorage()
      } else {
        throw new Error(
          `Invalid session storage type: ${this.shopifyServerConfig.sessionStorageType}`
        )
      }

      const shopifyAppInst = shopifyApp({
        ...cfg.shopifyAppConfig,
        sessionStorage,
      })
      const restApiPath = `admin/api/${cfg.shopifyAppConfig.api!.apiVersion}`
      this.shopifyApps[cfg.shopifyAppHandle] = {
        shopifyApp: shopifyAppInst,
        shopifyAppHandle: cfg.shopifyAppHandle,
        shopifyAppHandle2: cfg.shopifyAppHandle2,
        restApiPath,
        promoEmbedId: cfg.promoEmbedId,
      }
    })
  }

  //TODO: make all the functions call validateShopifyRequest() directly
  validateRequester(
    requester: UserContext | undefined,
    requiresAdmin = false
  ): ShopifyRequestContext {
    return validateShopifyRequest(requester, requiresAdmin)
  }

  getShopifyApp(appHandle: string): ShopifyApp {
    return this.shopifyApps[appHandle].shopifyApp
  }

  getShopifyApps() {
    return Object.values(this.shopifyApps)
  }

  getShopifyAppConfig(appHandle: string) {
    return this.shopifyApps[appHandle]
  }

  getShopifyAppHandle(appHandle: string): string {
    return this.shopifyApps[appHandle].shopifyAppHandle
  }

  getShopifyAppHandle2(appHandle: string): string | undefined {
    return this.shopifyApps[appHandle].shopifyAppHandle2
  }

  getPromoEmbedId(appHandle: string): string | undefined {
    return this.shopifyApps[appHandle].promoEmbedId
  }

  async executeGraphQl<QT, VT>(
    requester: UserContext | undefined,
    graphQl: {
      query: string
      variables?: VT
    },
    ignoreErrors = false
  ): Promise<{
    data?: QT
    errors?: ResponseErrors
    extensions?: GQLExtensions
  }> {
    const request = this.validateRequester(requester)
    const session = await this.getSession(requester)

    try {
      const client = new this.shopifyApps[
        request.appHandle
      ].shopifyApp.api.clients.Graphql({
        session,
      })

      const result = await client.request<QT>(graphQl.query, {
        variables: graphQl.variables,
      } as any)

      return {
        data: result.data,
        errors: result.errors,
        extensions: result.extensions,
      }
    } catch (e) {
      if (!ignoreErrors) {
        this.logger.error(`executeGraphQl: failed`)
        this.logger.error(e)
      }
      throw e
    }
  }

  async getSession(requester: UserContext | undefined) {
    const request = this.validateRequester(requester)
    const sessionId = this.shopifyApps[
      request.appHandle
    ].shopifyApp.api.session.getOfflineId(request.shop)
    const session =
      await this.shopifyApps[
        request.appHandle
      ].shopifyApp.config.sessionStorage.loadSession(sessionId)

    if (!session) {
      this.logger.error(
        {
          shop: request.shop,
          appHandle: request.appHandle,
        },
        `getRestClient: failed to get session`
      )
      throw new Error(`Failed to get session`)
    }
    return session
  }

  async getRestClient(requester: UserContext | undefined) {
    const request = this.validateRequester(requester)
    const session = await this.getSession(requester)

    const client = new this.shopifyApps[
      request.appHandle
    ].shopifyApp.api.clients.Rest({
      session,
    })
    const apiPath = this.shopifyApps[request.appHandle].restApiPath
    return {
      client,
      apiPath,
    }
  }

  async executeRest<RT>(
    requester: UserContext | undefined,
    rest: {
      op: ShopifyRestOp
      path: string
      query?: Record<string, any>
      data?: Record<string, any> | string
    }
  ): Promise<{
    data: RT
    pageInfo?: PageInfo
  }> {
    const { client, apiPath } = await this.getRestClient(requester)

    while (true) {
      try {
        const response = await client[rest.op]<RT>({
          path: `${apiPath}${rest.path}`,
          query: rest.query,
          data: rest.data ?? {},
        })
        return {
          data: response.body,
          pageInfo: response.pageInfo,
        }
      } catch (e) {
        if (e.response?.code === 429) {
          //A simple throttle mechanism
          this.logger.debug(`retryAfter=${e.response?.retryAfter}`)
          const retryAfter = (e.response?.retryAfter as number) ?? 2

          const sleepMs = Math.ceil(Math.random() * 1000) + retryAfter * 1000
          await new Promise((resolve) => {
            setTimeout(resolve, sleepMs)
          })
        } else {
          this.logger.error(e, `executeRest: failed`)
          throw e
        }
      }
    }
  }

  async getShopInfo(requester: UserContext | undefined) {
    const query = `#graphql
      query getShopInfo {
        shop {
          id
          name
          url
          email
          myshopifyDomain
        }
      }`

    const response = await this.executeGraphQl<
      GetShopInfoQuery,
      GetShopInfoQueryVariables
    >(requester, { query })

    return response?.data?.shop
  }

  async getAppInstallation(requester: UserContext | undefined) {
    const query = `#graphql
      query getAppInstallation {
        currentAppInstallation {
          id
          app {
            id
            handle
            title
          }
        }
      }`

    const response = await this.executeGraphQl<
      GetAppInstallationQuery,
      GetAppInstallationQueryVariables
    >(requester, { query })

    return response?.data?.currentAppInstallation
  }

  async getCurrentTheme(requester: UserContext | undefined) {
    this.validateRequester(requester)
    const themes = await this.executeRest<{
      themes: {
        id: string
        name: string
        role: string
        admin_graphql_api_id: string
      }[]
    }>(requester, {
      op: ShopifyRestOp.get,
      path: '/themes.json',
    })

    return themes.data.themes.find((t) => t.role === 'main')
  }

  async setMetafield(
    requester: UserContext | undefined,
    metafields: MetafieldsSetInput[]
  ) {
    const query = `#graphql
      mutation setMetafilds($metafields: [MetafieldsSetInput!]!) {
        metafieldsSet(metafields: $metafields) {
          userErrors {
            field
            message
          }
          metafields {
            id
            key
            namespace
            value
          }
        }
      }`
    const variables = {
      metafields,
    }
    const response = await this.executeGraphQl<
      SetMetafildsMutation,
      SetMetafildsMutationVariables
    >(requester, { query, variables })
    if (response.data?.metafieldsSet?.userErrors.length) {
      throw new Error(
        `Invalid input: ${response.data.metafieldsSet.userErrors}`
      )
    }
  }

  //Admin only: for onboarding purpse
  //This function return secret keys of all apps, please use it with extreme causion.
  generateShopifyAppConfigs(
    requester: UserContext | undefined,
    newShopifyApp: {
      appHandle: string
      appName: string
      apiKey: string
      secretKey: string
      promoEmbedId?: string
    }
  ) {
    validateIsAdmin(requester)

    const serverConfigs = this.shopifyServerConfig.shopifyAppConfigs.map(
      (c) => c.rawConfig
    )
    const webConfigs = serverConfigs.map((c) => ({
      APP_HANDLE: c.APP_HANDLE,
      API_KEY: c.API_KEY,
    }))

    const { serverConfig, webConfig } =
      this.generateNewShopifyAppConfig(newShopifyApp)

    serverConfigs.push(serverConfig)
    webConfigs.push(webConfig)

    return {
      serverConfigs,
      webConfigs,
    }
  }

  private generateNewShopifyAppConfig(newShopifyApp: {
    appHandle: string
    appName: string
    apiKey: string
    secretKey: string
    promoEmbedId?: string
  }) {
    //We don't know the exact rule how Shopify generate the id using the app name, so this is just a guess based on past results.
    const appHandle2Buf: string[] = []
    for (let i = 0; i < newShopifyApp.appName.length; i++) {
      const ch = newShopifyApp.appName.charAt(i).toLowerCase()
      if (('0' <= ch && ch <= '9') || ('a' <= ch && ch <= 'z')) {
        appHandle2Buf.push(ch)
      } else if (ch === ' ') {
        appHandle2Buf.push('-')
      }
    }
    let appHandle2: string | undefined = appHandle2Buf.join('')
    if (appHandle2 === newShopifyApp.appHandle) {
      appHandle2 = undefined
    }

    const serverConfig = {
      APP_HANDLE: newShopifyApp.appHandle,
      API_KEY: newShopifyApp.apiKey,
      SECRET_KEY: newShopifyApp.secretKey,
      PROMO_EMBED_ID: newShopifyApp.promoEmbedId,
      APP_HANDLE_2: appHandle2,
    }

    const webConfig = {
      APP_HANDLE: newShopifyApp.appHandle,
      API_KEY: newShopifyApp.apiKey,
    }

    return {
      serverConfig,
      webConfig,
    }
  }

  //Admin only
  generateAppTomlFile(
    requester: UserContext | undefined,
    shopifyApp: {
      appHandle: string
      appName: string
      apiKey: string
    }
  ) {
    validateIsAdmin(requester)
    const { appHandle, appName, apiKey } = shopifyApp

    return `# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "${apiKey}"
name = "${appName}"
handle = "${appHandle}"
application_url = "https://app.intentnow.com/intentnow/shopify?handle=${appHandle}"
embedded = true

[build]
include_config_on_deploy = true

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "read_customer_events,read_price_rules,read_themes,write_discounts,write_pixels,read_products"

[auth]
redirect_urls = [
  "https://api2.intentnow.com/api/shopify/${appHandle}/login",
  "https://api2.intentnow.com/api/shopify/${appHandle}/callback"
]

[webhooks]
api_version = "2025-01"

[app_proxy]
url = "https://api2.intentnow.com/api/intentnow/shopify-app-proxy/${appHandle}"
subpath = "${appHandle}"
prefix = "apps"

[pos]
embedded = false`
  }
}
