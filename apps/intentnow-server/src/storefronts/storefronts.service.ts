import {
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { OfferDiscountCodeConfigDto } from 'src/auth/entities/user-context'
import { hashSHA256ToNumber } from 'src/common/utils'
import { StoreCampaignDto } from 'src/dto/campaign.dto'
import { GetDiscountResponseDto } from 'src/dto/intentnow.dto'
import { OfferDiscountConfigDto, StoreOfferDto } from 'src/dto/offer.dto'
import { EventService } from 'src/intentnow/event.service'
import { GetDiscountService } from 'src/intentnow/get-discount.service'
import { IntentnowService } from 'src/intentnow/intentnow.service'
import { StoreCampaignsService } from 'src/stores/store-campaigns.service'
import { StoreOffersService } from 'src/stores/store-offers.service'
import { StoresService } from 'src/stores/stores.service'
import { DiscountCodeBasicInput } from 'src/types/shopify-api/admin.types'
import { UserPermissions } from 'src/users/user-permissions'
import {
  DiscountConfig,
  EventRequestDto,
  EventResponseDto,
} from '@packages/shared-entities'
import Statsig from 'statsig-node'
import * as amplitude from '@amplitude/analytics-node'
import { ServerConfig } from 'src/config/config'
import { ApiAuthGuard } from 'src/auth/api-auth-guard'
import {
  StoreConfigDto,
  StoreLaunchConfigDto,
  StoreLaunchConfigVariantDto,
} from 'src/dto/store.dto'

@Injectable()
export class StorefrontsService {
  private readonly logger = new Logger(StorefrontsService.name)
  private readonly intentowConfig: ServerConfig['intentnow']

  constructor(
    private readonly configService: ConfigService,
    private readonly storesService: StoresService,
    private readonly storeCampaignsService: StoreCampaignsService,
    private readonly storeOffersService: StoreOffersService,
    private readonly intentnowService: IntentnowService,
    private readonly getDiscountService: GetDiscountService,
    private readonly eventService: EventService,
    private readonly userPermissions: UserPermissions,
    private readonly apiAuthGuard: ApiAuthGuard
  ) {
    //TODO: create storefronts config
    this.intentowConfig =
      this.configService.get<ServerConfig['intentnow']>('intentnow')!
  }

  async getShopifyStorefrontOfferTop(
    shop: string,
    clientId: string | undefined,
    preview?: boolean | undefined,
    previewWidgetId?: string | undefined
  ): Promise<GetDiscountResponseDto> {
    const userContext =
      await this.userPermissions.requiresAccessOnShopifyStore(shop)

    let { value: isNewStore } = Statsig.getFeatureGate(
      {
        userID: shop,
      },
      'shopify-storefront-is-new-store'
    )

    if (isNewStore) {
      const { storeInfo } =
        await this.storesService.findStoreInfoByMyshopifyDomain(shop)
      if (!storeInfo) {
        this.logger.error(
          {
            shop,
          },
          `getShopifyStorefrontOfferTop: store not found`
        )
        if (preview) {
          throw new NotFoundException('Store not found')
        }
        return {}
      }

      if (storeInfo.storeConfig?.isLegacy) {
        this.logger.log(
          {
            shop,
            storeId: storeInfo.storeId,
          },
          `getShopifyStorefrontOfferTop: store is legacy, treat as old store`
        )
        isNewStore = false
      }
    }

    this.logger.log(
      {
        shop,
        clientId,
        preview,
        isNewStore,
      },
      `getShopifyStorefrontOfferTop: started`
    )

    if (isNewStore) {
      const { storeInfo } =
        await this.storesService.findStoreInfoByMyshopifyDomain(
          shop,
          true //grant acess
        )

      if (!storeInfo) {
        this.logger.error(
          {
            shop,
          },
          `getShopifyStorefrontOfferTop: store not found`
        )
        if (preview) {
          throw new NotFoundException('Store not found')
        }
        return {}
      }

      if (preview) {
        return await this.getStorefrontOfferPreview(
          storeInfo.storeId,
          previewWidgetId
        )
      }

      try {
        //TODO: check if the store is eligible to serve offers and if the model is ready (payment status, store settings, web pixel, widget on, launch config exists, etc.)

        this.logger.log(
          {
            shop,
            storeId: storeInfo.storeId,
            showOffers: storeInfo.storeConfig?.showOffers,
          },
          `getShopifyStorefrontOfferTop: start getting offer`
        )

        if (!clientId) {
          this.logger.warn(
            {
              shop,
            },
            `getShopifyStorefrontOfferTop: no clientId`
          )
          return {}
        }

        const { metadata, getDiscountResponse } = storeInfo.storeConfig
          ?.showOffers
          ? await this.getShopifyStorefrontOffer(
              storeInfo.storeId,
              storeInfo.storeConfig,
              shop,
              clientId,
              'getDiscount'
            )
          : {
              metadata: {
                mode: 'getDiscount' as any,
              },
              getDiscountResponse: {},
            }

        if (
          storeInfo.storeConfig?.receiveEvents &&
          storeInfo.storeConfig?.sendAmplitude
        ) {
          await this.storefrontAmplitudeTrack(
            storeInfo.storeId,
            shop,
            clientId,
            metadata
          )
        } else {
          this.logger.warn(
            {
              shop,
            },
            `getShopifyStorefrontOfferTop: receiveEvents orsendAmplitude is off`
          )
        }

        return getDiscountResponse
      } catch (e) {
        this.logger.error(
          e,
          {
            shop,
            clientId,
            preview,
          },
          `getShopifyStorefrontOfferTop: failed`
        )
        if (preview) {
          throw e
        }
        return {}
      }
    } else {
      return this.intentnowService.getClientDiscount(
        userContext,
        clientId,
        preview,
        previewWidgetId
      )
    }
  }

  async getShopifyStorefrontOffer(
    storeId: string,
    storeConfig: StoreConfigDto,
    shop: string,
    clientId: string,
    mode: 'event' | 'prediction' | 'getDiscount'
  ): Promise<{
    metadata: {
      mode: 'event' | 'prediction' | 'getDiscount'
      campaignId?: string
      modelSplit?: string
      bucketName?: string
      promoCandidate?: boolean
      configName?: string
      configVariantIndex?: number
    }
    getDiscountResponse: GetDiscountResponseDto
  }> {
    // Mode:
    // event: bucketing only, no prediction (calls to model), no discount
    // prediction: bucketing, prediction (calls to model), no discount
    // getDiscount: bucketing, prediction, and return discount if applicable

    await this.userPermissions.requiresAccessOnStore(storeId)
    const userContext =
      await this.userPermissions.requiresAccessOnShopifyStore(shop)

    const logging = mode !== 'event'
    logging &&
      this.logger.log(
        {
          storeId,
          shop,
          clientId,
        },
        `getShopifyStorefrontOffer: started`
      )

    const bucketData = await this.allocateClientToCampaignBucket(
      storeId,
      clientId
    )
    if (!bucketData) {
      //No active campaign
      return {
        metadata: {
          mode,
        },
        getDiscountResponse: {},
      }
    }
    const campaignId = bucketData.activeCampaign._id
    const modelSplit = bucketData.allocatedBucket.variant ? 'test' : 'control'
    const bucketName = bucketData.allocatedBucket.variant
      ? `test-${bucketData.allocatedBucket.variant.variantIndex + 1}`
      : 'control'

    const allocatedConfigVariantData =
      await this.allocateClientToLaunchConfigVariants(storeId, clientId)

    if (!allocatedConfigVariantData) {
      //No active launch config
      return {
        metadata: {
          mode,
        },
        getDiscountResponse: {},
      }
    }

    const {
      activeLaunchConfig,
      allocatedBucket: { configVariantIndex, configVariant },
    } = allocatedConfigVariantData

    const configName = activeLaunchConfig.name

    if (mode === 'event') {
      return {
        metadata: {
          mode,
          campaignId,
          modelSplit,
          bucketName,
          configName,
          configVariantIndex,
        },
        getDiscountResponse: {},
      }
    }

    if (mode === 'getDiscount') {
      const lastCheckoutTime = await this.eventService.getLastCheckoutTime(
        shop,
        clientId
      )
      if (lastCheckoutTime) {
        const now = new Date()
        if (now.getTime() < lastCheckoutTime.getTime() + 1000 * 60 * 60 * 3) {
          logging &&
            this.logger.log(
              `getShopifyStorefrontOffer: last checkout is too recent`
            )
          return {
            metadata: {
              mode,
              campaignId,
              modelSplit,
              bucketName,
              configName,
              configVariantIndex,
            },
            getDiscountResponse: {},
          }
        }
      }
    }

    const modelExperiment = bucketData.activeCampaign._id
    //TODO: support other model config versions
    const modelConfig = configVariant.modelConfigV1!

    let modelPromo = false
    let promoCandidate = false

    const { promo, promo_candidate } = await this.getDiscountService.getModel(
      shop,
      clientId,
      modelSplit,
      modelExperiment,
      modelConfig
    )
    modelPromo = promo ?? false
    promoCandidate = promo_candidate ?? false
    this.logger.log(
      {
        shop,
        clientId,
        modelSplit,
        promoCandidate,
        modelPromo,
      },
      `getShopifyStorefrontOffer: model returns promo=${modelPromo}`
    )

    const { modelOverrides } = storeConfig
    if (modelOverrides?.response) {
      modelPromo = modelOverrides.response.promo
      logging &&
        this.logger.log(
          `getShopifyStorefrontOffer: promoConfig overrides, promo=${modelPromo}`
        )
    }

    if (!bucketData.allocatedBucket.variant) {
      //No offer to show regardless the result
      return {
        metadata: {
          mode,
          campaignId,
          modelSplit,
          bucketName,
          promoCandidate,
          configName,
          configVariantIndex,
        },
        getDiscountResponse: {},
      }
    }

    const discountConfig =
      bucketData.allocatedBucket.variant.offer.discountConfig!
    const legacyDiscountConfig: DiscountConfig = {
      codeGen: discountConfig.codeGen,
      discountDurationInMinutes: discountConfig.discountDurationInMinutes,
      cacheValidDurationInMinutes: discountConfig.cacheValidDurationInMinutes,
      discountInput: this.genShopifyDiscountInput(discountConfig.discount),
    }

    if (mode === 'getDiscount') {
      if (modelPromo) {
        //Check if we have a cached discount and create a new one if not
        //TODO: move the getCachedPromo2() logics to use on MongoDB
        const { discount, createdShopifyDiscountData } =
          await this.getDiscountService.getCachedPromo2(
            userContext,
            undefined, //TODO: save generated discount code into a new MongodB table
            bucketData.allocatedBucket.variant?.offer._id,
            legacyDiscountConfig,
            clientId,
            true,
            undefined
          )
        if (createdShopifyDiscountData) {
          await this.storesService.recordShopifyGeneratedDiscount(
            storeId,
            shop,
            clientId,
            bucketData.allocatedBucket.variant?.offer,
            discount,
            createdShopifyDiscountData
          )
        }

        if (discount) {
          logging &&
            this.logger.log(`getShopifyStorefrontOffer: returning a discount`)
          return {
            metadata: {
              mode,
              campaignId,
              modelSplit,
              bucketName,
              promoCandidate,
              configName,
              configVariantIndex,
            },
            getDiscountResponse: {
              discount,
              widgetId: bucketData.allocatedBucket.variant?.offer._id,
              widget3: bucketData.allocatedBucket.variant?.offer.widgetConfig,
            },
          }
        }
      } else {
        //Check if we have a cached discount
        //TODO: move the getCachedPromo2() logics to use on MongoDB
        const { discount } = await this.getDiscountService.getCachedPromo2(
          userContext,
          undefined, //TODO: save generated discount code into a new MongodB table
          bucketData.allocatedBucket.variant?.offer._id,
          legacyDiscountConfig,
          clientId,
          false,
          undefined
        )

        if (discount) {
          this.logger.log(
            `getShopifyStorefrontOffer: returning a cached discount regardless of the model`
          )
          return {
            metadata: {
              mode,
              campaignId,
              modelSplit,
              bucketName,
              promoCandidate,
              configName,
              configVariantIndex,
            },
            getDiscountResponse: {
              discount,
              widgetId: bucketData.allocatedBucket.variant?.offer._id,
              widget3: bucketData.allocatedBucket.variant?.offer.widgetConfig,
            },
          }
        }
      }
    }

    //No discount to return
    return {
      metadata: {
        mode,
        campaignId,
        modelSplit,
        bucketName,
        promoCandidate,
        configName,
        configVariantIndex,
      },
      getDiscountResponse: {},
    }
  }

  async getStorefrontOfferPreview(
    storeId: string,
    previewOfferId?: string | undefined
  ): Promise<GetDiscountResponseDto> {
    if (previewOfferId) {
      const offer = await this.storeOffersService.getStoreOffer(
        storeId,
        previewOfferId
      )

      if (!offer) {
        this.logger.error(
          {
            storeId,
            previewOfferId,
          },
          `getStorefrontOfferPreview: offer not found`
        )
        throw new NotFoundException('Preview offer not found')
      }

      const discount = this.generatePreviewDiscount(offer.discountConfig!)

      return {
        preview: true,
        discount,
        widgetId: offer._id,
        widget3: offer.widgetConfig,
      }
    } else {
      //Randomly return a variant based on the current campaign settings.
      //It can end up in the control bucket.
      const randomClientId = crypto.randomUUID()
      const bucketData = await this.allocateClientToCampaignBucket(
        storeId,
        randomClientId
      )

      if (bucketData?.allocatedBucket.variant) {
        const offer = bucketData.allocatedBucket.variant.offer

        const discount = this.generatePreviewDiscount(offer.discountConfig!)

        return {
          preview: true,
          discount,
          widgetId: offer._id,
          widget3: offer.widgetConfig,
        }
      } else {
        return {}
      }
    }
  }

  generatePreviewDiscount(discountConfig: OfferDiscountCodeConfigDto) {
    const prefix = discountConfig.codeGen.prefix ?? ''
    return {
      title: discountConfig.discount.title,
      code: `${prefix}${discountConfig.codeGen.alphabet.slice(
        0,
        discountConfig.codeGen.length - prefix.length
      )}`,
      startsAt: new Date(),
      endsAt: new Date(),
    }
  }

  async allocateClientToCampaignBucket(
    storeId: string,
    clientId: string
  ): Promise<
    | {
        activeCampaign: StoreCampaignDto
        clientHash: number
        allocatedBucket: {
          bucketIndex: number
          variant?: {
            variantIndex: number
            offer: StoreOfferDto
          }
        }
      }
    | undefined
  > {
    const activeCampaignData =
      await this.storeCampaignsService.getActiveCampaignData(storeId)
    if (!activeCampaignData) {
      return
    }
    const clientHash =
      hashSHA256ToNumber(
        clientId,
        `allocateClientToCampaignBucket-${storeId}`
      ) % 100
    let bucketHash = clientHash
    let bucketIndex: number = -1
    for (let i = 0; i < activeCampaignData.buckets.length; i++) {
      if (bucketHash < activeCampaignData.buckets[i].allocation) {
        bucketIndex = i
        break
      }
      bucketHash -= activeCampaignData.buckets[i].allocation
    }

    if (bucketIndex < 0) {
      //This should never happen unless we have a bug above
      this.logger.error(
        {
          storeId,
          clientId,
          clientIdHash: bucketHash,
          activeCampaignData,
        },
        'allocateClientToBucket: failed to calculate bucket index'
      )
      throw new InternalServerErrorException('failed to calculate bucket index')
    }

    const bucketData = {
      activeCampaign: activeCampaignData.activeCampaign,
      clientHash,
      allocatedBucket: {
        bucketIndex,
        variant: activeCampaignData.buckets[bucketIndex].variant,
      },
    }

    return bucketData
  }

  async allocateClientToLaunchConfigVariants(
    storeId: string,
    clientId: string
  ): Promise<
    | {
        activeLaunchConfig: StoreLaunchConfigDto
        clientHash: number
        allocatedBucket: {
          bucketIndex: number
          configVariantIndex: number
          configVariant: StoreLaunchConfigVariantDto
        }
      }
    | undefined
  > {
    const activeLaunchConfigData =
      await this.storesService.getActiveLaunchConfig(storeId)
    if (!activeLaunchConfigData) {
      this.logger.warn(
        {
          storeId,
        },
        'allocateClientToLaunchConfigVariants: no active launch config found'
      )
      return
    }
    const clientHash =
      hashSHA256ToNumber(
        clientId,
        `allocateClientToLaunchConfigVariants-${storeId}-${activeLaunchConfigData.launchConfig._id}`
      ) % 100
    let bucketHash = clientHash
    let bucketIndex: number = -1
    for (let i = 0; i < activeLaunchConfigData.buckets.length; i++) {
      if (bucketHash < activeLaunchConfigData.buckets[i].allocation) {
        bucketIndex = i
        break
      }
      bucketHash -= activeLaunchConfigData.buckets[i].allocation
    }

    if (bucketIndex < 0) {
      //This should never happen unless we have a bug above
      this.logger.error(
        {
          storeId,
          clientId,
          clientIdHash: bucketHash,
          activeCampaignData: activeLaunchConfigData,
        },
        'allocateClientToBucket: failed to calculate bucket index'
      )
      throw new InternalServerErrorException('failed to calculate bucket index')
    }

    const bucketData = {
      activeLaunchConfig: activeLaunchConfigData.launchConfig,
      clientHash,
      allocatedBucket: {
        bucketIndex,
        configVariantIndex:
          activeLaunchConfigData.buckets[bucketIndex].configVariantIndex,
        configVariant:
          activeLaunchConfigData.buckets[bucketIndex].configVariant,
      },
    }

    return bucketData
  }

  genShopifyDiscountInput(
    discountConfig: OfferDiscountConfigDto
  ): DiscountCodeBasicInput {
    const commonDiscountInput = {
      appliesOncePerCustomer: true,
      usageLimit: 1,
      customerSelection: {
        all: true,
      },
      ...(discountConfig.minimumOrder > 0
        ? {
            minimumRequirement: {
              subtotal: {
                greaterThanOrEqualToSubtotal: discountConfig.minimumOrder,
              },
            },
          }
        : {}),
      title: discountConfig.title,
    }

    if (discountConfig.type === 'percent') {
      return {
        ...commonDiscountInput,
        customerGets: {
          items: {
            all: true,
          },
          value: {
            percentage: discountConfig.percentOff! / 100,
          },
        },
      }
    } else if (discountConfig.type === 'amount') {
      return {
        ...commonDiscountInput,
        customerGets: {
          items: {
            all: true,
          },
          value: {
            discountAmount: {
              amount: discountConfig.amountOff!,
            },
          },
        },
      }
    } else {
      throw new InternalServerErrorException('discount type not supported')
    }
  }

  async saveShopifyStoreEvents(
    events: EventRequestDto[],
    ipAddress: string | undefined,
    userAgent?: string | undefined
  ): Promise<EventResponseDto[]> {
    if (events.length <= 0 || !events[0].shop || !events[0].clientId) {
      this.logger.error(
        {
          events,
        },
        `saveShopifyStoreEvents: empty events or missing shop or missing clientId`
      )
      return []
    }

    const shop = events[0].shop
    const clientId = events[0].clientId
    let { value: isNewStore } = Statsig.getFeatureGate(
      {
        userID: shop,
      },
      'shopify-storefront-is-new-store'
    )

    if (isNewStore) {
      const { storeInfo } =
        await this.storesService.findStoreInfoByMyshopifyDomain(shop)
      if (storeInfo?.storeConfig?.isLegacy) {
        this.logger.log(
          {
            shop,
          },
          `saveShopifyStoreEvents: store is legacy, treat as old store`
        )
        isNewStore = false
      }
    }

    if (isNewStore) {
      //Prediction and user bucketing logics for new stores
      let trackableEvent = false
      let isPageView = false
      for (const event of events) {
        if (event.type === 'standard' || event.type === 'custom-intentnow') {
          trackableEvent = true
          if (event.name === 'page_viewed') {
            isPageView = true
            break
          }
        }
      }

      //Add a limited robot user context if there is no user context
      await this.apiAuthGuard.callWithUser(
        await this.apiAuthGuard.robotUserForShop(shop),
        async () => {
          const { storeInfo } =
            await this.storesService.findStoreInfoByMyshopifyDomain(
              shop,
              true //grant acess
            )
          if (!storeInfo) {
            this.logger.error(
              {
                shop,
              },
              `saveShopifyStoreEvents: store not found`
            )
            return events.map(() => ({ message: 'Store not found' }))
          }

          if (!storeInfo.storeConfig?.receiveEvents) {
            this.logger.warn(
              {
                shop,
                storeId: storeInfo.storeId,
              },
              `saveShopifyStoreEvents: receiveEvents is off`
            )
            return events.map(() => ({ message: 'Event not received' }))
          }

          if (trackableEvent) {
            const prediction =
              storeInfo.storeConfig?.predictOffers && isPageView
            if (prediction) {
              this.logger.log(
                {
                  shop,
                  storeId: storeInfo.storeId,
                },
                `saveShopifyStoreEvents: predictOffers is on`
              )
            }

            const { metadata } = await this.getShopifyStorefrontOffer(
              storeInfo.storeId,
              storeInfo.storeConfig,
              shop,
              clientId,
              prediction ? 'prediction' : 'event'
            )

            if (storeInfo.storeConfig?.sendAmplitude) {
              await this.storefrontAmplitudeTrack(
                storeInfo.storeId,
                shop,
                clientId,
                metadata
              )
            }
          }
        }
      )
    }

    return await this.eventService.saveEvents(events, ipAddress, userAgent)
  }

  async storefrontAmplitudeTrack(
    storeId: string,
    shop: string,
    clientId: string,
    {
      mode,
      campaignId,
      modelSplit,
      bucketName,
      promoCandidate,
      configName,
      configVariantIndex,
    }: {
      mode: 'event' | 'prediction' | 'getDiscount'
      campaignId?: string
      modelSplit?: string
      bucketName?: string
      promoCandidate?: boolean
      configName?: string
      configVariantIndex?: number
    }
  ) {
    const identifyObj = new amplitude.Identify()
    identifyObj.set('eventVersion', this.intentowConfig.analyticsEventVersion)

    //TODO: do we want to get this into the event property?
    identifyObj.set('storeId', storeId)

    if (campaignId) {
      identifyObj.set('campaignId', campaignId)
    } else {
      identifyObj.unset('campaignId')
    }

    if (bucketName) {
      identifyObj.set('campaignBucket', bucketName)
    } else {
      identifyObj.unset('campaignBucket')
    }

    //Tracking for launchConfig (legacy)
    if (configName && modelSplit === 'test') {
      identifyObj.set('launchConfig', `${configName}_${configVariantIndex}`)
    } else {
      identifyObj.unset('launchConfig')
    }

    //Legacy property for compatibility purpose
    if (modelSplit) {
      identifyObj.set('split_model-exp', modelSplit)
    } else {
      identifyObj.unset('split_model-exp')
    }

    if (mode === 'getDiscount' || mode === 'prediction') {
      if (promoCandidate !== undefined) {
        identifyObj.set(`promoCandidate`, promoCandidate)
      } else {
        identifyObj.unset(`promoCandidate`)
      }
    }

    amplitude.identify(identifyObj, {
      user_id: clientId,
      device_id: clientId,
    })

    if (mode === 'getDiscount' || mode === 'prediction') {
      if (promoCandidate !== undefined) {
        await this.eventService.saveEvent(
          {
            id: crypto.randomUUID(),
            name: 'intentnow-promo-candidate',
            shop,
            clientId,
            type: 'custom-intentnow',
            eventSource: 'intentnow-server',
            timestamp: new Date().toISOString(),
            data: {
              promoCandidate: promoCandidate,
            },
          },
          undefined,
          undefined
        )
      }
    }
  }
}
