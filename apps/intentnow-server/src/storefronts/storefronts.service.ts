import { Injectable, Logger } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { UserContext } from 'src/auth/entities/user-context'
import { IntentnowService } from 'src/intentnow/intentnow.service'
import { ShopifyService } from 'src/shopify/shopify.service'

@Injectable()
export class StorefrontsService {
  private readonly logger = new Logger(StorefrontsService.name)

  constructor(
    private readonly configService: ConfigService,
    private readonly shopifyService: ShopifyService,
    private readonly intentnowService: IntentnowService
  ) {}

  async getStorefrontOffer(
    requester: UserContext | undefined,
    clientId: string | undefined,
    preview?: boolean | undefined,
    previewWidgetId?: string | undefined
  ) {
    const request = this.shopifyService.validateRequester(requester)
    const shop = request.shop!

    this.logger.log(
      {
        shop,
        clientId,
        preview,
      },
      `getStorefrontOffer: started`
    )

    return this.intentnowService.getClientDiscount(
      requester,
      clientId,
      preview,
      previewWidgetId
    )
  }
}
