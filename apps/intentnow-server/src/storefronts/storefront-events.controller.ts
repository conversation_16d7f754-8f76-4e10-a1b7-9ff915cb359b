import { Body, Controller, Logger, Post, Req } from '@nestjs/common'
import { UserRequest } from 'src/auth/entities/user-context'
import { MetricService } from 'nestjs-otel'
import { Counter } from '@opentelemetry/api'
import {
  EventResponseDto,
  EventsRequestDto,
  RawEventResponseDto,
  RawEventsRequestDto,
} from '../dto/event.dto'
import { plainToInstance } from 'class-transformer'
import { StorefrontsService } from './storefronts.service'
import { EventService } from 'src/intentnow/event.service'

@Controller()
export class StorefrontEventsController {
  private readonly logger = new Logger(StorefrontEventsController.name)
  private readonly eventCallsCounter: Counter

  constructor(
    private readonly storefrontsService: StorefrontsService,
    private readonly eventService: EventService,
    private readonly metricService: MetricService
  ) {
    this.eventCallsCounter = this.metricService.getCounter('event_calls', {
      prefix: 'intentnow_server',
    })
  }

  async adaptiveSaveEvents(req: UserRequest, payload: EventsRequestDto) {
    const xForwardedFor = req.headers['x-forwarded-for'] as string | undefined
    const ipAddress = xForwardedFor?.split?.(',')[0] ?? req.ip ?? 'unknown'
    const userAgent = req.headers['user-agent'] as string | undefined

    this.eventCallsCounter.add(1, {
      type: payload.type,
    })
    const resp = await this.storefrontsService.saveShopifyStoreEvents(
      payload.events,
      ipAddress,
      userAgent
    )
    return resp.map((r) => plainToInstance(EventResponseDto, r))
  }

  @Post('api/intentnow/events')
  async saveEvents(
    @Req() req: UserRequest,
    @Body()
    payload: EventsRequestDto
  ): Promise<EventResponseDto[]> {
    return await this.adaptiveSaveEvents(req, payload)
  }

  @Post('api/intentnow/raw-events')
  async saveRawEvents(
    @Req() req: UserRequest,
    @Body()
    payload: RawEventsRequestDto
  ): Promise<RawEventResponseDto[]> {
    const resp = await this.eventService.saveRawEvents(payload.events)
    return resp.map((r) => plainToInstance(RawEventResponseDto, r))
  }

  //To be compatible with the old event endpoint
  @Post('v1/events')
  async saveEvents2(
    @Req() req: UserRequest,
    @Body()
    payload: EventsRequestDto
  ): Promise<EventResponseDto[]> {
    return await this.adaptiveSaveEvents(req, payload)
  }
}
