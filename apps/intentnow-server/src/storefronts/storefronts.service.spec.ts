import { Test, TestingModule } from '@nestjs/testing'
import { StorefrontsService } from './storefronts.service'
import { StoreCampaignsService } from 'src/stores/store-campaigns.service'
import { ConfigService } from '@nestjs/config'
import { UserPermissions } from 'src/users/user-permissions'
import { StoreConfigService } from 'src/intentnow/store-config.service'
import { EventService } from 'src/intentnow/event.service'
import { defaultStoreOfferDiscountCodeConfig } from '@packages/shared-entities'
import { OfferDiscountConfigDto } from 'src/dto/offer.dto'
import { GetDiscountService } from 'src/intentnow/get-discount.service'
import { StoresService } from 'src/stores/stores.service'

describe('StorefrontsService', () => {
  let service: StorefrontsService

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [StorefrontsService, ConfigService],
    })
      .useMocker((token) => {
        if (token === 'CONFIGURATION_TOKEN') {
          return {
            app: {
              name: 'test-app',
            },
          }
        } else if (token === UserPermissions) {
          return {
            requiresAccessOnShopifyStore: jest.fn(),
            requiresAccessOnStore: jest.fn(),
          }
        } else if (token === StoresService) {
          return {
            getActiveLaunchConfig: (storeId: string) => {
              if (storeId === 'test-store-1') {
                return {
                  launchConfig: {
                    _id: 'test-launch-config-id',
                    name: 'launch-config',
                  },
                  buckets: [
                    {
                      allocation: 100,
                      configVariantIndex: 0,
                      configVariant: {
                        modelConfigV1: {
                          temperature: 0.9,
                        },
                      },
                    },
                  ],
                }
              } else if (storeId === 'test-store-2') {
                return {
                  launchConfig: {
                    _id: 'test-launch-config-id',
                    name: 'launch-config',
                  },
                  buckets: [
                    {
                      allocation: 50,
                      configVariantIndex: 0,
                      configVariant: {
                        modelConfigV1: {
                          temperature: 0.9,
                        },
                      },
                    },
                    {
                      allocation: 50,
                      configVariantIndex: 1,
                      configVariant: {
                        modelConfigV1: {
                          temperature: 0.2,
                        },
                      },
                    },
                  ],
                }
              } else if (storeId === 'test-store-3') {
                return {
                  launchConfig: {
                    _id: 'test-launch-config-different-id',
                    name: 'launch-config',
                  },
                  buckets: [
                    {
                      allocation: 50,
                      configVariantIndex: 0,
                      configVariant: {
                        modelConfigV1: {
                          temperature: 0.9,
                        },
                      },
                    },
                    {
                      allocation: 50,
                      configVariantIndex: 1,
                      configVariant: {
                        modelConfigV1: {
                          temperature: 0.2,
                        },
                      },
                    },
                  ],
                }
              }

              return undefined
            },
          }
        } else if (token === StoreCampaignsService) {
          return {
            getActiveCampaignData: (storeId: string) => {
              if (storeId === 'test-store-1') {
                return {
                  activeCampaign: {
                    _id: 'test-campaign-id',
                    name: 'test-campaign',
                  },
                  buckets: [
                    { allocation: 9 },
                    {
                      allocation: 16,
                      variant: {
                        variantIndex: 0,
                        offer: {
                          _id: 'test-offer-1',
                          name: 'test-offer-1',
                          discountConfig: defaultStoreOfferDiscountCodeConfig,
                          widgetConfig: {
                            type: 'widget3',
                            dialog: {},
                            teaser: {},
                          },
                        },
                      },
                    },
                    {
                      allocation: 78,
                      variant: {
                        variantIndex: 1,
                        offer: {
                          _id: 'test-offer-2',
                          name: 'test-offer-2',
                          discountConfig: defaultStoreOfferDiscountCodeConfig,
                          widgetConfig: {
                            type: 'widget3',
                            dialog: {},
                            teaser: {},
                          },
                        },
                      },
                    },
                  ],
                }
              }
            },
          }
        } else if (token === StoreConfigService) {
          return {
            getStoreConfigForGetDiscountV2: () => {
              return {
                configName: 'launch-config',
                configVariantIndex: 1,
                configVariant: {
                  modelConfig: {
                    parameters: {
                      temperature: 0.9,
                    },
                  },
                  discountConfig: {},
                  widget: {},
                },
                activeShopifyStoreConfig: {
                  currentLaunchConfig: {
                    name: 'launch-config',
                  },
                },
              }
            },
          }
        } else if (token === GetDiscountService) {
          return {
            getModel: (shop: string, clientId: string) => {
              if (clientId === 'test-client-0') {
                return {
                  promo: true,
                  promo_candidate: true,
                }
              } else if (clientId === 'test-client-1234') {
                return {
                  promo: false,
                  promo_candidate: true,
                }
              } else {
                return {
                  promo: false,
                  promo_candidate: false,
                }
              }
            },
            getCachedPromo2: (
              requester: any,
              shopifyStoreId: string,
              discountConfigId: string | undefined,
              discountConfig: any,
              clientId: string
            ) => {
              if (clientId === 'test-client-0') {
                return {
                  discount: {
                    title: '10% Off',
                    code: '10OFF',
                    startsAt: new Date(),
                    endsAt: new Date(),
                  },
                  created: true,
                }
              } else {
                return {
                  discount: undefined,
                  created: false,
                }
              }
            },
          }
        } else if (token === EventService) {
          return {
            getLastCheckoutTime: () =>
              new Date(Date.now() - 1000 * 60 * 60 * 4),
          }
        }
        return {}
      })
      .compile()

    service = module.get<StorefrontsService>(StorefrontsService)
  })

  it('should be defined', () => {
    expect(service).toBeDefined()
  })

  it('allocateClientToCampaignBucket', async () => {
    let result = await service.allocateClientToCampaignBucket(
      'test-store-1',
      'test-client-0'
    )
    expect(result).toEqual({
      activeCampaign: {
        _id: 'test-campaign-id',
        name: 'test-campaign',
      },
      allocatedBucket: {
        bucketIndex: 2,
        variant: {
          variantIndex: 1,
          offer: {
            _id: 'test-offer-2',
            name: 'test-offer-2',
            discountConfig: expect.anything(),
            widgetConfig: expect.anything(),
          },
        },
      },
      clientHash: 65,
    })

    result = await service.allocateClientToCampaignBucket(
      'test-store-1',
      'test-client-1234'
    )
    expect(result).toEqual({
      activeCampaign: {
        _id: 'test-campaign-id',
        name: 'test-campaign',
      },
      allocatedBucket: {
        bucketIndex: 0,
        variant: undefined,
      },
      clientHash: 8,
    })
  })

  it('allocateClientToLaunchConfigVariants', async () => {
    let result = await service.allocateClientToLaunchConfigVariants(
      'test-store-2',
      'test-client-0'
    )
    expect(result).toEqual({
      activeLaunchConfig: {
        _id: 'test-launch-config-id',
        name: 'launch-config',
      },
      allocatedBucket: {
        bucketIndex: 1,
        configVariantIndex: 1,
        configVariant: {
          modelConfigV1: {
            temperature: 0.2,
          },
        },
      },
      clientHash: 80,
    })

    //User falls into a different bucket
    result = await service.allocateClientToLaunchConfigVariants(
      'test-store-2',
      'test-client-3'
    )
    expect(result).toEqual({
      activeLaunchConfig: {
        _id: 'test-launch-config-id',
        name: 'launch-config',
      },
      allocatedBucket: {
        bucketIndex: 0,
        configVariantIndex: 0,
        configVariant: {
          modelConfigV1: {
            temperature: 0.9,
          },
        },
      },
      clientHash: 31,
    })

    //A store with same launch config but different launch config ID
    result = await service.allocateClientToLaunchConfigVariants(
      'test-store-3',
      'test-client-0'
    )
    expect(result).toEqual({
      activeLaunchConfig: {
        _id: 'test-launch-config-different-id',
        name: 'launch-config',
      },
      allocatedBucket: {
        bucketIndex: 0,
        configVariantIndex: 0,
        configVariant: {
          modelConfigV1: {
            temperature: 0.9,
          },
        },
      },
      clientHash: 14,
    })

    //A store without an active launch config
    result = await service.allocateClientToLaunchConfigVariants(
      'test-store-4',
      'test-client-3'
    )
    expect(result).toBeUndefined()
  })

  it('genShopifyDiscountInput', () => {
    let discountConfig: OfferDiscountConfigDto = {
      title: '10% Off',
      type: 'percent',
      percentOff: 10,
      minimumOrder: 50,
    }
    let result = service.genShopifyDiscountInput(discountConfig)
    expect(result).toEqual({
      appliesOncePerCustomer: true,
      usageLimit: 1,
      customerSelection: {
        all: true,
      },
      customerGets: {
        items: {
          all: true,
        },
        value: {
          percentage: 0.1,
        },
      },
      minimumRequirement: {
        subtotal: {
          greaterThanOrEqualToSubtotal: 50,
        },
      },
      title: '10% Off',
    })

    discountConfig = {
      title: '$20 Off',
      type: 'amount',
      amountOff: 20,
      minimumOrder: 0,
    }

    result = service.genShopifyDiscountInput(discountConfig)
    expect(result).toEqual({
      appliesOncePerCustomer: true,
      usageLimit: 1,
      customerSelection: {
        all: true,
      },
      customerGets: {
        items: {
          all: true,
        },
        value: {
          discountAmount: {
            amount: 20,
          },
        },
      },
      title: '$20 Off',
    })
  })

  it('getShopifyStorefrontOffer', async () => {
    //Test group and get promo
    let result = await service.getShopifyStorefrontOffer(
      'test-store-1',
      {},
      'test-store.myshopify.com',
      'test-client-0',
      'getDiscount'
    )
    expect(result).toEqual({
      metadata: {
        mode: 'getDiscount',
        campaignId: 'test-campaign-id',
        modelSplit: 'test',
        bucketName: 'test-2',
        promoCandidate: true,
        configName: 'launch-config',
        configVariantIndex: 0,
      },
      getDiscountResponse: {
        discount: {
          title: '10% Off',
          code: '10OFF',
          startsAt: expect.any(Date),
          endsAt: expect.any(Date),
        },
        widgetId: 'test-offer-2',
        widget3: {
          type: 'widget3',
          dialog: {},
          teaser: {},
        },
      },
    })

    //Control group
    result = await service.getShopifyStorefrontOffer(
      'test-store-1',
      {},
      'test-store.myshopify.com',
      'test-client-1234',
      'getDiscount'
    )
    expect(result).toEqual({
      metadata: {
        mode: 'getDiscount',
        campaignId: 'test-campaign-id',
        modelSplit: 'control',
        bucketName: 'control',
        promoCandidate: true,
        configName: 'launch-config',
        configVariantIndex: 0,
      },
      getDiscountResponse: {},
    })

    //Test group and no promo
    result = await service.getShopifyStorefrontOffer(
      'test-store-1',
      {},
      'test-store.myshopify.com',
      'test-client-34',
      'getDiscount'
    )
    expect(result).toEqual({
      metadata: {
        mode: 'getDiscount',
        campaignId: 'test-campaign-id',
        modelSplit: 'test',
        bucketName: 'test-1',
        promoCandidate: false,
        configName: 'launch-config',
        configVariantIndex: 0,
      },
      getDiscountResponse: {},
    })
  })
})
