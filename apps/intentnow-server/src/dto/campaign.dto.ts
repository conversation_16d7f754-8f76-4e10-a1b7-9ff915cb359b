import { Exclude, Expose, Type } from 'class-transformer'
import { DocumentDto, PaginatedDataDto } from './common.dto'
import { PartialType } from '@nestjs/swagger'
import {
  IsBoolean,
  IsDate,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  Max,
  Min,
} from 'class-validator'

export enum StoreCampaignStatus {
  active = 'active',
  disabled = 'disabled',
  notStarted = 'notStarted',
  ended = 'ended',
  unknown = 'unknown',
}

@Exclude()
export class StoreCampaignDto extends DocumentDto {
  @Expose()
  storeId: string

  @Expose()
  createdAt: Date

  @Expose()
  updatedAt: Date

  @Expose()
  name: string

  @Expose()
  enabled: boolean

  @Expose()
  startDate: Date

  @Expose()
  endDate?: Date

  @Expose()
  launchRatio: number

  //Derived
  @Expose()
  status?: StoreCampaignStatus

  // @Expose()
  // widgetId: string
}

export class PaginatedStoreCampaignsDto extends PaginatedDataDto(
  StoreCampaignDto
) {}

export class StoreCampaignCreateDto {
  @IsNotEmpty()
  @IsString()
  name: string

  @IsNotEmpty()
  @IsBoolean()
  enabled: boolean

  @IsNotEmpty()
  @IsDate()
  @Type(() => Date)
  startDate: Date

  @IsOptional()
  @IsDate()
  @Type(() => Date)
  endDate?: Date

  @IsNotEmpty()
  @IsInt()
  @Min(1)
  @Max(100)
  launchRatio: number

  // @IsNotEmpty()
  // @IsString()
  // widgetId: string
}

export class StoreCampaignUpdateDto extends PartialType(
  StoreCampaignCreateDto
) {}
