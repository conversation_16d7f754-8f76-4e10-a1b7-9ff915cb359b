import { Exclude, Expose } from 'class-transformer'
import { DocumentDto, PaginatedDataDto } from './common.dto'
import { IsEnum, IsNotEmpty, IsString } from 'class-validator'
import { PartialType } from '@nestjs/swagger'

@Exclude()
export class UserDto extends DocumentDto {
  @Expose()
  authType: 'clerk' | 'firebase'

  @Expose()
  createdAt: Date

  @Expose()
  updatedAt: Date

  @Expose()
  displayName: string

  @Expose()
  email: string

  @Expose()
  deletedAt?: Date
}

export class UserCreateDto {
  @IsNotEmpty()
  @IsString()
  _id: string

  @IsNotEmpty()
  @IsEnum(['clerk'])
  authType: 'clerk'

  @IsNotEmpty()
  @IsString()
  displayName: string

  @IsNotEmpty()
  @IsString()
  email: string
}

export class UserUpdateDto extends PartialType(UserCreateDto) {}

export class PaginatedUsersDto extends PaginatedDataDto(UserDto) {}

@Exclude()
export class UserStoreAccessDto {
  @Expose()
  userId: string

  @Expose()
  storeId: string

  @Expose()
  grantedBy: string

  @Expose()
  createdAt: Date
}

export class UserStoreAccessCreateDto {
  @IsNotEmpty()
  @IsString()
  storeId: string

  @IsNotEmpty()
  @IsString()
  grantedBy: string
}

export class PaginatedUserStoreAccessesDto extends PaginatedDataDto(
  UserStoreAccessDto
) {}
