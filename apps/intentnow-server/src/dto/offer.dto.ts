import { PartialType } from '@nestjs/swagger'
import { Exclude, Expose, Type } from 'class-transformer'
import {
  IsNotEmpty,
  IsString,
  IsNumber,
  IsIn,
  IsOptional,
  ValidateNested,
  Min,
  Max,
  IsInt,
} from 'class-validator'
import { DocumentDto, PaginatedDataDto } from './common.dto'

@Exclude()
export class OfferLaunchConfigDto {
  @Expose()
  @IsNumber()
  @Min(0)
  @Max(100)
  launchRatio: number
}

export class OfferWidgetConfigDto {}

@Exclude()
export class CodeGenConfigDto {
  @Expose()
  @IsNotEmpty()
  @IsString()
  alphabet: string

  @Expose()
  @IsNumber()
  @Min(1)
  length: number

  @Expose()
  @IsOptional()
  @IsString()
  prefix?: string
}

export class OfferDiscountConfigDto {
  @Expose()
  @IsNotEmpty()
  @IsIn(['percent', 'amount'])
  discountType: 'percent' | 'amount'

  @Expose()
  @IsInt()
  @IsOptional()
  @Min(1)
  percentOff?: number

  @Expose()
  @IsNumber()
  @IsOptional()
  @Min(0.01)
  amountOff?: number

  @Expose()
  @IsNumber()
  @IsOptional()
  @Min(0)
  minimumOrder: number
}

@Exclude()
export class OfferDiscountCodeConfigDto {
  @Expose()
  @ValidateNested()
  @Type(() => CodeGenConfigDto)
  codeGen: CodeGenConfigDto

  @Expose()
  @ValidateNested()
  @Type(() => OfferDiscountConfigDto)
  discount: OfferDiscountConfigDto

  @Expose()
  @IsNumber()
  @Min(1)
  discountDurationInMinutes: number

  @Expose()
  @IsNumber()
  @Min(1)
  cacheValidDurationInMinutes: number
}

@Exclude()
export class StoreOfferDto extends DocumentDto {
  @Expose()
  storeId: string

  @Expose()
  createdAt: Date

  @Expose()
  updatedAt: Date

  @Expose()
  name: string

  @Expose()
  @IsNotEmpty()
  @IsIn(['draft', 'active', 'offline', 'scheduled'])
  status: 'draft' | 'active' | 'offline'

  @Expose()
  @IsOptional()
  @Type(() => OfferLaunchConfigDto)
  launchConfig?: OfferLaunchConfigDto

  @Expose()
  @IsOptional()
  @Type(() => OfferWidgetConfigDto)
  widgetConfig?: OfferWidgetConfigDto

  @Expose()
  @IsOptional()
  @Type(() => OfferDiscountCodeConfigDto)
  discountConfig?: OfferDiscountCodeConfigDto
}

export class PaginatedStoreOffersDto extends PaginatedDataDto(StoreOfferDto) {}

export class StoreOfferCreateDto {
  @IsNotEmpty()
  @IsString()
  name: string

  @Expose()
  @IsNotEmpty()
  @IsIn(['draft', 'active', 'offline'])
  status: 'draft' | 'active' | 'offline'

  @IsOptional()
  @ValidateNested()
  @Type(() => OfferLaunchConfigDto)
  launchConfig?: OfferLaunchConfigDto

  @IsOptional()
  @ValidateNested()
  @Type(() => OfferWidgetConfigDto)
  widgetConfig?: OfferWidgetConfigDto

  @IsOptional()
  @ValidateNested()
  @Type(() => OfferDiscountCodeConfigDto)
  discountConfig?: OfferDiscountCodeConfigDto
}

export class StoreOfferUpdateDto extends PartialType(StoreOfferCreateDto) {}
