import { Exclude, Expose, Type } from 'class-transformer'
import { IsNotEmpty, IsOptional, IsString } from 'class-validator'
import { DocumentDto, PaginatedDataDto } from './common.dto'
import { PartialType } from '@nestjs/swagger'

export class StoreShopifyConfigDto {
  @IsNotEmpty()
  @IsString()
  myshopifyDomain: string

  @IsOptional()
  @IsNotEmpty()
  @IsString()
  appHandle?: string
}

@Exclude()
export class StoreDto extends DocumentDto {
  @Expose()
  name: string

  @Expose()
  website: string

  @Expose()
  shopifyConfig?: StoreShopifyConfigDto

  @Expose()
  createdAt: Date
}

export class StoreCreateDto {
  @IsNotEmpty()
  @IsString()
  name: string

  @IsNotEmpty()
  @IsString()
  website: string

  @IsOptional()
  @Type(() => StoreShopifyConfigDto)
  shopifyConfig?: StoreShopifyConfigDto
}

export class StoreUpdateDto extends PartialType(StoreCreateDto) {}

export class PaginatedStoresDto extends PaginatedDataDto(StoreDto) {}

@Exclude()
export class LinkStoreRequestDto extends DocumentDto {
  @Expose()
  status: string

  @Expose()
  storeType: string

  @Expose()
  storeRef: string

  @Expose()
  storeName: string

  @Expose()
  storeWebsite: string

  @Expose()
  source: string

  @Expose()
  createdAt: Date

  @Expose()
  expiresAt: Date
}

@Exclude()
export class CommitLinkStoreRequestDto {
  @Expose()
  storeId: string
}
