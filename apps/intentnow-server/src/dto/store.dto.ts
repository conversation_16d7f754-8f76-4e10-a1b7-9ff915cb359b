import { Exclude, Expose, Type } from 'class-transformer'
import {
  IsBoolean,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator'
import { DocumentDto, PaginatedDataDto } from './common.dto'
import { PartialType } from '@nestjs/swagger'

@Exclude()
export class StoreShopifyConfigDto {
  @Expose()
  @IsNotEmpty()
  @IsString()
  myshopifyDomain: string

  @Expose()
  @IsOptional()
  @IsNotEmpty()
  @IsString()
  appHandle?: string
}

@Exclude()
export class StoreConfigDto {
  @Expose()
  @IsOptional()
  @IsBoolean()
  receiveEvents?: boolean

  @Expose()
  @IsOptional()
  @IsBoolean()
  sendAmplitude?: boolean

  @Expose()
  @IsOptional()
  @IsBoolean()
  showOffers?: boolean

  @Expose()
  @IsOptional()
  @IsBoolean()
  predictOffers?: boolean

  @Expose()
  @IsOptional()
  @IsBoolean()
  isLegacy?: boolean

  @Expose()
  @IsOptional()
  modelOverrides?: any
}

@Exclude()
export class EffectiveStoreConfigDto {
  @Expose()
  receiveEvents: boolean

  @Expose()
  sendAmplitude: boolean

  @Expose()
  showOffers: boolean

  @Expose()
  predictOffers: boolean

  @Expose()
  isLegacy: boolean

  @Expose()
  modelOverrides?: any
}

@Exclude()
export class MerchantPortalFeaturesDto {
  @Expose()
  fullOfferEditor: boolean

  @Expose()
  internalDiscountConfigSection: boolean
}

@Exclude()
export class StoreDto extends DocumentDto {
  @Expose()
  name: string

  @Expose()
  website: string

  @Expose()
  createdAt: Date

  @Expose()
  config?: StoreConfigDto

  @Expose()
  shopifyConfig?: StoreShopifyConfigDto

  //Derived fields
  @Expose()
  effectiveConfig?: EffectiveStoreConfigDto

  @Expose()
  merchantPortalFeatures?: MerchantPortalFeaturesDto
}

export class StoreCreateDto {
  @IsNotEmpty()
  @IsString()
  name: string

  @IsNotEmpty()
  @IsString()
  website: string

  @IsOptional()
  @Type(() => StoreConfigDto)
  config?: StoreConfigDto

  @IsOptional()
  @Type(() => StoreShopifyConfigDto)
  shopifyConfig?: StoreShopifyConfigDto
}

export class StoreUpdateDto extends PartialType(StoreCreateDto) {}

export class PaginatedStoresDto extends PaginatedDataDto(StoreDto) {}

export class StoreAddUserDto {
  @IsNotEmpty()
  @IsString()
  email: string
}

@Exclude()
export class LinkStoreRequestDto extends DocumentDto {
  @Expose()
  status: string

  @Expose()
  storeType: string

  @Expose()
  storeRef: string

  @Expose()
  storeName: string

  @Expose()
  storeWebsite: string

  @Expose()
  source: string

  @Expose()
  createdAt: Date

  @Expose()
  expiresAt: Date
}

@Exclude()
export class CommitLinkStoreRequestDto {
  @Expose()
  storeId: string
}

@Exclude()
export class ShopifyStoreIntegrationDto {
  @Expose()
  linked: boolean

  @Expose()
  shopifyExtensions?: {
    webPixelOn?: boolean
    promoWidgetOn?: boolean
    promoWidgetToggleLink?: string
  }

  @Expose()
  shopifyStore?: {
    name: string
    myshopifyDomain: string
    appHandle: string
    url: string
    shopifyPlus: boolean
    currencyCode: string
  }
}

export class ShopifyStoreIntegrationUpdateDto {
  @IsOptional()
  @IsBoolean()
  ['shopifyExtensions.webPixelOn']?: boolean
}

@Exclude()
export class StoreImageDto extends DocumentDto {
  @Expose()
  name: string

  @Expose()
  imageUrl: string

  @Expose()
  createdAt: Date

  @Expose()
  fileInfo: {
    filename: string
    mimetype: string
    size: number
    ext: string
    format: string
    width: number
    height: number
  }
}

export class StoreImageCreateDto {
  @IsOptional()
  @IsString()
  name?: string
}

export class PaginatedStoreImagesDto extends PaginatedDataDto(StoreImageDto) {}

@Exclude()
export class ModelConfigV1Dto {
  @Expose()
  @IsNotEmpty()
  @IsString()
  model: string

  @Expose()
  @IsNumber()
  floor: number

  @Expose()
  @IsNumber()
  ceiling: number

  @Expose()
  @IsNumber()
  start: number

  @Expose()
  @IsNumber()
  end: number
}

@Exclude()
export class StoreLaunchConfigVariantDto {
  @Expose()
  modelConfigV1?: ModelConfigV1Dto
}

@Exclude()
export class StoreLaunchConfigDto extends DocumentDto {
  @Expose()
  name: string

  @Expose()
  createdAt: Date

  @Expose()
  configVariants: StoreLaunchConfigVariantDto[]
}

export class StoreLaunchConfigCreateDto {
  @IsNotEmpty()
  @IsString()
  name: string

  @IsNotEmpty()
  @Type(() => StoreLaunchConfigVariantDto)
  configVariants: StoreLaunchConfigVariantDto[]
}

export class PaginatedStoreLaunchConfigsDto extends PaginatedDataDto(
  StoreLaunchConfigDto
) {}

@Exclude()
export class ActiveStoreLaunchConfigDto {
  @Expose()
  activeLaunchConfig?: {
    launchConfig: StoreLaunchConfigDto

    buckets: {
      allocation: number
      configVariantIndex: number
      configVariant: StoreLaunchConfigVariantDto
    }[]
  }
}
