import { Inject, Injectable, Logger, NotFoundException } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { StoresService } from 'src/stores/stores.service'
import { StoreOffer, StoreOfferDocument } from './entities/offer.mongo'
import { Connection, PaginateModel } from 'mongoose'
import { InjectConnection, InjectModel } from '@nestjs/mongoose'
import { UserPermissions } from 'src/users/user-permissions'
import { CrudQuery } from 'src/dto/common.dto'
import {
  buildMongoQuery,
  buildMongoUpdate,
  mapPaginatedMongoResultsToDto,
} from 'src/common/data-helper'
import {
  PaginatedStoreOffersDto,
  StoreOfferCreateDto,
  StoreOfferDto,
  StoreOfferUpdateDto,
} from 'src/dto/offer.dto'
import { plainToInstance } from 'class-transformer'
import { Cache } from 'cache-manager'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { ServerConfig } from 'src/config/config'

@Injectable()
export class StoreOffersService {
  private readonly logger = new Logger(StoreOffersService.name)
  private readonly appName: string

  constructor(
    private readonly configService: ConfigService,
    private readonly userPermissions: UserPermissions,
    private readonly storeService: StoresService,
    @InjectConnection() private mongoConnection: Connection,
    @InjectModel(StoreOffer.name)
    private readonly storeOfferModel: PaginateModel<StoreOfferDocument>,
    @Inject(CACHE_MANAGER) private readonly cacheManager: Cache
  ) {
    this.appName = configService.get<ServerConfig['app']>('app')!.name
  }

  async getStoreOffer(
    storeId: string,
    offerId: string
  ): Promise<StoreOfferDto> {
    await this.userPermissions.requiresAccessOnStore(storeId)
    await this.storeService.getStore(storeId)

    const storeOffer = await this.storeOfferModel.findById(offerId)
    if (!storeOffer || storeOffer.storeId !== storeId) {
      throw new NotFoundException('StoreOffer not found')
    }

    const offer = plainToInstance(StoreOfferDto, storeOffer.toObject())
    return offer
  }

  async getStoreOffers(storeId: string, crudQuery: CrudQuery) {
    await this.userPermissions.requiresAccessOnStore(storeId)
    await this.storeService.getStore(storeId)

    const { filter, options } = buildMongoQuery(this.storeOfferModel, crudQuery)
    const paginatedResults = await this.storeOfferModel.paginate(
      {
        ...filter,
        storeId: {
          $eq: storeId,
        },
      },
      options
    )

    const offers = mapPaginatedMongoResultsToDto(
      paginatedResults,
      StoreOfferDto,
      PaginatedStoreOffersDto
    )
    return offers
  }

  async createStoreOffer(
    storeId: string,
    newStoreOffer: StoreOfferCreateDto
  ): Promise<StoreOfferDto> {
    await this.userPermissions.requiresAccessOnStore(storeId)
    await this.storeService.getStore(storeId)

    //TODO: validate values

    const now = new Date()
    const newOffer = {
      storeId,
      createdAt: now,
      updatedAt: now,
      name: newStoreOffer.name,
      status: newStoreOffer.status,
      launchConfig: newStoreOffer.launchConfig,
      widgetConfig: newStoreOffer.widgetConfig,
      discountConfig: newStoreOffer.discountConfig,
    } satisfies StoreOffer
    const created = await this.storeOfferModel.create(newOffer)
    return plainToInstance(StoreOfferDto, created.toObject())
  }

  async updateStoreOffer(
    storeId: string,
    OfferId: string,
    updateDto: StoreOfferUpdateDto
  ): Promise<StoreOfferDto> {
    await this.userPermissions.requiresAccessOnStore(storeId)
    await this.storeService.getStore(storeId)

    const updateObj = buildMongoUpdate(updateDto, {
      shopifyConfig: {},
    })

    //TODO
    throw new Error('Not implemented')

    //return plainToInstance(StoreOfferDto, storeOffer.toObject())
  }

  async deleteStoreOffer(storeId: string, offerId: string) {
    await this.userPermissions.requiresAccessOnStore(storeId)

    //Validate storeId/offerId
    await this.getStoreOffer(storeId, offerId)

    const deleted = await this.storeOfferModel.findByIdAndDelete(offerId)
    if (!deleted) {
      throw new NotFoundException('StoreOffer not found')
    }
    return plainToInstance(StoreOfferDto, deleted.toObject())
  }

  async getActiveOffer(
    storeId: string,
    skipCache?: boolean
  ): Promise<StoreOfferDto | undefined> {
    await this.userPermissions.requiresAccessOnStore(storeId)
    await this.storeService.getStore(storeId)

    const cacheKey = `intentnow-activeStoreOffer:${this.appName}:${storeId}`
    if (!skipCache) {
      const cachedOffer = await this.cacheManager.get<StoreOfferDto>(cacheKey)
      if (cachedOffer) {
        return cachedOffer
      }
    }

    //TODO
    throw new Error('Not implemented')

    // await this.cacheManager.set(cacheKey, offer, 1000 * 60) //cache for 1 minute
    // return offer
  }
}
