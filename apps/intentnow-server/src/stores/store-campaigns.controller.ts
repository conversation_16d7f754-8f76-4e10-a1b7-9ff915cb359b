import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseBoolPipe,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common'
import { StoreCampaignsService } from './store-campaigns.service'
import { CrudQuery, CrudQueryDto } from 'src/dto/common.dto'
import { ParsedCrudQuery } from 'src/common/data-helper'
import {
  StoreCampaignCreateDto,
  StoreCampaignUpdateDto,
} from 'src/dto/campaign.dto'
import { ApiAuthGuard } from 'src/auth/api-auth-guard'

@UseGuards(ApiAuthGuard)
@Controller('api/intentnow')
export class StoreCampaignsController {
  constructor(private readonly campaignsService: StoreCampaignsService) {}

  @Get('stores/:storeId/campaigns')
  async getStoreCampaigns(
    @Param('storeId') storeId: string,
    @Query() q: CrudQueryDto, //Keep this for swagger,
    @ParsedCrudQuery({
      sortFields: ['createdAt', 'name', 'schedule.start', 'schedule.end'],
      filterFields: [],
    })
    query: CrudQuery
  ) {
    return await this.campaignsService.getStoreCampaigns(storeId, query)
  }

  @Get('stores/:storeId/campaigns/:campaignId')
  async getStoreCampaign(
    @Param('storeId') storeId: string,
    @Param('campaignId') campaignId: string
  ) {
    return await this.campaignsService.getStoreCampaign(storeId, campaignId)
  }

  @Post('stores/:storeId/campaigns')
  async createStoreCampaign(
    @Param('storeId') storeId: string,
    @Body() dto: StoreCampaignCreateDto
  ) {
    return await this.campaignsService.createStoreCampaign(storeId, dto)
  }

  @Patch('stores/:storeId/campaigns/:campaignId')
  async updateStoreCampaign(
    @Param('storeId') storeId: string,
    @Param('campaignId') campaignId: string,
    @Body() dto: StoreCampaignUpdateDto
  ) {
    return await this.campaignsService.updateStoreCampaign(
      storeId,
      campaignId,
      dto
    )
  }

  @Delete('stores/:storeId/campaigns/:campaignId')
  async deleteStoreCampaign(
    @Param('storeId') storeId: string,
    @Param('campaignId') campaignId: string
  ) {
    return await this.campaignsService.deleteStoreCampaign(storeId, campaignId)
  }

  @Get('stores/:storeId/active-campaign')
  async getActiveCampaign(
    @Param('storeId') storeId: string,
    @Query('skipCache', new ParseBoolPipe({ optional: true }))
    skipCache: boolean | undefined
  ) {
    return await this.campaignsService.getActiveCampaign(storeId, skipCache)
  }
}
