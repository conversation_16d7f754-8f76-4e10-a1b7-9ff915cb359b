import {
  BadRequestException,
  Inject,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { StoresService } from 'src/stores/stores.service'
import { StoreCampaign, StoreCampaignDocument } from './entities/campaign.mongo'
import { ClientSession, Connection, PaginateModel } from 'mongoose'
import { InjectConnection, InjectModel } from '@nestjs/mongoose'
import { UserPermissions } from 'src/users/user-permissions'
import { CrudQuery } from 'src/dto/common.dto'
import {
  buildMongoQuery,
  buildMongoUpdate,
  mapPaginatedMongoResultsToDto,
} from 'src/common/data-helper'
import {
  PaginatedStoreCampaignsDto,
  StoreCampaignCreateDto,
  StoreCampaignDto,
  StoreCampaignStatus,
  StoreCampaignUpdateDto,
} from 'src/dto/campaign.dto'
import { plainToInstance } from 'class-transformer'
import { Cache } from 'cache-manager'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { ServerConfig } from 'src/config/config'

@Injectable()
export class StoreCampaignsService {
  private readonly logger = new Logger(StoreCampaignsService.name)
  private readonly appName: string

  constructor(
    private readonly configService: ConfigService,
    private readonly userPermissions: UserPermissions,
    private readonly storeService: StoresService,
    @InjectConnection() private mongoConnection: Connection,
    @InjectModel(StoreCampaign.name)
    private readonly storeCampaignModel: PaginateModel<StoreCampaignDocument>,
    @Inject(CACHE_MANAGER) private readonly cacheManager: Cache
  ) {
    this.appName = configService.get<ServerConfig['app']>('app')!.name
  }

  async getStoreCampaign(
    storeId: string,
    campaignId: string
  ): Promise<StoreCampaignDto> {
    await this.userPermissions.requiresAccessOnStore(storeId)
    await this.storeService.getStore(storeId)

    const storeCampaign = await this.storeCampaignModel.findById(campaignId)
    if (!storeCampaign || storeCampaign.storeId !== storeId) {
      throw new NotFoundException('StoreCampaign not found')
    }

    const campaign = plainToInstance(StoreCampaignDto, storeCampaign.toObject())
    await this.decorateCampaignStatuses(storeId, [campaign])
    return campaign
  }

  async getStoreCampaigns(storeId: string, crudQuery: CrudQuery) {
    await this.userPermissions.requiresAccessOnStore(storeId)
    await this.storeService.getStore(storeId)

    const { filter, options } = buildMongoQuery(
      this.storeCampaignModel,
      crudQuery
    )
    const paginatedResults = await this.storeCampaignModel.paginate(
      {
        ...filter,
        storeId: {
          $eq: storeId,
        },
      },
      options
    )

    const campaigns = mapPaginatedMongoResultsToDto(
      paginatedResults,
      StoreCampaignDto,
      PaginatedStoreCampaignsDto
    )
    await this.decorateCampaignStatuses(storeId, campaigns.data)
    return campaigns
  }

  async createStoreCampaign(
    storeId: string,
    newStoreCampaign: StoreCampaignCreateDto
  ): Promise<StoreCampaignDto> {
    await this.userPermissions.requiresAccessOnStore(storeId)
    await this.storeService.getStore(storeId)

    const now = new Date()
    const created = await this.mongoConnection.transaction(async (session) => {
      await this.validateCampaign(
        storeId,
        {
          startDate: newStoreCampaign.startDate,
          endDate: newStoreCampaign.endDate,
          enabled: newStoreCampaign.enabled,
        },
        session
      )
      const store = new this.storeCampaignModel({
        storeId,
        createdAt: now,
        updatedAt: now,
        name: newStoreCampaign.name,
        startDate: newStoreCampaign.startDate,
        endDate: newStoreCampaign.endDate,
        launchRatio: newStoreCampaign.launchRatio,
        enabled: newStoreCampaign.enabled,
      } satisfies StoreCampaign)
      const created = await store.save({ session })
      return created
    })

    return plainToInstance(StoreCampaignDto, created.toObject())
  }

  async updateStoreCampaign(
    storeId: string,
    campaignId: string,
    updateDto: StoreCampaignUpdateDto
  ): Promise<StoreCampaignDto> {
    await this.userPermissions.requiresAccessOnStore(storeId)
    await this.storeService.getStore(storeId)

    const updateObj = buildMongoUpdate(
      updateDto,
      {},
      {
        storeId: {},
      }
    )

    const storeCampaign = await this.mongoConnection.transaction(
      async (session) => {
        const existingCampaign = await this.storeCampaignModel.findById(
          campaignId,
          null,
          { session }
        )
        if (!existingCampaign || existingCampaign.storeId !== storeId) {
          throw new NotFoundException('StoreCampaign not found')
        }
        await this.validateCampaign(
          storeId,
          {
            _id: campaignId,
            startDate: updateDto.startDate ?? existingCampaign.startDate,
            endDate: updateDto.endDate ?? existingCampaign.endDate,
            enabled: updateDto.enabled ?? existingCampaign.enabled,
          },
          session
        )

        const storeCampaign = await this.storeCampaignModel.findByIdAndUpdate(
          campaignId,
          {
            ...updateObj,
            updatedAt: new Date(),
          },
          {
            new: true,
            session,
          }
        )
        return storeCampaign
      }
    )

    if (!storeCampaign) {
      throw new NotFoundException('StoreCampaign not found')
    }
    return plainToInstance(StoreCampaignDto, storeCampaign.toObject())
  }

  async deleteStoreCampaign(storeId: string, campaignId: string) {
    await this.userPermissions.requiresAccessOnStore(storeId)

    //Validate storeId/campaignId
    await this.getStoreCampaign(storeId, campaignId)

    const deleted = await this.storeCampaignModel.findByIdAndDelete(campaignId)
    if (!deleted) {
      throw new NotFoundException('StoreCampaign not found')
    }
    return plainToInstance(StoreCampaignDto, deleted.toObject())
  }

  async getActiveCampaign(
    storeId: string,
    skipCache?: boolean
  ): Promise<StoreCampaignDto | undefined> {
    await this.userPermissions.requiresAccessOnStore(storeId)
    await this.storeService.getStore(storeId)

    const cacheKey = `intentnow-activeStoreCampaign:${this.appName}:${storeId}`
    if (!skipCache) {
      const cachedCampaign =
        await this.cacheManager.get<StoreCampaignDto>(cacheKey)
      if (cachedCampaign) {
        return cachedCampaign
      }
    }

    const now = new Date()
    const storeCampaign = (
      await this.storeCampaignModel.paginate(
        {
          storeId: {
            $eq: storeId,
          },
          enabled: {
            $eq: true,
          },
          startDate: { $lte: now },
          $or: [
            { endDate: { $eq: null } },
            { endDate: { $exists: false } },
            { endDate: { $gte: now } },
          ],
        },
        {
          //In case there may be more than one active campaign, return the latest one
          sort: { startDate: -1, createdAt: -1, updatedAt: -1 },
          limit: 1,
        }
      )
    )?.docs[0]
    if (!storeCampaign) {
      return undefined
    }
    const campaign = plainToInstance(StoreCampaignDto, storeCampaign.toObject())
    campaign.status = StoreCampaignStatus.active

    await this.cacheManager.set(cacheKey, campaign, 1000 * 60) //cache for 1 minute

    return campaign
  }

  async decorateCampaignStatuses(
    storeId: string,
    campaigns: StoreCampaignDto[]
  ) {
    const now = new Date()
    const activeCampaign = await this.getActiveCampaign(storeId, true)
    campaigns.forEach((campaign) => {
      if (campaign._id === activeCampaign?._id) {
        campaign.status = StoreCampaignStatus.active
      } else if (!campaign.enabled) {
        campaign.status = StoreCampaignStatus.disabled
      } else if (campaign.startDate > now) {
        campaign.status = StoreCampaignStatus.notStarted
      } else if (campaign.endDate && campaign.endDate < now) {
        campaign.status = StoreCampaignStatus.ended
      } else {
        //A unlikely case (due to rare edge condition)
        this.logger.error(
          {
            storeId,
            campaignId: campaign._id,
          },
          'decorateCampaignStatuses: unknown status'
        )
        campaign.status = StoreCampaignStatus.unknown
      }
    })
  }

  async validateCampaign(
    storeId: string,
    campaign: {
      _id?: string
      startDate: Date
      endDate?: Date
      enabled: boolean
    },
    session?: ClientSession | undefined
  ) {
    await this.userPermissions.requiresAccessOnStore(storeId)

    if (campaign.endDate && campaign.startDate > campaign.endDate) {
      throw new BadRequestException('invalid-start-end')
    }

    //Now check if there is conflicting campaigns
    if (!campaign.enabled) {
      return undefined
    }

    const periodFilters: any[] = []
    if (campaign.endDate) {
      periodFilters.push({
        startDate: { $gte: campaign.startDate, $lte: campaign.endDate },
      })
      periodFilters.push({
        endDate: { $gte: campaign.startDate, $lte: campaign.endDate },
      })
      periodFilters.push({
        startDate: { $lte: campaign.endDate },
        $or: [{ endDate: { $exists: false } }, { endDate: { $eq: null } }],
      })
    } else {
      periodFilters.push({
        endDate: { $exists: false },
      })
      periodFilters.push({
        endDate: { $eq: null },
      })
      periodFilters.push({
        endDate: { $gte: campaign.startDate },
      })
    }

    const conflictingCampaigns = await this.storeCampaignModel.paginate(
      {
        storeId: {
          $eq: storeId,
        },
        ...(campaign._id
          ? {
              _id: {
                $ne: campaign._id,
              },
            }
          : {}),
        enabled: {
          $eq: true,
        },
        $or: periodFilters,
      },
      {
        limit: 1,
        session,
      }
    )
    if (conflictingCampaigns.docs.length > 0) {
      throw new BadRequestException('conflicting-campaign')
    }
  }
}
