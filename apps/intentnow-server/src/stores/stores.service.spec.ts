import { Test, TestingModule } from '@nestjs/testing'
import { StoresService } from './stores.service'
import { MongoMemoryReplSet } from 'mongodb-memory-server'
import { MongoClient, ObjectId } from 'mongodb'
import { UserPermissions } from 'src/users/user-permissions'
import { MongooseModule } from '@nestjs/mongoose'
import {
  Store,
  StoreSchema,
  LinkStoreRequest,
  LinkStoreRequestSchema,
} from './entities/store.mongo'
import { UsersService } from 'src/users/users.service'
import { ApiAuthGuard } from 'src/auth/api-auth-guard'
import { AnalyticsService } from 'src/analytics/analytics.service'
import {
  UnauthorizedException,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { ConfigService } from '@nestjs/config'
import { StoreChartType, ChartPeriod } from 'src/dto/analytics.dto'

describe('StoresService', () => {
  let service: StoresService
  let mongoServer: MongoMemoryReplSet
  let mongoClient: MongoClient

  beforeAll(async () => {
    mongoServer = await MongoMemoryReplSet.create({ replSet: { count: 4 } })
    mongoClient = await MongoClient.connect(mongoServer.getUri(), {})
  }, 60000)

  beforeEach(async () => {
    const cacheManagerMock = {
      get: jest.fn(),
      set: jest.fn(),
      del: jest.fn(),
    }

    const userPermissionsMock = {
      requiresAccessOnStore: jest.fn(),
      requiresAdmin: jest.fn(),
      requiresAccessOnUser: jest.fn().mockResolvedValue({
        userId: 'test-user',
        roles: { admin: false, merchant: true },
      }),
      requiresAccessOnShopifyStore: jest.fn(),
      requiresMerchant: jest.fn().mockResolvedValue({
        userId: 'test-user',
        roles: { admin: false, merchant: true },
      }),
    }

    const usersServiceMock = {
      getUserStoreAccesses: jest.fn().mockResolvedValue({
        data: [],
        meta: { page: 1, pageSize: 10 },
      }),
      createUserStoreAccess: jest.fn(),
    }

    const apiAuthGuardMock = {
      useRobotUser: jest.fn(),
    }

    const analyticsServiceMock = {
      getAmplitudeFunnels: jest.fn(),
      getAmplitudeEventSegments: jest.fn(),
    }

    const module: TestingModule = await Test.createTestingModule({
      imports: [
        MongooseModule.forRoot(mongoServer.getUri()),
        MongooseModule.forFeature([
          {
            name: Store.name,
            collection: 'stores',
            schema: StoreSchema,
          },
          {
            name: LinkStoreRequest.name,
            collection: 'link_store_requests',
            schema: LinkStoreRequestSchema,
          },
        ]),
      ],
      providers: [
        StoresService,
        ConfigService,
        { provide: UserPermissions, useValue: userPermissionsMock },
        { provide: UsersService, useValue: usersServiceMock },
        { provide: ApiAuthGuard, useValue: apiAuthGuardMock },
        { provide: AnalyticsService, useValue: analyticsServiceMock },
        { provide: CACHE_MANAGER, useValue: cacheManagerMock },
      ],
    })
      .useMocker((token) => {
        if (token === 'CONFIGURATION_TOKEN') {
          return {
            app: {
              name: 'test-app',
            },
          }
        }
        return {}
      })
      .compile()

    service = module.get<StoresService>(StoresService)
  })

  afterEach(async () => {
    await mongoClient.db().collection('stores').deleteMany({})
    await mongoClient.db().collection('link_store_requests').deleteMany({})
  })

  afterAll(async (): Promise<any> => {
    if (mongoClient) {
      await mongoClient.close()
    }
    if (mongoServer) {
      await mongoServer.stop()
    }
  })

  describe('Store CRUD Operations', () => {
    it('should create a new store', async () => {
      const newStore = {
        name: 'Test Store',
        website: 'https://teststore.com',
        shopifyConfig: {
          myshopifyDomain: 'teststore.myshopify.com',
          appHandle: 'test-app',
        },
      }

      const created = await service.createStore(newStore)

      expect(created).toBeDefined()
      expect(created.name).toBe(newStore.name)
      expect(created.website).toBe(newStore.website)
      expect(created.shopifyConfig?.myshopifyDomain).toBe(
        newStore.shopifyConfig.myshopifyDomain
      )
      expect(created.createdAt).toBeDefined()
    })

    it('should get a store by id', async () => {
      // First create a store
      const newStore = {
        name: 'Test Store',
        website: 'https://teststore.com',
        shopifyConfig: {
          myshopifyDomain: 'teststore.myshopify.com',
        },
      }
      const created = await service.createStore(newStore)

      // Then get it
      const retrieved = await service.getStore(created._id)

      expect(retrieved).toBeDefined()
      expect(retrieved._id).toBe(created._id)
      expect(retrieved.name).toBe(newStore.name)
    })

    it('should throw NotFoundException when store not found', async () => {
      await expect(
        service.getStore('68550d636488aea5a98fa1f8')
      ).rejects.toThrow(NotFoundException)
    })

    it('should update a store', async () => {
      // First create a store
      const newStore = {
        name: 'Test Store',
        website: 'https://teststore.com',
      }
      const created = await service.createStore(newStore)

      // Then update it
      const updateData = {
        name: 'Updated Store Name',
        website: 'https://updatedstore.com',
      }
      const updated = await service.updateStore(created._id, updateData)

      expect(updated.name).toBe(updateData.name)
      expect(updated.website).toBe(updateData.website)
    })

    it('should throw NotFoundException when updating non-existent store', async () => {
      const updateData = { name: 'Updated Name' }
      await expect(
        service.updateStore('68550d636488aea5a98fa1f8', updateData)
      ).rejects.toThrow(NotFoundException)
    })
  })

  describe('Store Listing Operations', () => {
    it('should get paginated stores list', async () => {
      // Create test stores
      await service.createStore({
        name: 'Store 1',
        website: 'https://store1.com',
      })
      await service.createStore({
        name: 'Store 2',
        website: 'https://store2.com',
      })
      await service.createStore({
        name: 'Store 3',
        website: 'https://store3.com',
      })

      const result = await service.getStores({ limit: 2 })

      expect(result.data.length).toBe(2)
      expect(result.meta.total).toBe(3)
      expect(result.meta.page).toBe(1)
      expect(result.meta.pageCount).toBe(2)
    })

    it('should get none stores for user', async () => {
      const result = await service.getStoresForUser('test-user', {})

      expect(result).toBeDefined()
      expect(result.data).toBeDefined()
    })
  })

  describe('Link Store Request Operations', () => {
    it('should create a link store request', async () => {
      const linkRequest = {
        storeType: 'shopify' as const,
        storeRef: 'teststore.myshopify.com',
        source: 'test-app',
        storeName: 'Test Store',
        storeWebsite: 'https://teststore.com',
      }

      const created = await service.createLinkStoreRequest(linkRequest)

      expect(created).toBeDefined()
      expect(created.status).toBe('pending')
      expect(created.storeRef).toBe(linkRequest.storeRef)
      expect(created.storeName).toBe(linkRequest.storeName)
      expect(created.expiresAt).toBeDefined()
    })

    it('should throw BadRequestException for invalid store type', async () => {
      const linkRequest = {
        storeType: 'invalid' as any,
        storeRef: 'teststore.myshopify.com',
        source: 'test-app',
        storeName: 'Test Store',
        storeWebsite: 'https://teststore.com',
      }

      await expect(service.createLinkStoreRequest(linkRequest)).rejects.toThrow(
        BadRequestException
      )
    })

    it('should get a link store request', async () => {
      const linkRequest = {
        storeType: 'shopify' as const,
        storeRef: 'teststore.myshopify.com',
        source: 'test-app',
        storeName: 'Test Store',
        storeWebsite: 'https://teststore.com',
      }
      const created = await service.createLinkStoreRequest(linkRequest)

      const retrieved = await service.getLinkStoreRequest(created._id)

      expect(retrieved).toBeDefined()
      expect(retrieved._id).toBe(created._id)
      expect(retrieved.status).toBe('pending')
    })

    it('should throw NotFoundException for non-existent link request', async () => {
      await expect(
        service.getLinkStoreRequest('68550d636488aea5a98fa1f8')
      ).rejects.toThrow(NotFoundException)
    })

    it('should mark expired link request as expired', async () => {
      // Create a request that's already expired
      const linkRequest = {
        storeType: 'shopify' as const,
        storeRef: 'teststore.myshopify.com',
        source: 'test-app',
        storeName: 'Test Store',
        storeWebsite: 'https://teststore.com',
      }
      const created = await service.createLinkStoreRequest(linkRequest)

      // Manually update the expiry date to past
      await mongoClient
        .db()
        .collection('link_store_requests')
        .updateOne(
          { _id: new ObjectId(created._id) },
          { $set: { expiresAt: new Date(Date.now() - 1000 * 60 * 60) } }
        )

      await expect(service.getLinkStoreRequest(created._id)).rejects.toThrow(
        NotFoundException
      )
    })

    it('should commit a link store request', async () => {
      const linkRequest = {
        storeType: 'shopify' as const,
        storeRef: 'teststore.myshopify.com',
        source: 'test-app',
        storeName: 'Test Store',
        storeWebsite: 'https://teststore.com',
      }
      const created = await service.createLinkStoreRequest(linkRequest)

      const result = await service.commitLinkStoreRequest(created._id)

      expect(result).toBeDefined()
      expect(result.storeId).toBeDefined()
    })

    it('should throw NotFoundException when committing non-existent request', async () => {
      await expect(
        service.commitLinkStoreRequest('68550d636488aea5a98fa1f8')
      ).rejects.toThrow(NotFoundException)
    })
  })

  describe('Store Chart Operations', () => {
    it('should get store chart with cache', async () => {
      // Create a store first
      const store = await service.createStore({
        name: 'Test Store',
        website: 'https://teststore.com',
        shopifyConfig: {
          myshopifyDomain: 'teststore.myshopify.com',
        },
      })

      const cacheManager = service['cacheManager']
      const mockChart = { funnelChart: { chartName: 'Test Chart', groups: [] } }
      cacheManager.get = jest.fn().mockResolvedValue(mockChart)

      const result = await service.getStoreChart(
        store._id,
        StoreChartType.userConversion,
        {
          period: ChartPeriod.last7Days,
        }
      )

      expect(result).toEqual(mockChart)
      expect(cacheManager.get).toHaveBeenCalled()
    })

    it('should throw BadRequestException for invalid chart type', async () => {
      const store = await service.createStore({
        name: 'Test Store',
        website: 'https://teststore.com',
        shopifyConfig: {
          myshopifyDomain: 'teststore.myshopify.com',
        },
      })

      await expect(
        service.getStoreChart(store._id, 'invalid' as any, {
          period: ChartPeriod.last7Days,
        })
      ).rejects.toThrow(BadRequestException)
    })
  })
})

describe('StoresService Access Control', () => {
  let service: StoresService

  beforeEach(async () => {
    const cacheManagerMock = {
      get: jest.fn(),
      set: jest.fn(),
      del: jest.fn(),
    }

    const userPermissionsMock = {
      requiresAccessOnStore: () => {
        throw new UnauthorizedException('Unauthorized')
      },
      requiresAdmin: () => {
        throw new UnauthorizedException('Unauthorized')
      },
      requiresAccessOnUser: () => {
        throw new UnauthorizedException('Unauthorized')
      },
      requiresAccessOnShopifyStore: () => {
        throw new UnauthorizedException('Unauthorized')
      },
      requiresMerchant: () => {
        throw new UnauthorizedException('Unauthorized')
      },
    }

    const usersServiceMock = {
      getUserStoreAccesses: jest.fn(),
      createUserStoreAccess: jest.fn(),
    }

    const apiAuthGuardMock = {
      useRobotUser: jest.fn(),
    }

    const analyticsServiceMock = {
      getAmplitudeFunnels: jest.fn(),
      getAmplitudeEventSegments: jest.fn(),
    }

    const module: TestingModule = await Test.createTestingModule({
      imports: [],
      providers: [
        StoresService,
        ConfigService,
        { provide: UserPermissions, useValue: userPermissionsMock },
        { provide: UsersService, useValue: usersServiceMock },
        { provide: ApiAuthGuard, useValue: apiAuthGuardMock },
        { provide: AnalyticsService, useValue: analyticsServiceMock },
        { provide: CACHE_MANAGER, useValue: cacheManagerMock },
      ],
    })
      .useMocker((token) => {
        if (token === 'CONFIGURATION_TOKEN') {
          return {
            app: {
              name: 'test-app',
            },
          }
        }
        return {}
      })
      .compile()

    service = module.get<StoresService>(StoresService)
  })

  it('should throw unauthorized exception for getStore', async () => {
    await expect(service.getStore('test-store-id')).rejects.toThrow(
      'Unauthorized'
    )
  })

  it('should throw unauthorized exception for getStores', async () => {
    await expect(service.getStores({})).rejects.toThrow('Unauthorized')
  })

  it('should throw unauthorized exception for updateStore', async () => {
    await expect(
      service.updateStore('test-store-id', { name: 'Updated Store' })
    ).rejects.toThrow('Unauthorized')
  })

  it('should throw unauthorized exception for createStore', async () => {
    await expect(
      service.createStore({
        name: 'Test Store',
        website: 'https://teststore.com',
      })
    ).rejects.toThrow('Unauthorized')
  })

  it('should throw unauthorized exception for getStoresForUser', async () => {
    await expect(service.getStoresForUser('test-user', {})).rejects.toThrow(
      'Unauthorized'
    )
  })

  it('should throw unauthorized exception for createLinkStoreRequest', async () => {
    await expect(
      service.createLinkStoreRequest({
        storeType: 'shopify',
        storeRef: 'teststore.myshopify.com',
        source: 'test-app',
        storeName: 'Test Store',
        storeWebsite: 'https://teststore.com',
      })
    ).rejects.toThrow('Unauthorized')
  })

  it('should throw unauthorized exception for getLinkStoreRequest', async () => {
    await expect(
      service.getLinkStoreRequest('test-request-id')
    ).rejects.toThrow('Unauthorized')
  })

  it('should throw unauthorized exception for commitLinkStoreRequest', async () => {
    await expect(
      service.commitLinkStoreRequest('test-request-id')
    ).rejects.toThrow('Unauthorized')
  })

  it('should throw unauthorized exception for getStoreChart', async () => {
    await expect(
      service.getStoreChart('test-store-id', StoreChartType.userConversion, {
        period: ChartPeriod.last7Days,
      })
    ).rejects.toThrow('Unauthorized')
  })
})
