import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import {
  ShopifyRequestContext,
  UserContext,
} from 'src/auth/entities/user-context'
import { ServerConfig } from 'src/config/config'
import {
  ShopifyStoreIntegrationDto,
  ShopifyStoreIntegrationUpdateDto,
} from 'src/dto/store.dto'
import { ShopifyRestOp, ShopifyService } from 'src/shopify/shopify.service'
import {
  DeleteWebPixelMutation,
  DeleteWebPixelMutationVariables,
  EnableWebPixelMutation,
  EnableWebPixelMutationVariables,
  GetIntentnowMetafieldsQuery,
  GetIntentnowMetafieldsQueryVariables,
  GetWebPixelQuery,
  GetWebPixelQueryVariables,
  UpdateWebPixelMutation,
  UpdateWebPixelMutationVariables,
} from 'src/types/shopify-api/admin.generated'
import { UserPermissions } from 'src/users/user-permissions'
import { StoresService } from './stores.service'
import { plainToInstance } from 'class-transformer'
import { ApiAuthGuard } from 'src/auth/api-auth-guard'
import { FilterOp } from 'src/dto/common.dto'

@Injectable()
export class ShopifyStoreService {
  private readonly logger = new Logger(ShopifyStoreService.name)
  private readonly intentowConfig: ServerConfig['intentnow']

  constructor(
    private readonly configService: ConfigService,
    private readonly userPermissions: UserPermissions,
    private readonly apiAuthGuard: ApiAuthGuard,
    private readonly storesService: StoresService,
    private shopifyService: ShopifyService
  ) {
    this.intentowConfig =
      this.configService.get<ServerConfig['intentnow']>('intentnow')!
  }

  async getShopifyIntegration(
    storeId: string
  ): Promise<ShopifyStoreIntegrationDto> {
    await this.userPermissions.requiresAccessOnStore(storeId)
    const store = await this.storesService.getStore(storeId)

    if (
      !store.shopifyConfig?.myshopifyDomain ||
      !store.shopifyConfig.appHandle
    ) {
      return {
        linked: false,
      }
    }

    try {
      const appHandle = this.shopifyService.validateAppHandle(
        store.shopifyConfig.appHandle
      )
      const userContext = this.userPermissions.grantAccessOnShopifyStore(
        store.shopifyConfig.myshopifyDomain,
        appHandle
      )
      const shopInfo = await this.shopifyService.getShopInfo(userContext)
      const { webPixel, promoEmbedActivated, promoEmbedActivationLink } =
        await this.getAppSettings(userContext, false)

      const integration: ShopifyStoreIntegrationDto = {
        linked: true,
        shopifyExtensions: {
          webPixelOn: !!webPixel,
          promoWidgetOn: promoEmbedActivated,
          promoWidgetToggleLink: promoEmbedActivationLink,
        },
        ...(shopInfo && {
          shopifyStore: {
            name: shopInfo.name,
            myshopifyDomain: shopInfo.myshopifyDomain,
            appHandle: appHandle,
            url: shopInfo.url,
            shopifyPlus: shopInfo.plan.shopifyPlus,
            currencyCode: shopInfo.currencyCode,
          },
        }),
      }

      return plainToInstance(ShopifyStoreIntegrationDto, integration)
    } catch (e) {
      this.logger.error(
        e,
        {
          storeId,
          shopifyConfig: store.shopifyConfig,
        },
        `getShopifyIntegration: failed`
      )
      return {
        linked: false,
      }
    }
  }

  async updateShopifyIntegration(
    storeId: string,
    dto: ShopifyStoreIntegrationUpdateDto
  ): Promise<ShopifyStoreIntegrationDto> {
    const userContext =
      await this.userPermissions.requiresAccessOnStore(storeId)
    let integration = await this.getShopifyIntegration(storeId)

    if (!integration.linked) {
      throw new BadRequestException('Shopify store not linked')
    }

    if (
      dto['shopifyExtensions.webPixelOn'] === undefined ||
      dto['shopifyExtensions.webPixelOn'] === null
    ) {
      //No-op
      return integration
    }

    if (
      dto['shopifyExtensions.webPixelOn'] ===
      integration.shopifyExtensions?.webPixelOn
    ) {
      //No-op
      return integration
    }

    if (dto['shopifyExtensions.webPixelOn']) {
      await this.createWebPixel(userContext)
    } else {
      await this.deleteWebPixel(userContext)
    }

    integration = await this.getShopifyIntegration(storeId)
    return plainToInstance(ShopifyStoreIntegrationDto, integration)
  }

  async getWebPixel(requester: UserContext | undefined) {
    this.shopifyService.validateRequester(requester)
    try {
      const query = `#graphql
          query getWebPixel {
            webPixel {
              id
              settings
            }
          }`

      const response = await this.shopifyService.executeGraphQl<
        GetWebPixelQuery,
        GetWebPixelQueryVariables
      >(requester, { query }, true)

      return response.data?.webPixel
    } catch (e) {
      //this.logger.error(e, 'getWebPixel: failed to get web pixel')
      return undefined
    }
  }

  async deleteWebPixel(requester: UserContext | undefined) {
    this.shopifyService.validateRequester(requester)
    const id = (await this.getWebPixel(requester))?.id
    if (!id) {
      throw new NotFoundException('Web pixel not found')
    }

    this.logger.log(`deleteWebPixel: started`)

    const query = `#graphql
        mutation deleteWebPixel($id: ID!) {
          webPixelDelete(id: $id) {
            deletedWebPixelId
            userErrors {
              code
              field
              message
            }
          }
        }`
    const variables = {
      id,
    }

    const response = await this.shopifyService.executeGraphQl<
      DeleteWebPixelMutation,
      DeleteWebPixelMutationVariables
    >(requester, { query, variables })

    if (!response.data?.webPixelDelete?.deletedWebPixelId) {
      throw new Error(`Failed to delete web pixel`)
    }

    return {
      deletedWebPixelId: response.data.webPixelDelete.deletedWebPixelId,
    }
  }

  getWebPixelInput(request: ShopifyRequestContext) {
    const shop = request.shop!
    return {
      settings: JSON.stringify({
        shop,
        eventApi: this.intentowConfig.eventApi.url,
        eventApiToken: this.intentowConfig.eventApi.token,
        eventSource: this.shopifyService.getShopifyAppHandle(request.appHandle),
      }),
    }
  }

  async createWebPixel(requester: UserContext | undefined) {
    const request = this.shopifyService.validateRequester(requester)

    this.logger.log(`deleteWebPixel: started`)

    const query = `#graphql
        mutation enableWebPixel($input: WebPixelInput!) {
          webPixelCreate(webPixel: $input) {
            userErrors {
              code
              field
              message
            }
            webPixel {
              settings
              id
            }
          }
        }`

    const variables = {
      input: this.getWebPixelInput(request),
    }

    const response = await this.shopifyService.executeGraphQl<
      EnableWebPixelMutation,
      EnableWebPixelMutationVariables
    >(requester, {
      query,
      variables,
    })

    if (response.data?.webPixelCreate?.userErrors.length) {
      throw new Error(`Failed to create web pixel due to invalid input`)
    }
    if (!response.data?.webPixelCreate?.webPixel) {
      throw new Error(`No web pixel created`)
    }

    return {
      webPixel: {
        id: response.data?.webPixelCreate?.webPixel.id,
        settings: JSON.parse(response.data?.webPixelCreate?.webPixel.settings),
      },
    }
  }

  async updateWebPixel(requester: UserContext | undefined, id: string) {
    const request = this.shopifyService.validateRequester(requester)
    this.logger.log({ id }, `updateWebPixel: started`)

    const query = `#graphql
        mutation updateWebPixel($id: ID!, $input: WebPixelInput!) {
          webPixelUpdate(id: $id, webPixel: $input) {
            userErrors {
              code
              field
              message
            }
            webPixel {
              id
              settings
            }
          }
        }`

    const variables = {
      id,
      input: this.getWebPixelInput(request),
    }

    const response = await this.shopifyService.executeGraphQl<
      UpdateWebPixelMutation,
      UpdateWebPixelMutationVariables
    >(requester, {
      query,
      variables,
    })

    if (response.data?.webPixelUpdate?.userErrors.length) {
      throw new Error(`Failed to update web pixel due to invalid input`)
    }

    return response?.data
  }

  async getAppMetafields(requester: UserContext | undefined) {
    this.shopifyService.validateRequester(requester)

    const appInfo = await this.shopifyService.getAppInstallation(requester)
    if (!appInfo) {
      throw new NotFoundException('App info not found')
    }

    const query = `#graphql
        query getIntentnowMetafields($appInstId:ID!) {
          appInstallation(id:$appInstId) {
            metafields(first: 250, namespace: "intentnow") {
              nodes {
                id
                namespace
                key
                value
              }
            }
          }
        }`

    const variables = {
      appInstId: appInfo.id,
    }

    const response = await this.shopifyService.executeGraphQl<
      GetIntentnowMetafieldsQuery,
      GetIntentnowMetafieldsQueryVariables
    >(requester, {
      query,
      variables,
    })

    return response.data?.appInstallation?.metafields.nodes ?? []
  }

  async setupAppMetafields(requester: UserContext | undefined) {
    //Set up app proxy subpath for the promo widget
    //This subpath needs to be unique for each different app instance, so we simply use the app handle as the subpath.
    //The subpath is saved into the app metafield so the frontend (Liquid template) can retrieve it easily.
    const request = this.shopifyService.validateRequester(requester)

    const appInfo = await this.shopifyService.getAppInstallation(requester)
    if (!appInfo) {
      throw new NotFoundException('App info not found')
    }

    const metafields = [
      {
        namespace: 'intentnow',
        key: 'appProxySubpath',
        ownerId: appInfo.id,
        value: `${appInfo.app.handle}`,
        type: 'single_line_text_field',
      },
      {
        namespace: 'intentnow',
        key: 'eventApiUrl',
        ownerId: appInfo.id,
        value: `${this.intentowConfig.eventApi.url}`,
        type: 'single_line_text_field',
      },
      {
        namespace: 'intentnow',
        key: 'eventApiToken',
        ownerId: appInfo.id,
        value: `${this.intentowConfig.eventApi.token}`,
        type: 'single_line_text_field',
      },
      // {
      //   namespace: 'intentnow',
      //   key: 'amplitudeKey',
      //   ownerId: appInfo.id,
      //   value: `${this.amplitudeConfig.apiKey}`,
      //   type: 'single_line_text_field',
      // },
    ]
    this.logger.log(
      { shop: request.shop, metafields },
      `setupAppProxySubpath: metafields`
    )

    await this.shopifyService.setMetafield(requester, metafields)
    return {}
  }

  async getAppSettings(
    requester: UserContext | undefined,
    updateToLatest: boolean
  ) {
    const request = this.shopifyService.validateRequester(requester)
    const shop = request.shop
    this.logger.log({ shop }, `syncAppSettings: started`)

    try {
      const appHandle = this.shopifyService.getShopifyAppHandle(
        request.appHandle
      )

      const promises: [
        ReturnType<typeof this.getAppMetafieldsSettings>,
        ReturnType<typeof this.getWebPixelSettings>,
        ReturnType<typeof this.getPromoEmbedSettings>,
        ReturnType<typeof this.getMerchantPortalState>,
      ] = [
        this.getAppMetafieldsSettings(requester, updateToLatest),
        this.getWebPixelSettings(requester, updateToLatest),
        this.getPromoEmbedSettings(requester),
        this.getMerchantPortalState(requester),
      ]
      const [
        metafields,
        { webPixel },
        { promoEmbedActivated, promoEmbedActivationLink },
        { merchantPortal },
      ] = await Promise.all(promises)

      return {
        appHandle,
        webPixel,
        promoEmbedActivationLink,
        promoEmbedActivated,
        metafields,
        merchantPortal,
      }
    } catch (e) {
      this.logger.error(
        e,
        {
          shop,
        },
        `syncAppSettings: failed`
      )
      throw new InternalServerErrorException('Failed to sync app settings')
    }
  }

  async getAppMetafieldsSettings(
    requester: UserContext | undefined,
    updateToLatest = false
  ) {
    if (updateToLatest) {
      //Set up metafields for promo widget
      await this.setupAppMetafields(requester)
    }
    const metafields = await this.getAppMetafields(requester)
    return metafields
  }

  async getWebPixelSettings(
    requester: UserContext | undefined,
    updateToLatest = false
  ) {
    this.shopifyService.validateRequester(requester)
    let webPixel = await this.getWebPixel(requester)

    //Check if webPixel settings are up to date
    if (updateToLatest && webPixel) {
      if (
        webPixel.settings?.eventApi !== this.intentowConfig.eventApi.url ||
        webPixel.settings?.eventApiToken !== this.intentowConfig.eventApi.token
      ) {
        //Update the settings if they are stale
        await this.updateWebPixel(requester, webPixel.id)

        //Re-fetch the webPixel
        webPixel = await this.getWebPixel(requester)
      }
    }

    return {
      webPixel,
    }
  }

  async getPromoEmbedSettings(requester: UserContext | undefined) {
    const request = this.shopifyService.validateRequester(requester)
    const appHandle = this.shopifyService.getShopifyAppHandle(request.appHandle)
    const appHandle2 = this.shopifyService.getShopifyAppHandle2(
      request.appHandle
    )
    const promoEmbedId = this.shopifyService.getPromoEmbedId(request.appHandle)
    let promoEmbedActivationLink: string | undefined
    let promoEmbedActivated = false
    if (promoEmbedId) {
      promoEmbedActivationLink = `https://${request.shop}/admin/themes/current/editor?context=apps&activateAppId=${promoEmbedId}/intentnow-embed`

      const theme = await this.shopifyService.getCurrentTheme(requester)
      if (theme) {
        const themeSettingsData = await this.shopifyService.executeRest<{
          asset: {
            key: string
            value: string
            content_type: string
            theme_id: string
            size: number
          }
        }>(requester, {
          op: ShopifyRestOp.get,
          path: `/themes/${theme.id}/assets.json?asset[key]=config/settings_data.json`,
        })
        const themeSettings = JSON.parse(themeSettingsData.data.asset.value)
        const matchAppEmbedBlocks = [
          `shopify://apps/${appHandle}/blocks/intentnow-embed/${promoEmbedId}`,
        ]
        if (appHandle2) {
          //For some reason, Shopify may not use the standard app handle for the appembed block string
          matchAppEmbedBlocks.push(
            `shopify://apps/${appHandle2}/blocks/intentnow-embed/${promoEmbedId}`
          )
        }
        for (const block of Object.values(
          themeSettings.current?.blocks ?? {}
        ) as any) {
          if (
            block.disabled === false &&
            matchAppEmbedBlocks.includes(block.type)
          ) {
            promoEmbedActivated = true
            break
          }
        }
      }
    }

    return {
      promoEmbedActivated,
      promoEmbedActivationLink,
    }
  }

  async getMerchantPortalState(requester: UserContext | undefined) {
    const request = this.shopifyService.validateRequester(requester)
    const shop = request.shop!
    this.logger.log({ shop }, `getMerchantPortalState: started`)

    //We don't have a proper user context to read stores information, use a robot user with admin access
    const { data: stores } = await this.apiAuthGuard.callWithUser(
      this.apiAuthGuard.adminRobotUser(),
      async () => {
        return await this.storesService.getStores({
          limit: 1,
          filters: [
            {
              field: 'shopifyConfig.myshopifyDomain',
              op: FilterOp.eq,
              value: shop,
            },
          ],
        })
      }
    )

    return {
      merchantPortal: {
        linkedStoreId: stores.length ? stores[0]._id : undefined,
      },
    }
  }

  async createLinkStoreRequest(requester: UserContext | undefined) {
    const request = this.shopifyService.validateRequester(requester)
    const shop = request.shop
    this.logger.log({ shop }, `createLinkStoreRequest: started`)

    const shopInfo = await this.shopifyService.getShopInfo(requester)

    if (!shopInfo) {
      throw new NotFoundException('Shop info not found')
    }

    return await this.storesService.createLinkStoreRequest({
      storeType: 'shopify',
      storeRef: shop,
      source: request.appHandle,
      storeName: shopInfo.name,
      storeWebsite: shopInfo.url,
    })
  }
}
