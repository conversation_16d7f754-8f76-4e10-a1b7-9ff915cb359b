import {
  BadRequestException,
  Inject,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import {
  LinkStoreRequest,
  LinkStoreRequestDocument,
  Store,
  StoreDocument,
} from './entities/store.mongo'
import {
  StoreLaunchConfig,
  StoreLaunchConfigDocument,
} from './entities/launch-config.mongo'
import { PaginateModel } from 'mongoose'
import {
  CommitLinkStoreRequestDto,
  LinkStoreRequestDto,
  PaginatedStoreImagesDto,
  PaginatedStoresDto,
  StoreAddUserDto,
  StoreCreateDto,
  StoreDto,
  MerchantPortalFeaturesDto,
  StoreImageCreateDto,
  StoreImageDto,
  StoreUpdateDto,
  StoreLaunchConfigDto,
  StoreLaunchConfigCreateDto,
  PaginatedStoreLaunchConfigsDto,
  ModelConfigV1Dto,
} from '../dto/store.dto'
import { plainToInstance } from 'class-transformer'
import {
  buildMongoQuery,
  buildMongoUpdate,
  mapPaginatedMongoResultsToDto,
} from 'src/common/data-helper'
import { UserPermissions } from 'src/users/user-permissions'
import { UsersService } from 'src/users/users.service'
import { CrudQuery } from 'src/dto/common.dto'
import { ApiAuthGuard } from 'src/auth/api-auth-guard'
import {
  ChartPeriod,
  ChartQueryDto,
  AggregatedEventChartDto,
  FunnelChartDto,
  ChartDto,
  StoreChartType,
} from 'src/dto/analytics.dto'
import { AnalyticsService } from 'src/analytics/analytics.service'
import {
  AmplitudeEventSegmentsQuery,
  AmplitudeFunnelsQuery,
  AmplitudeInterval,
  AmplitudeMetric,
  AmplitudePropOp,
  AmplitudePropType,
} from 'src/analytics/entities/amplitude-api'
import { Cache } from 'cache-manager'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { ServerConfig } from 'src/config/config'
import { ConfigService } from '@nestjs/config'
import { StorageService } from '@codebrew/nestjs-storage'
import { StoreImage, StoreImageDocument } from './entities/image.mongo'
import * as path from 'path'
import { Readable } from 'stream'
import { randomHash } from 'src/common/utils'
import * as sharp from 'sharp'
import { defaultStoreConfig, StoreConfigDto } from '@packages/shared-entities'
import * as _ from 'lodash'

@Injectable()
export class StoresService {
  private readonly logger = new Logger(StoresService.name)
  private readonly intentowConfig: ServerConfig['intentnow']
  private readonly appName: string

  constructor(
    private readonly configService: ConfigService,
    @InjectModel(Store.name)
    private readonly storeModel: PaginateModel<StoreDocument>,
    @InjectModel(StoreImage.name)
    private readonly storeImageModel: PaginateModel<StoreImageDocument>,
    @InjectModel(StoreLaunchConfig.name)
    private readonly storeLaunchConfigModel: PaginateModel<StoreLaunchConfigDocument>,
    @InjectModel(LinkStoreRequest.name)
    private readonly linkStoreRequestModel: PaginateModel<LinkStoreRequestDocument>,
    private readonly userPermissions: UserPermissions,
    private readonly usersService: UsersService,
    private readonly apiAuthGuard: ApiAuthGuard,
    private readonly analyticsService: AnalyticsService,
    private readonly storageService: StorageService,
    @Inject(CACHE_MANAGER) private readonly cacheManager: Cache
  ) {
    this.intentowConfig =
      configService.get<ServerConfig['intentnow']>('intentnow')!
    this.appName = configService.get<ServerConfig['app']>('app')!.name
  }

  async getStore(storeId: string): Promise<StoreDto> {
    await this.userPermissions.requiresAccessOnStore(storeId)

    const store = await this.storeModel.findById(storeId)
    if (!store) {
      throw new NotFoundException('Store not found')
    }
    const storeObj = store.toObject()
    const merchantPortalFeatures = await this.getMerchantPortalFeatures(store)
    return plainToInstance(StoreDto, {
      ...storeObj,
      merchantPortalFeatures,
      effectiveConfig: {
        receiveEvents: _.isNil(storeObj.config?.receiveEvents)
          ? defaultStoreConfig.receiveEvents
          : storeObj.config?.receiveEvents,
        sendAmplitude: _.isNil(storeObj.config?.sendAmplitude)
          ? defaultStoreConfig.sendAmplitude
          : storeObj.config?.sendAmplitude,
        showOffers: _.isNil(storeObj.config?.showOffers)
          ? defaultStoreConfig.showOffers
          : storeObj.config?.showOffers,
        predictOffers: _.isNil(storeObj.config?.predictOffers)
          ? defaultStoreConfig.predictOffers
          : storeObj.config?.predictOffers,
        isLegacy: _.isNil(storeObj.config?.isLegacy)
          ? defaultStoreConfig.isLegacy
          : storeObj.config?.isLegacy,
      },
    })
  }

  async getMerchantPortalFeatures(
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    store: StoreDocument
  ): Promise<MerchantPortalFeaturesDto> {
    const user = this.userPermissions.requiresUser()
    if (user.roles?.admin) {
      return {
        fullOfferEditor: true,
        internalDiscountConfigSection: true,
      }
    } else {
      return {
        fullOfferEditor: false,
        internalDiscountConfigSection: false,
      }
    }
  }

  async getStores(crudQuery: CrudQuery): Promise<PaginatedStoresDto> {
    this.userPermissions.requiresAdmin()

    const { filter, options } = buildMongoQuery(this.storeModel, crudQuery)
    const paginatedResults = await this.storeModel.paginate(filter, options)

    return mapPaginatedMongoResultsToDto(
      paginatedResults,
      StoreDto,
      PaginatedStoresDto
    )
  }

  async updateStore(
    storeId: string,
    updateDto: StoreUpdateDto
  ): Promise<StoreDto> {
    //TODO: there are critical information in the store that should not be updated by the merchant. For now, we will not allow merchant to update the store. In the future we may want to whitelist some safe fields to be updated by the merchant as needed.
    this.userPermissions.requiresAdmin()

    this.validateStoreConfig(updateDto.config)

    const updateObj = buildMongoUpdate(updateDto, {
      shopifyConfig: {},
    })

    const store = await this.storeModel.findByIdAndUpdate(
      storeId,
      {
        ...updateObj,
        updatedAt: new Date(),
      },
      {
        new: true,
      }
    )
    if (!store) {
      throw new NotFoundException('Store not found')
    }
    return plainToInstance(StoreDto, store.toObject())
  }

  async createStore(newStore: StoreCreateDto): Promise<StoreDto> {
    this.userPermissions.requiresAdmin()

    this.validateStoreConfig(newStore.config)

    const now = new Date()
    const store = new this.storeModel({
      createdAt: now,
      updatedAt: now,
      name: newStore.name,
      website: newStore.website,
      shopifyConfig: newStore.shopifyConfig,
    } satisfies Store)
    const created = await store.save()
    return plainToInstance(StoreDto, created.toObject())
  }

  validateStoreConfig(config?: StoreConfigDto) {
    if (config) {
      //validate config
      if (config.showOffers && config.predictOffers) {
        throw new BadRequestException(
          'showOffers and  predictOffers cannot be enabled at the same time'
        )
      }
    }
  }

  async getStoresForUser(
    userId: string,
    crudQuery: CrudQuery
  ): Promise<PaginatedStoresDto> {
    const user = await this.userPermissions.requiresAccessOnUser(userId)

    //TODO: This is a temporary hack to allow admin users to see all stores for testing purpose during the early development phase of the Merchant Portal. We will want to remove this later.
    if (user.roles?.admin && user.userId === userId) {
      return await this.getStores(crudQuery)
    }

    const storeAccesses = await this.usersService.getUserStoreAccessesByUser(
      userId,
      crudQuery
    )
    const storeIds = storeAccesses.data.map((x) => x.storeId)

    const paginatedResults = await this.storeModel.paginate(
      {
        _id: { $in: storeIds },
      },
      {
        page: storeAccesses.meta.page,
        limit: storeAccesses.meta.pageSize,
      }
    )

    return mapPaginatedMongoResultsToDto(
      paginatedResults,
      StoreDto,
      PaginatedStoresDto
    )
  }

  async getStoreUsers(storeId: string, crudQuery: CrudQuery) {
    await this.userPermissions.requiresAccessOnStore(storeId)
    await this.getStore(storeId)

    return await this.usersService.getUsersByStore(storeId, crudQuery)
  }

  async addUserToStore(storeId: string, dto: StoreAddUserDto) {
    const userContext =
      await this.userPermissions.requiresAccessOnStore(storeId)

    //Need admin access for user operations
    return await this.apiAuthGuard.callWithUser(
      this.apiAuthGuard.adminRobotUser(),
      async () => {
        return await this.usersService.createUserStoreAccessByEmail(dto.email, {
          storeId,
          grantedBy: userContext.userId,
        })
      }
    )
  }

  async deleteUserFromStore(storeId: string, userId: string) {
    const userContext =
      await this.userPermissions.requiresAccessOnStore(storeId)

    if (userId === userContext.userId) {
      throw new BadRequestException("Can't remove yourself")
    }

    //Need admin access for user operations
    return await this.apiAuthGuard.callWithUser(
      this.apiAuthGuard.adminRobotUser(),
      async () => {
        return await this.usersService.deleteUserStoreAccessByStoreId(
          userId,
          storeId
        )
      }
    )
  }

  async createLinkStoreRequest(linkeStoreRequest: {
    storeType: 'shopify'
    storeRef: string
    source: string
    storeName: string
    storeWebsite: string
  }) {
    if (linkeStoreRequest.storeType !== 'shopify') {
      throw new BadRequestException('Invalid store type')
    }

    await this.userPermissions.requiresAccessOnShopifyStore(
      linkeStoreRequest.storeRef
    )

    const now = new Date()
    const creatRequest = new this.linkStoreRequestModel({
      status: 'pending',
      storeType: linkeStoreRequest.storeType,
      storeRef: linkeStoreRequest.storeRef,
      storeName: linkeStoreRequest.storeName,
      storeWebsite: linkeStoreRequest.storeWebsite,
      source: linkeStoreRequest.source,
      createdAt: now,
      updatedAt: now,
      expiresAt: new Date(now.getTime() + 1000 * 60 * 60), //1 hour
    } satisfies LinkStoreRequest)
    const created = await creatRequest.save()

    return plainToInstance(LinkStoreRequestDto, created.toObject())
  }

  async getLinkStoreRequest(requestId: string): Promise<LinkStoreRequestDto> {
    const user = this.userPermissions.requiresMerchant()

    let request = await this.linkStoreRequestModel.findById(requestId)
    const now = new Date()
    if (request?.status === 'pending' && request.expiresAt < now) {
      request = await this.linkStoreRequestModel.findByIdAndUpdate(
        requestId,
        {
          $set: {
            status: 'expired',
            updatedAt: now,
          },
        },
        { new: true }
      )
    }

    if (!request) {
      throw new NotFoundException('Request not found')
    }

    if (!user.roles?.admin) {
      //For none-admin users, only return the request if it is still pending
      if (request.status !== 'pending') {
        throw new NotFoundException('Request not found')
      }
    }

    return plainToInstance(LinkStoreRequestDto, request.toObject())
  }

  async commitLinkStoreRequest(
    requestId: string
  ): Promise<CommitLinkStoreRequestDto> {
    const user = this.userPermissions.requiresMerchant()

    const request = await this.getLinkStoreRequest(requestId)
    if (!request || request.status !== 'pending') {
      throw new NotFoundException('Request not found')
    }

    const now = new Date()
    try {
      //We need more permission to finish commiting the request
      return await this.apiAuthGuard.callWithUser(
        this.apiAuthGuard.adminRobotUser(),
        async () => {
          const store = await this.storeModel.findOneAndUpdate(
            {
              'shopifyConfig.myshopifyDomain': request.storeRef,
            },
            {
              $setOnInsert: {
                createdAt: now,
                updatedAt: now,
                name: request.storeName,
                website: request.storeWebsite,
                shopifyConfig: {
                  myshopifyDomain: request.storeRef,
                  appHandle: request.source,
                },
              },
            },
            { upsert: true, new: true }
          )

          await this.usersService.createUserStoreAccess(user.userId, {
            storeId: store._id.toString(),
            grantedBy: user.userId,
          })

          await this.linkStoreRequestModel.findByIdAndUpdate(
            requestId,
            {
              $set: {
                status: 'completed',
                updatedAt: now,
              },
            },
            { new: true }
          )

          return {
            storeId: store._id.toString(),
          }
        }
      )
    } catch (e) {
      this.logger.error(
        e,
        {
          requestId,
        },
        `commitLinkStoreRequest: failed to commit link store request`
      )
      try {
        await this.linkStoreRequestModel.findByIdAndUpdate(
          requestId,
          {
            $set: {
              status: 'failed',
              updatedAt: now,
            },
          },
          { new: true }
        )
      } catch (e2) {
        this.logger.error(
          e2,
          {
            requestId,
          },
          `commitLinkStoreRequest: failed to update request status during failed commit`
        )
      }
      throw new InternalServerErrorException(
        'Failed to commit link store request'
      )
    }
  }

  async getStoreChart(
    storeId: string,
    chartType: StoreChartType,
    query: ChartQueryDto
  ) {
    await this.userPermissions.requiresAccessOnStore(storeId)
    const cacheKey = `intentnow-storeChart:${this.appName}:${storeId}:${chartType}:${query.period}`

    if (!query.skipCache) {
      const cachedChart = await this.cacheManager.get<ChartDto>(cacheKey)
      if (cachedChart) {
        return cachedChart
      }
    }

    let chart: ChartDto
    switch (chartType) {
      case StoreChartType.userConversion:
        chart = await this.getStoreUserConverstionChart(
          storeId,
          query,
          'User Conversions'
        )
        break
      case StoreChartType.otfUserConversion:
        chart = await this.getStoreUserConverstionChart(
          storeId,
          query,
          'Promo Target Group Conversions',
          true
        )
        break
      case StoreChartType.userRevenue:
        chart = await this.getStoreUserRevenueChart(storeId, query)
        break
      default:
        throw new BadRequestException('Invalid chart type')
    }

    await this.cacheManager.set(cacheKey, chart, 1000 * 60 * 60 * 12) //cache for 0.5 day

    return chart
  }

  private buildPeriodParams(query: ChartQueryDto) {
    let start: Date
    let end: Date
    switch (query.period) {
      case ChartPeriod.last7Days:
        start = new Date(Date.now() - 1000 * 60 * 60 * 24 * 7)
        end = new Date()
        break
      case ChartPeriod.last30Days:
        start = new Date(Date.now() - 1000 * 60 * 60 * 24 * 30)
        end = new Date()
        break
      case ChartPeriod.last90Days:
        start = new Date(Date.now() - 1000 * 60 * 60 * 24 * 90)
        end = new Date()
        break
      default:
        throw new BadRequestException('Invalid period')
    }

    return {
      start,
      end,
    }
  }

  async getStoreUserConverstionChart(
    storeId: string,
    query: ChartQueryDto,
    chartName: string,
    otf: boolean = false
  ): Promise<ChartDto> {
    await this.userPermissions.requiresAccessOnStore(storeId)
    const store = await this.getStore(storeId)
    if (!store.shopifyConfig?.myshopifyDomain) {
      throw new NotFoundException('Shopify config not found')
    }
    const { start, end } = this.buildPeriodParams(query)

    const funnelsQuery: AmplitudeFunnelsQuery = {
      start,
      end,
      events: [
        {
          event_type: '[Shopify] page_viewed',
          filters: [
            {
              subprop_type: AmplitudePropType.event,
              subprop_key: 'shop',
              subprop_op: AmplitudePropOp.is,
              subprop_value: [store.shopifyConfig.myshopifyDomain],
            },
            {
              subprop_type: AmplitudePropType.user,
              subprop_key: 'gp:split_model-exp',
              subprop_op: AmplitudePropOp.is,
              subprop_value: ['control', 'test'],
            },
            ...(otf
              ? [
                  {
                    subprop_type: AmplitudePropType.user,
                    subprop_key: 'gp:promoCandidate',
                    subprop_op: AmplitudePropOp.is,
                    subprop_value: ['true'],
                  },
                ]
              : []),
          ],
        },
        {
          event_type: '[Shopify] checkout_completed',
        },
      ],
      interval: AmplitudeInterval.daily,
      groupBy: 'gp:split_model-exp',
      conversionWindowInSeconds: 60 * 60 * 3, //3 hours
    }

    const funnels =
      await this.analyticsService.getAmplitudeFunnels(funnelsQuery)

    const groups = funnels.data
      .map((d): FunnelChartDto['groups'][number] => {
        return {
          groupName: d.groupValue,
          funnel: [
            {
              funnelStep: 'sessions',
              count: d.cumulativeRaw[0],
            },
            {
              funnelStep: 'checkouts',
              count: d.cumulativeRaw[1],
              conversionRate: d.cumulative[1],
            },
          ],
        }
      })
      .sort((a, b) => a.groupName.localeCompare(b.groupName))

    const lift =
      groups.length >= 2 && groups[0].funnel[1].conversionRate
        ? (groups[1].funnel[1].conversionRate! -
            groups[0].funnel[1].conversionRate!) /
          groups[0].funnel[1].conversionRate!
        : undefined

    const chart: ChartDto = {
      funnelChart: {
        chartName:
          lift !== undefined
            ? `${chartName} (lift: ${Math.floor(lift * 10000) / 100}%)`
            : chartName,
        groups,
      },
    }

    return plainToInstance(ChartDto, chart)
  }

  async getStoreUserRevenueChart(
    storeId: string,
    query: ChartQueryDto
  ): Promise<ChartDto> {
    await this.userPermissions.requiresAccessOnStore(storeId)
    const store = await this.getStore(storeId)
    if (!store.shopifyConfig?.myshopifyDomain) {
      throw new NotFoundException('Shopify config not found')
    }
    const { start, end } = this.buildPeriodParams(query)

    const eventSegmentsQuery: AmplitudeEventSegmentsQuery = {
      start,
      end,
      event: {
        event_type: '[Shopify] page_viewed',
        filters: [
          {
            subprop_type: AmplitudePropType.event,
            subprop_key: 'shop',
            subprop_op: AmplitudePropOp.is,
            subprop_value: [store.shopifyConfig.myshopifyDomain],
          },
        ],
      },
      event2: {
        event_type: '[Shopify] checkout_completed',
        filters: [
          {
            subprop_type: AmplitudePropType.event,
            subprop_key: 'shop',
            subprop_op: AmplitudePropOp.is,
            subprop_value: [store.shopifyConfig.myshopifyDomain],
          },
        ],
        group_by: [
          {
            type: AmplitudePropType.event,
            value: 'checkoutTotalPriceAmount',
          },
        ],
      },
      metric: AmplitudeMetric.formula,
      formula: 'PROPSUM(B)/UNIQUES(A)',
      interval: AmplitudeInterval.daily,
      segmentBy: [
        {
          prop: 'gp:split_model-exp',
          op: AmplitudePropOp.is,
          values: ['control', 'test'],
        },
      ],
      groupBy: 'gp:split_model-exp',
    }

    const eventSegments =
      await this.analyticsService.getAmplitudeEventSegments(eventSegmentsQuery)

    const groups = eventSegments.data.seriesLabels?.[0]
      ? eventSegments.data.series
          .map((d, index): AggregatedEventChartDto['groups'][number] => {
            return {
              groupName: eventSegments.data.seriesLabels[index],
              aggregatedValues: [
                {
                  name: 'revenue per user',
                  value: eventSegments.data.seriesCollapsed[index][0].value,
                },
              ],
            }
          })
          .sort((a, b) => a.groupName.localeCompare(b.groupName))
      : []

    const lift =
      groups.length && groups[0].aggregatedValues[0].value
        ? (groups[1].aggregatedValues[0].value -
            groups[0].aggregatedValues[0].value) /
          groups[0].aggregatedValues[0].value
        : undefined

    const chart: ChartDto = {
      aggregatedEventChart: {
        chartName:
          lift !== undefined
            ? `User Revenue (lift: ${Math.floor(lift * 10000) / 100}%)`
            : 'User Revenue',
        unit: 'USD',
        groups,
      },
    }

    return plainToInstance(ChartDto, chart)
  }

  async findStoreInfoByMyshopifyDomain(
    myshopifyDomain: string,
    grantAccess = false,
    skipCache = false
  ): Promise<{
    storeInfo?: {
      storeId: string
      storeConfig?: StoreConfigDto
    }
  }> {
    //There is no need for user access to just get the store info (which only includes the store config). But access on the shopify store is needed if grantAccess is true.

    const cacheKey = `intentnow-findStoreInfoByMyshopifyDomain:${this.appName}:${myshopifyDomain}`

    if (!skipCache) {
      const cachedData =
        await this.cacheManager.get<
          Awaited<
            ReturnType<
              typeof StoresService.prototype.findStoreInfoByMyshopifyDomain
            >
          >
        >(cacheKey)

      if (cachedData) {
        if (cachedData.storeInfo && grantAccess) {
          await this.userPermissions.requiresAccessOnShopifyStore(
            myshopifyDomain
          )
          this.userPermissions.grantAccessOnStore(cachedData.storeInfo.storeId)
        }
        return cachedData
      }
    }

    const store = await this.storeModel.findOne({
      'shopifyConfig.myshopifyDomain': myshopifyDomain,
    })
    const storeId = store?._id.toString()

    const storeConfig = await this.apiAuthGuard.callWithUser(
      this.apiAuthGuard.adminRobotUser(),
      async () => {
        if (!storeId) {
          return undefined
        }
        const store = await this.getStore(storeId)
        return store.effectiveConfig
      }
    )
    const storeInfo = storeId ? { storeId, storeConfig } : undefined

    //Cache the storeInfo for one minute
    await this.cacheManager.set(cacheKey, { storeInfo }, 60000)

    if (storeInfo && grantAccess) {
      await this.userPermissions.requiresAccessOnShopifyStore(myshopifyDomain)
      this.userPermissions.grantAccessOnStore(storeInfo.storeId)
    }

    return {
      storeInfo,
    }
  }

  async getStoreImage(storeId: string, imageId: string) {
    await this.userPermissions.requiresAccessOnStore(storeId)

    const image = await this.storeImageModel.findById(imageId)
    if (!image || image.storeId !== storeId) {
      throw new NotFoundException('Image not found')
    }

    return plainToInstance(StoreImageDto, image.toObject())
  }

  async getStoreImages(storeId: string, crudQuery: CrudQuery) {
    await this.userPermissions.requiresAccessOnStore(storeId)

    const { filter, options } = buildMongoQuery(this.storeImageModel, crudQuery)
    const paginatedResults = await this.storeImageModel.paginate(
      {
        ...filter,
        storeId: {
          $eq: storeId,
        },
      },
      options
    )

    const images = mapPaginatedMongoResultsToDto(
      paginatedResults,
      StoreImageDto,
      PaginatedStoreImagesDto
    )
    return images
  }

  async createStoreImage(
    storeId: string,
    dto: StoreImageCreateDto,
    imageFile: Express.Multer.File
  ) {
    await this.userPermissions.requiresAccessOnStore(storeId)
    await this.getStore(storeId)

    let imageName = dto.name
    if (!imageName) {
      imageName = imageFile.originalname
    }

    let ext = path.extname(imageFile.originalname)
    if (ext.startsWith('.')) {
      ext = ext.slice(1)
    }

    const uploadName = `${randomHash()}/${imageFile.originalname.replaceAll(' ', '_')}`
    const uploadPath = `${this.intentowConfig.imageStorage.uploadPath}/${uploadName}`

    //Resize and get metadata
    const newBuffer = await sharp(imageFile.buffer)
      .resize({
        width: 1000,
        height: 1000,
        fit: 'inside',
        withoutEnlargement: true,
      })
      .toBuffer()
    const metadata = await sharp(newBuffer).metadata()

    //Save to GCS
    const stream = Readable.from(newBuffer)
    await this.storageService.getDisk('gcs').put(uploadPath, stream)

    const newStoreImage = new this.storeImageModel({
      storeId,
      name: imageName,
      fileInfo: {
        filename: imageFile.originalname,
        mimetype: imageFile.mimetype,
        size: metadata.size!,
        ext,
        format: metadata.format!.toUpperCase(),
        width: metadata.width,
        height: metadata.height,
      },
      createdAt: new Date(),
      uploadInfo: {
        cdnBucket: this.intentowConfig.imageStorage.cdnBucket,
        path: uploadPath,
      },
      imageUrl: `${this.intentowConfig.imageStorage.imageUrlPrefix}${uploadPath}`,
    } satisfies StoreImage)
    const created = await newStoreImage.save()

    return plainToInstance(StoreImageDto, created.toObject())
  }

  async deleteStoreImage(storeId: string, imageId: string) {
    await this.userPermissions.requiresAccessOnStore(storeId)
    await this.getStoreImage(storeId, imageId)

    const deleted = await this.storeImageModel.findByIdAndDelete(imageId)
    if (!deleted) {
      throw new NotFoundException('Image not found')
    }

    return plainToInstance(StoreImageDto, deleted.toObject())
  }

  async getStoreLaunchConfig(storeId: string, launchConfigId: string) {
    await this.userPermissions.requiresAccessOnStore(storeId)

    const launchConfig =
      await this.storeLaunchConfigModel.findById(launchConfigId)
    if (!launchConfig || launchConfig.storeId !== storeId) {
      throw new NotFoundException('Launch config not found')
    }

    return plainToInstance(StoreLaunchConfigDto, launchConfig.toObject())
  }

  async getStoreLaunchConfigs(storeId: string, crudQuery: CrudQuery) {
    await this.userPermissions.requiresAccessOnStore(storeId)

    const { filter, options } = buildMongoQuery(
      this.storeLaunchConfigModel,
      crudQuery
    )
    const paginatedResults = await this.storeLaunchConfigModel.paginate(
      {
        ...filter,
        storeId: {
          $eq: storeId,
        },
      },
      options
    )

    const launchConfigs = mapPaginatedMongoResultsToDto(
      paginatedResults,
      StoreLaunchConfigDto,
      PaginatedStoreLaunchConfigsDto
    )
    return launchConfigs
  }

  async createStoreLaunchConfig(
    storeId: string,
    dto: StoreLaunchConfigCreateDto
  ) {
    this.userPermissions.requiresAdmin()
    await this.getStore(storeId)

    this.validateLaunchConfig(dto)

    const newStoreLaunchConfig = new this.storeLaunchConfigModel({
      storeId,
      name: dto.name,
      configVariants: dto.configVariants,
      createdAt: new Date(),
    } satisfies StoreLaunchConfig)
    const created = await newStoreLaunchConfig.save()

    return plainToInstance(StoreLaunchConfigDto, created.toObject())
  }

  async deleteStoreLaunchConfig(storeId: string, launchConfigId: string) {
    await this.userPermissions.requiresAccessOnStore(storeId)
    await this.getStoreLaunchConfig(storeId, launchConfigId)

    const deleted =
      await this.storeLaunchConfigModel.findByIdAndDelete(launchConfigId)
    if (!deleted) {
      throw new NotFoundException('Launch config not found')
    }

    return plainToInstance(StoreLaunchConfigDto, deleted.toObject())
  }

  async getActiveLaunchConfig(storeId: string) {
    await this.userPermissions.requiresAccessOnStore(storeId)

    const launchConfigs = await this.getStoreLaunchConfigs(storeId, {
      sorts: [{ field: 'createdAt', order: 'desc' }],
      limit: 1,
    })
    if (!launchConfigs.data.length) {
      return undefined
    }

    return launchConfigs.data[0]
  }

  validateLaunchConfig(launchConfig: StoreLaunchConfigCreateDto) {
    if (!launchConfig.name) {
      throw new BadRequestException('invalid-name')
    }
    if (!launchConfig.configVariants.length) {
      throw new BadRequestException('invalid-config-variants-length')
    }
    launchConfig.configVariants.forEach((x) => {
      if (x.modelConfigV1) {
        this.validateModelConfigV1(x.modelConfigV1)
      } else {
        throw new BadRequestException('invalid-config-variant')
      }
    })
  }

  validateModelConfigV1({
    model,
    floor,
    ceiling,
    start,
    end,
  }: ModelConfigV1Dto) {
    if (!model) {
      throw new BadRequestException('invalid-model')
    }
    if (floor < 0 || floor > 1) {
      throw new BadRequestException('invalid-floor')
    }
    if (ceiling < 0 || ceiling > 1) {
      throw new BadRequestException('invalid-ceiling')
    }
    if (start < 0) {
      throw new BadRequestException('invalid-start')
    }
    if (end < 0) {
      throw new BadRequestException('invalid-end')
    }
    if (floor >= ceiling) {
      throw new BadRequestException('invalid-floor-ceiling')
    }
    if (start >= end) {
      throw new BadRequestException('invalid-start-end')
    }
  }
}
