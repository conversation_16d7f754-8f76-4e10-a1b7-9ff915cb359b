import { Module } from '@nestjs/common'
import { StoresController } from './stores.controller'
import { AuthModule } from 'src/auth/auth.module'
import { StoresService } from './stores.service'
import {
  LinkStoreRequest,
  LinkStoreRequestSchema,
  Store,
  StoreSchema,
} from './entities/store.mongo'
import { MongooseModule } from '@nestjs/mongoose'
import { ClsModule } from 'nestjs-cls'
import { UsersModule } from 'src/users/users.module'
import { AnalyticsModule } from 'src/analytics/analytics.module'
import { StoreCampaignsService } from './store-campaigns.service'
import { StoreCampaign, StoreCampaignSchema } from './entities/campaign.mongo'
import { StoreCampaignsController } from './store-campaigns.controller'
import { StoreOffer, StoreOfferSchema } from './entities/offer.mongo'
import { StoreOffersService } from './store-offers.service'

@Module({
  imports: [
    AuthModule,
    UsersModule,
    AnalyticsModule,
    ClsModule,
    MongooseModule.forFeature([
      {
        name: Store.name,
        collection: 'stores',
        schema: StoreSchema,
      },
    ]),
    MongooseModule.forFeature([
      {
        name: LinkStoreRequest.name,
        collection: 'link_store_requests',
        schema: LinkStoreRequestSchema,
      },
    ]),
    MongooseModule.forFeature([
      {
        name: StoreCampaign.name,
        collection: 'store_campaigns',
        schema: StoreCampaignSchema,
      },
    ]),
    MongooseModule.forFeature([
      {
        name: StoreOffer.name,
        collection: 'store_offers',
        schema: StoreOfferSchema,
      },
    ]),
    UsersModule,
  ],
  controllers: [StoresController, StoreCampaignsController],
  providers: [StoresService, StoreCampaignsService, StoreOffersService],
  exports: [StoresService, StoreCampaignsService, StoreOffersService],
})
export class StoresModule {}
