import {
  Body,
  Controller,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common'
import {
  PaginatedStoresDto,
  StoreCreateDto,
  StoreDto,
  StoreUpdateDto,
} from '../dto/store.dto'
import { ApiAuthGuard } from 'src/auth/api-auth-guard'
import { StoresService } from './stores.service'
import { CrudQuery, CrudQueryDto } from '../dto/common.dto'
import { ParsedCrudQuery } from 'src/common/data-helper'
import { ChartQueryDto, StoreChartType } from 'src/dto/analytics.dto'
import { ApiParam } from '@nestjs/swagger'

@UseGuards(ApiAuthGuard)
@Controller('api/intentnow')
export class StoresController {
  constructor(private readonly storesService: StoresService) {}

  @Get('stores/:storeId')
  async getOne(@Param('storeId') storeId: string) {
    const store = await this.storesService.getStore(storeId)
    return store
  }

  @Get('stores')
  async getList(
    @Query() q: CrudQueryDto, //Only for swagger
    @ParsedCrudQuery({
      sortFields: ['createdAt', 'name'],
      filterFields: ['name', 'shopifyConfig.myshopifyDomain'],
    })
    query: CrudQuery
  ): Promise<PaginatedStoresDto> {
    const stores = await this.storesService.getStores(query)
    return stores
  }

  @Post('stores')
  async createOne(@Body() dto: StoreCreateDto): Promise<StoreDto> {
    return await this.storesService.createStore(dto)
  }

  @Patch('stores/:storeId')
  async updateOne(
    @Param('storeId') storeId: string,
    @Body() dto: StoreUpdateDto
  ): Promise<StoreDto> {
    return await this.storesService.updateStore(storeId, dto)
  }

  @Get('users/:userId/stores')
  async getStoreListForUser(
    @Param('userId') userId: string,
    @Query() q: CrudQueryDto, //Only for swagger
    @ParsedCrudQuery({
      sortFields: ['createdAt', 'name'],
      filterFields: ['name'],
    })
    query: CrudQuery
  ) {
    return await this.storesService.getStoresForUser(userId, query)
  }

  @Get('link-store-requests/:requestId')
  async getLinkStoreRequest(@Param('requestId') requestId: string) {
    return await this.storesService.getLinkStoreRequest(requestId)
  }

  @Post('link-store-requests/:requestId/commit')
  async commitLinkStoreRequest(@Param('requestId') requestId: string) {
    return await this.storesService.commitLinkStoreRequest(requestId)
  }

  @ApiParam({
    name: 'chartType',
    enum: StoreChartType,
  })
  @Get('stores/:storeId/charts/:chartType')
  async getStoreUserConverstionChart(
    @Param('storeId') storeId: string,
    @Param('chartType') chartType: StoreChartType,
    @Query() query: ChartQueryDto
  ) {
    return await this.storesService.getStoreChart(storeId, chartType, query)
  }
}
