import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common'
import {
  PaginatedStoresDto,
  ShopifyStoreIntegrationUpdateDto,
  StoreAddUserDto,
  StoreCreateDto,
  StoreDto,
  StoreImageCreateDto,
  StoreUpdateDto,
  StoreLaunchConfigDto,
  StoreLaunchConfigCreateDto,
  PaginatedStoreLaunchConfigsDto,
  ActiveStoreLaunchConfigDto,
} from '../dto/store.dto'
import { ApiAuthGuard } from 'src/auth/api-auth-guard'
import { StoresService } from './stores.service'
import { CrudQuery, CrudQueryDto } from '../dto/common.dto'
import { ParsedCrudQuery } from 'src/common/data-helper'
import { ChartQueryDto, StoreChartType } from 'src/dto/analytics.dto'
import { ApiParam } from '@nestjs/swagger'
import { ShopifyStoreService } from './shopify-store.service'
import { FileInterceptor } from '@nestjs/platform-express'

@UseGuards(ApiAuthGuard)
@Controller('api/intentnow')
export class StoresController {
  constructor(
    private readonly storesService: StoresService,
    private readonly shopifyStoreService: ShopifyStoreService
  ) {}

  @Get('stores/:storeId')
  async getOne(@Param('storeId') storeId: string) {
    const store = await this.storesService.getStore(storeId)
    return store
  }

  @Get('stores')
  async getList(
    @Query() q: CrudQueryDto, //Only for swagger
    @ParsedCrudQuery({
      sortFields: ['createdAt', 'name'],
      filterFields: ['name', 'shopifyConfig.myshopifyDomain'],
    })
    query: CrudQuery
  ): Promise<PaginatedStoresDto> {
    const stores = await this.storesService.getStores(query)
    return stores
  }

  @Post('stores')
  async createOne(@Body() dto: StoreCreateDto): Promise<StoreDto> {
    return await this.storesService.createStore(dto)
  }

  @Patch('stores/:storeId')
  async updateOne(
    @Param('storeId') storeId: string,
    @Body() dto: StoreUpdateDto
  ): Promise<StoreDto> {
    return await this.storesService.updateStore(storeId, dto)
  }

  @Get('users/:userId/stores')
  async getStoreListForUser(
    @Param('userId') userId: string,
    @Query() q: CrudQueryDto, //Only for swagger
    @ParsedCrudQuery({
      sortFields: ['createdAt', 'name'],
      filterFields: ['name'],
    })
    query: CrudQuery
  ) {
    return await this.storesService.getStoresForUser(userId, query)
  }

  @Get('stores/:storeId/users')
  async getStoreUsers(
    @Param('storeId') storeId: string,
    @Query() q: CrudQueryDto, //Only for swagger
    @ParsedCrudQuery({
      sortFields: ['email', 'displayName'],
      filterFields: ['email', 'displayName'],
    })
    query: CrudQuery
  ) {
    return await this.storesService.getStoreUsers(storeId, query)
  }

  @Post('stores/:storeId/users')
  async addUserToStore(
    @Param('storeId') storeId: string,
    @Body() dto: StoreAddUserDto
  ) {
    return await this.storesService.addUserToStore(storeId, dto)
  }

  @Delete('stores/:storeId/users/:userId')
  async deleteUserFromStore(
    @Param('storeId') storeId: string,
    @Param('userId') userId: string
  ) {
    return await this.storesService.deleteUserFromStore(storeId, userId)
  }

  @Get('link-store-requests/:requestId')
  async getLinkStoreRequest(@Param('requestId') requestId: string) {
    return await this.storesService.getLinkStoreRequest(requestId)
  }

  @Post('link-store-requests/:requestId/commit')
  async commitLinkStoreRequest(@Param('requestId') requestId: string) {
    return await this.storesService.commitLinkStoreRequest(requestId)
  }

  @ApiParam({
    name: 'chartType',
    enum: StoreChartType,
  })
  @Get('stores/:storeId/charts/:chartType')
  async getStoreUserConverstionChart(
    @Param('storeId') storeId: string,
    @Param('chartType') chartType: StoreChartType,
    @Query() query: ChartQueryDto
  ) {
    return await this.storesService.getStoreChart(storeId, chartType, query)
  }

  @Get('stores/:storeId/shopify-integration')
  async getShopifyIntegration(@Param('storeId') storeId: string) {
    return await this.shopifyStoreService.getShopifyIntegration(storeId)
  }

  @Patch('stores/:storeId/shopify-integration')
  async updateShopifyIntegration(
    @Param('storeId') storeId: string,
    @Body() dto: ShopifyStoreIntegrationUpdateDto
  ) {
    return await this.shopifyStoreService.updateShopifyIntegration(storeId, dto)
  }

  @Get('stores/:storeId/images')
  async getStoreImages(
    @Param('storeId') storeId: string,
    @Query() q: CrudQueryDto, //Only for swagger
    @ParsedCrudQuery({
      sortFields: ['createdAt', 'name', 'fileInfo.size'],
      filterFields: ['name'],
    })
    query: CrudQuery
  ) {
    return await this.storesService.getStoreImages(storeId, query)
  }

  @Get('stores/:storeId/images/:imageId')
  async getStoreImage(
    @Param('storeId') storeId: string,
    @Param('imageId') imageId: string
  ) {
    return await this.storesService.getStoreImage(storeId, imageId)
  }

  @Delete('stores/:storeId/images/:imageId')
  async deleteStoreImage(
    @Param('storeId') storeId: string,
    @Param('imageId') imageId: string
  ) {
    return await this.storesService.deleteStoreImage(storeId, imageId)
  }

  @Post('stores/:storeId/images')
  @UseInterceptors(
    FileInterceptor('imageFile', {
      limits: {
        fileSize: 1024 * 1024 * 8, //8MB maximum file size
      },
    })
  )
  async createStoreImage(
    @Param('storeId') storeId: string,
    @UploadedFile() imageFile: Express.Multer.File,
    @Body() dto: StoreImageCreateDto
  ) {
    return await this.storesService.createStoreImage(storeId, dto, imageFile)
  }

  @Get('stores/:storeId/launch-configs')
  async getStoreLaunchConfigs(
    @Param('storeId') storeId: string,
    @Query() q: CrudQueryDto, //Only for swagger
    @ParsedCrudQuery({
      sortFields: ['createdAt'],
      filterFields: [],
    })
    query: CrudQuery
  ): Promise<PaginatedStoreLaunchConfigsDto> {
    return await this.storesService.getStoreLaunchConfigs(storeId, query)
  }

  @Post('stores/:storeId/launch-configs')
  async createStoreLaunchConfig(
    @Param('storeId') storeId: string,
    @Body() dto: StoreLaunchConfigCreateDto
  ): Promise<StoreLaunchConfigDto> {
    return await this.storesService.createStoreLaunchConfig(storeId, dto)
  }

  @Get('stores/:storeId/active-launch-config')
  async getActiveLaunchConfig(
    @Param('storeId') storeId: string
  ): Promise<ActiveStoreLaunchConfigDto> {
    const activeLaunchConfig =
      await this.storesService.getActiveLaunchConfig(storeId)
    return {
      activeLaunchConfig,
    }
  }
}
