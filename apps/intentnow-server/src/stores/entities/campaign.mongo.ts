import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose'
import { HydratedDocument } from 'mongoose'
import * as paginate from 'mongoose-paginate-v2'
import { mongooseSchemaOptions } from 'src/common/data-helper'

@Schema(mongooseSchemaOptions)
export class StoreCampaign {
  @Prop()
  storeId: string

  @Prop()
  createdAt: Date

  @Prop()
  updatedAt: Date

  @Prop()
  name: string

  @Prop()
  enabled: boolean

  @Prop()
  startDate: Date

  @Prop()
  endDate?: Date

  @Prop()
  launchRatio: number //percentage of users to launch to

  // @Prop()
  // widgetId: string
}

export type StoreCampaignDocument = HydratedDocument<StoreCampaign>
export const StoreCampaignSchema = SchemaFactory.createForClass(StoreCampaign)
StoreCampaignSchema.plugin(paginate)
