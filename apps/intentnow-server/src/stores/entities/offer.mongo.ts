import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose'
import { HydratedDocument } from 'mongoose'
import * as paginate from 'mongoose-paginate-v2'
import { mongooseSchemaOptions } from 'src/common/data-helper'

export class OfferLaunchConfig {
  @Prop()
  launchRatio: number //percentage of users to launch to

  //TODO: implement schedules
}

export class OfferWidgetConfig {
  @Prop()
  type: 'widget3'

  @Prop()
  dialog: any

  @Prop()
  teaser: any
}

export class CodeGenConfig {
  @Prop()
  alphabet: string

  @Prop()
  length: number

  @Prop()
  prefix?: string
}

export class OfferDiscountConfig {
  @Prop()
  discountType: 'percent' | 'amount'

  @Prop()
  percentOff?: number

  @Prop()
  amountOff?: number

  @Prop()
  minimumOrder?: number
}

export class OfferDiscountCodeConfig {
  @Prop({
    type: CodeGenConfig,
  })
  codeGen: CodeGenConfig

  @Prop({
    type: OfferDiscountConfig,
  })
  discount: OfferDiscountConfig

  @Prop()
  discountDurationInMinutes: number

  @Prop()
  cacheValidDurationInMinutes: number
}

@Schema(mongooseSchemaOptions)
export class StoreOffer {
  @Prop()
  storeId: string

  @Prop()
  createdAt: Date

  @Prop()
  updatedAt: Date

  @Prop()
  name: string

  @Prop()
  status: 'draft' | 'active' | 'offline'

  @Prop({
    type: OfferLaunchConfig,
  })
  launchConfig?: OfferLaunchConfig

  @Prop({
    type: OfferWidgetConfig,
  })
  widgetConfig?: OfferWidgetConfig

  @Prop({
    type: OfferDiscountCodeConfig,
  })
  discountConfig?: OfferDiscountCodeConfig
}

export type StoreOfferDocument = HydratedDocument<StoreOffer>
export const StoreOfferSchema = SchemaFactory.createForClass(StoreOffer)
StoreOfferSchema.plugin(paginate)
