import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { HydratedDocument } from 'mongoose'
import * as paginate from 'mongoose-paginate-v2'
import { mongooseSchemaOptions } from 'src/common/data-helper'

export class StoreShopifyConfig {
  @Prop()
  myshopifyDomain: string

  @Prop()
  appHandle?: string
}

@Schema(mongooseSchemaOptions)
export class Store {
  @Prop()
  createdAt: Date

  @Prop()
  updatedAt: Date

  @Prop()
  name: string

  @Prop()
  website: string

  @Prop({
    type: StoreShopifyConfig,
  })
  shopifyConfig?: StoreShopifyConfig
}

export type StoreDocument = HydratedDocument<Store>
export const StoreSchema = SchemaFactory.createForClass(Store)
StoreSchema.plugin(paginate)

export enum StoreWidgetType {
  widget2 = 'widget2',
  widget3 = 'widget3',
}

@Schema(mongooseSchemaOptions)
export class StoreWidget {
  @Prop()
  createdAt: Date

  @Prop()
  updatedAt: Date

  @Prop()
  storeId: string

  @Prop()
  name: string

  @Prop()
  type: StoreWidgetType

  // @Prop()
  // discount: DiscountConfig

  // @Prop()
  // widget2?: Widget2Config

  // @Prop()
  // widget3?: Widget3Config
}

export type StoreWidgetDocument = HydratedDocument<StoreWidget>
export const StoreWidgetSchema = SchemaFactory.createForClass(StoreWidget)
StoreWidgetSchema.plugin(paginate)

@Schema(mongooseSchemaOptions)
export class LinkStoreRequest {
  @Prop()
  status: 'pending' | 'completed' | 'expired' | 'failed'

  @Prop()
  storeType: 'shopify'

  @Prop()
  storeRef: string //For shopify store this is the myshopifydomain

  @Prop()
  storeName: string

  @Prop()
  storeWebsite: string

  @Prop()
  source: string //Fo shopify store this is the app handle

  @Prop()
  createdAt: Date

  @Prop()
  updatedAt: Date

  @Prop()
  expiresAt: Date
}

export type LinkStoreRequestDocument = HydratedDocument<LinkStoreRequest>
export const LinkStoreRequestSchema =
  SchemaFactory.createForClass(LinkStoreRequest)
LinkStoreRequestSchema.plugin(paginate)
