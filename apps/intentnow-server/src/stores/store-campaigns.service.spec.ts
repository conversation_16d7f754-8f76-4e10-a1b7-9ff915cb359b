import { Test, TestingModule } from '@nestjs/testing'
import { StoreCampaignsService } from './store-campaigns.service'
import { MongoMemoryReplSet } from 'mongodb-memory-server'
import { MongoClient } from 'mongodb'
import { UserPermissions } from 'src/users/user-permissions'
import { MongooseModule } from '@nestjs/mongoose'
import { StoreCampaign, StoreCampaignSchema } from './entities/campaign.mongo'
import { StoreCampaignStatus } from 'src/dto/campaign.dto'
import { StoresService } from 'src/stores/stores.service'
import { UnauthorizedException } from '@nestjs/common'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { ConfigService } from '@nestjs/config'

describe('CampaignsService', () => {
  let service: StoreCampaignsService
  let mongoServer: MongoMemoryReplSet
  let mongoClient: MongoClient //use for insert initial test data

  beforeAll(async () => {
    //A replset is needed for the MongoDB transaction to work
    //mongoServer = await MongoMemoryServer.create()
    mongoServer = await MongoMemoryReplSet.create({ replSet: { count: 4 } })
    mongoClient = await MongoClient.connect(mongoServer.getUri(), {})

    //Insert initial test data into MongoDB here
    //...
  }, 60000)

  beforeEach(async () => {
    const cacheManagerMock = {
      get: jest.fn(),
      set: jest.fn(),
      del: jest.fn(),
    }

    const module: TestingModule = await Test.createTestingModule({
      imports: [
        MongooseModule.forRoot(mongoServer.getUri()),
        MongooseModule.forFeature([
          {
            name: StoreCampaign.name,
            collection: 'store_campaigns',
            schema: StoreCampaignSchema,
          },
        ]),
      ],
      providers: [StoreCampaignsService, ConfigService],
    })
      .useMocker((token) => {
        if (token === 'CONFIGURATION_TOKEN') {
          return {
            app: {
              name: 'test-app',
            },
          }
        } else if (token === UserPermissions) {
          return {
            requiresAccessOnStore: jest.fn(),
          }
        } else if (token === StoresService) {
          return {
            getStore: jest.fn(),
          }
        } else if (token === CACHE_MANAGER) {
          return cacheManagerMock
        }
        return {}
      })
      .compile()

    service = module.get<StoreCampaignsService>(StoreCampaignsService)
  })

  afterEach(async () => {
    await mongoClient.db().collection('store_campaigns').deleteMany({})
  })

  afterAll(async (): Promise<any> => {
    if (mongoClient) {
      await mongoClient.close()
    }
    if (mongoServer) {
      await mongoServer.stop()
    }
  })

  it('store campaign crud operations', async () => {
    const storeId = 'test-store-id'

    let getCampaignsResults = await service.getStoreCampaigns(storeId, {})
    expect(getCampaignsResults.data.length).toBe(0)
    expect(getCampaignsResults.meta.total).toBe(0)

    const newStoreCampaign = {
      name: 'test-campaign',
      enabled: true,
      startDate: new Date(),
      endDate: new Date(),
      launchRatio: 10,
    }

    const created = await service.createStoreCampaign(storeId, newStoreCampaign)
    expect(created).toBeDefined()
    expect(created.name).toBe(newStoreCampaign.name)

    getCampaignsResults = await service.getStoreCampaigns(storeId, {})
    expect(getCampaignsResults.data.length).toBe(1)
    expect(getCampaignsResults.meta.total).toBe(1)
    expect(getCampaignsResults.data[0].name).toEqual(created.name)
    expect(getCampaignsResults.data[0].status).toBe(StoreCampaignStatus.ended)

    await service.updateStoreCampaign(storeId, created._id, {
      name: 'test-campaign-updated',
    })

    const getCampaign = await service.getStoreCampaign(storeId, created._id)
    expect(getCampaign.name).toBe('test-campaign-updated')
    expect(getCampaign.status).toBe(StoreCampaignStatus.ended)

    await service.deleteStoreCampaign(storeId, created._id)

    getCampaignsResults = await service.getStoreCampaigns(storeId, {})
    expect(getCampaignsResults.data.length).toBe(0)
    expect(getCampaignsResults.meta.total).toBe(0)
  }, 60000)

  it('store campaign paginated list', async () => {
    const storeId = 'test-store-id'

    let getCampaignsResults = await service.getStoreCampaigns(storeId, {})
    expect(getCampaignsResults.data.length).toBe(0)
    expect(getCampaignsResults.meta.total).toBe(0)

    const newStoreCampaign = {
      enabled: false,
      startDate: new Date(),
      endDate: new Date(),
      launchRatio: 10,
    }

    await service.createStoreCampaign(storeId, {
      ...newStoreCampaign,
      name: 'campaign-1',
    })
    await service.createStoreCampaign(storeId, {
      ...newStoreCampaign,
      name: 'campaign-3',
    })
    await service.createStoreCampaign(storeId, {
      ...newStoreCampaign,
      name: 'campaign-4',
    })
    await service.createStoreCampaign(storeId, {
      ...newStoreCampaign,
      name: 'campaign-2',
    })
    await service.createStoreCampaign(storeId, {
      ...newStoreCampaign,
      name: 'campaign-5',
    })

    getCampaignsResults = await service.getStoreCampaigns(storeId, {
      limit: 2,
      sorts: [{ field: 'name', order: 'asc' }],
    })
    expect(getCampaignsResults.data.length).toBe(2)
    expect(getCampaignsResults.meta.total).toBe(5)
    expect(getCampaignsResults.meta.page).toBe(1)
    expect(getCampaignsResults.meta.pageCount).toBe(3)
    expect(getCampaignsResults.data[0].name).toBe('campaign-1')
    expect(getCampaignsResults.data[1].name).toBe('campaign-2')

    getCampaignsResults = await service.getStoreCampaigns(storeId, {
      limit: 3,
      page: 2,
      sorts: [{ field: 'name', order: 'asc' }],
    })
    expect(getCampaignsResults.data.length).toBe(2)
    expect(getCampaignsResults.meta.total).toBe(5)
    expect(getCampaignsResults.meta.page).toBe(2)
    expect(getCampaignsResults.meta.pageCount).toBe(2)
  })

  it('get active campaign', async () => {
    const storeId = 'test-store-id'

    let activeCampaign = await service.getActiveCampaign(storeId)
    expect(activeCampaign).toBeUndefined()

    const now = new Date(Date.now() - 1000 * 60)
    await service.createStoreCampaign(storeId, {
      name: 'campaign-1',
      enabled: true,
      startDate: new Date(now.getTime() + 1000 * 60 * 60),
      launchRatio: 10,
    })
    const campaign2 = await service.createStoreCampaign(storeId, {
      name: 'campaign-2',
      enabled: true,
      startDate: now,
      endDate: new Date(now.getTime() + 1000 * 60 * 60 * 0.9),
      launchRatio: 10,
    })
    await service.createStoreCampaign(storeId, {
      name: 'campaign-3',
      enabled: false,
      startDate: now,
      endDate: new Date(now.getTime() + 1000 * 60 * 60 * 24),
      launchRatio: 10,
    })

    activeCampaign = await service.getActiveCampaign(storeId)
    expect(activeCampaign).toBeDefined()
    expect(activeCampaign?.name).toBe('campaign-2')

    await service.deleteStoreCampaign(storeId, campaign2._id)
    activeCampaign = await service.getActiveCampaign(storeId)
    expect(activeCampaign).toBeUndefined()

    await service.createStoreCampaign(storeId, {
      name: 'campaign-4',
      enabled: true,
      startDate: now,
      endDate: new Date(now.getTime() + 1000 * 60 * 60 * 0.5),
      launchRatio: 10,
    })
    activeCampaign = await service.getActiveCampaign(storeId)
    expect(activeCampaign).toBeDefined()
    expect(activeCampaign?.name).toBe('campaign-4')
    expect(activeCampaign?.status).toBe(StoreCampaignStatus.active)
  }, 60000)

  it('decorate campaign statuses', async () => {
    const storeId = 'test-store-id'

    const now = new Date()
    await service.createStoreCampaign(storeId, {
      name: 'campaign-1',
      enabled: true,
      startDate: new Date(now.getTime() + 1000 * 60 * 60),
      launchRatio: 10,
    })
    await service.createStoreCampaign(storeId, {
      name: 'campaign-2',
      enabled: true,
      startDate: new Date(now.getTime() - 1000 * 60 * 60),
      endDate: new Date(now.getTime() + 1000 * 60 * 60 * 0.5),
      launchRatio: 10,
    })
    await service.createStoreCampaign(storeId, {
      name: 'campaign-3',
      enabled: false,
      startDate: now,
      endDate: new Date(now.getTime() + 1000 * 60 * 60 * 24),
      launchRatio: 10,
    })
    await service.createStoreCampaign(storeId, {
      name: 'campaign-4',
      enabled: true,
      startDate: new Date(now.getTime() - 1000 * 60 * 60 * 0.5),
      endDate: new Date(now.getTime() - 1000 * 60 * 30 * 0.6),
      launchRatio: 10,
    })

    const campaigns = await service.getStoreCampaigns(storeId, {
      sorts: [{ field: 'name', order: 'asc' }],
    })
    expect(campaigns.data.length).toBe(4)
    expect(campaigns.data[0].status).toBe(StoreCampaignStatus.notStarted)
    expect(campaigns.data[1].status).toBe(StoreCampaignStatus.active)
    expect(campaigns.data[2].status).toBe(StoreCampaignStatus.disabled)
    expect(campaigns.data[3].status).toBe(StoreCampaignStatus.ended)
  })

  it('campaign validations', async () => {
    const storeId = 'test-store-id'

    await expect(
      service.createStoreCampaign(storeId, {
        name: 'campaign-0',
        enabled: true,
        startDate: new Date(),
        endDate: new Date(Date.now() - 1000 * 60 * 60),
        launchRatio: 10,
      })
    ).rejects.toThrow('invalid-start-end')

    const now = new Date()
    await service.createStoreCampaign(storeId, {
      name: 'campaign-1',
      enabled: true,
      startDate: new Date(),
      endDate: new Date(now.getTime() + 1000 * 60 * 60),
      launchRatio: 10,
    })

    await expect(
      service.createStoreCampaign(storeId, {
        name: 'campaign-2',
        enabled: true,
        startDate: new Date(now.getTime() + 1000 * 60 * 60 * 0.5),
        endDate: new Date(now.getTime() + 1000 * 60 * 60),
        launchRatio: 10,
      })
    ).rejects.toThrow('conflicting-campaign')

    await service.createStoreCampaign(storeId, {
      name: 'campaign-2',
      enabled: false,
      startDate: new Date(now.getTime() + 1000 * 60 * 60 * 0.5),
      endDate: new Date(now.getTime() + 1000 * 60 * 60),
      launchRatio: 10,
    })

    await expect(
      service.createStoreCampaign(storeId, {
        name: 'campaign-3',
        enabled: true,
        startDate: new Date(now.getTime() - 1000 * 60 * 60 * 0.5),
        launchRatio: 10,
      })
    ).rejects.toThrow('conflicting-campaign')

    await service.createStoreCampaign(storeId, {
      name: 'campaign-1',
      enabled: true,
      startDate: new Date(now.getTime() + 1000 * 60 * 60 * 1.5),
      launchRatio: 10,
    })

    await expect(
      service.createStoreCampaign(storeId, {
        name: 'campaign-1',
        enabled: true,
        startDate: new Date(now.getTime() + 1000 * 60 * 60 * 2),
        endDate: new Date(now.getTime() + 1000 * 60 * 60 * 3),
        launchRatio: 10,
      })
    ).rejects.toThrow('conflicting-campaign')
  })
})

describe('CampaignsService Access Control', () => {
  let service: StoreCampaignsService

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [],
      providers: [StoreCampaignsService, ConfigService],
    })
      .useMocker((token) => {
        if (token === 'CONFIGURATION_TOKEN') {
          return {
            app: {
              name: 'test-app',
            },
          }
        } else if (token === UserPermissions) {
          return {
            requiresAccessOnStore: () => {
              throw new UnauthorizedException('Unauthorized')
            },
          }
        } else if (token === StoresService) {
          return {
            getStore: jest.fn(),
          }
        }
        return {}
      })
      .compile()

    service = module.get<StoreCampaignsService>(StoreCampaignsService)
  })

  it('should throw unauthorized exception', async () => {
    await expect(
      service.getStoreCampaigns('test-store-id', {})
    ).rejects.toThrow('Unauthorized')

    await expect(
      service.getStoreCampaign('test-store-id', '123')
    ).rejects.toThrow('Unauthorized')

    await expect(
      service.createStoreCampaign('test-store-id', {
        name: 'test-campaign',
        enabled: true,
        startDate: new Date(),
        endDate: new Date(),
        launchRatio: 10,
      })
    ).rejects.toThrow('Unauthorized')

    await expect(
      service.updateStoreCampaign('test-store-id', '123', {
        name: 'test-campaign',
      })
    ).rejects.toThrow('Unauthorized')

    await expect(
      service.deleteStoreCampaign('test-store-id', '123')
    ).rejects.toThrow('Unauthorized')

    await expect(service.getActiveCampaign('test-store-id')).rejects.toThrow(
      'Unauthorized'
    )
  })
})
