import * as dotenv from 'dotenv'
dotenv.config()
import { otelSDK } from './tracing'
import { NestFactory, Reflector } from '@nestjs/core'
import { AppModule } from './app.module'
import { Logger, LoggerErrorInterceptor } from 'nestjs-pino'
import Statsig from 'statsig-node'
import { ServerConfig } from './config/config'
import { ConfigService } from '@nestjs/config'
import * as amplitude from '@amplitude/analytics-node'
import { ClassSerializerInterceptor, ValidationPipe } from '@nestjs/common'
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger'
import { IntentnowModule } from './intentnow/intentnow.module'
import { AdminModule } from './admin/admin.module'
import { ShopifyModule } from './shopify/shopify.module'
import { StoresModule } from './stores/stores.module'
import { UsersModule } from './users/users.module'
import { StorefrontsModule } from './storefronts/storefronts.module'

function sleep(ms: number) {
  return new Promise((resolve) => setTimeout(resolve, ms))
}

async function bootstrap() {
  await sleep(+(process.env.APP_START_WAIT_TIME_MS ?? 0))

  otelSDK.start()

  const app = await NestFactory.create(AppModule, {
    rawBody: true, // clerk webhook need to have access to the raw body
  })
  app.enableCors()
  app.useLogger(app.get(Logger))
  app.useGlobalInterceptors(new LoggerErrorInterceptor())
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true, // Transform is recomended configuration for avoid issues with arrays of files transformations
      whitelist: true,
    })
  )
  app.useGlobalInterceptors(
    new ClassSerializerInterceptor(app.get(Reflector), {
      //strategy: 'excludeAll',
      excludeExtraneousValues: true,
    })
  )
  app.enableShutdownHooks()

  const statsigConfig = app
    .get(ConfigService)
    .get<ServerConfig['statsig']>('statsig')
  if (statsigConfig?.secretKey) {
    await Statsig.initialize(statsigConfig.secretKey, {
      environment: {
        tier: statsigConfig.environment,
      },
    })
  }

  const amplitudeConfig = app
    .get(ConfigService)
    .get<ServerConfig['amplitude']>('amplitude')
  if (amplitudeConfig?.apiKey) {
    amplitude.init(amplitudeConfig.apiKey, {
      logLevel: amplitudeConfig.logLevel,
    })
  }

  const appEnv =
    app.get(ConfigService).get<ServerConfig['app']>('app')?.environment ?? ''

  if (['staging', 'development'].includes(appEnv)) {
    SwaggerModule.setup(
      'api-docs',
      app,
      () =>
        SwaggerModule.createDocument(
          app,
          new DocumentBuilder()
            .setTitle('IntentNow API')
            .setVersion('1.0')
            .build(),
          {
            include: [
              IntentnowModule,
              AdminModule,
              ShopifyModule,
              UsersModule,
              StoresModule,
              StorefrontsModule,
            ],
          }
        ),
      {
        swaggerOptions: {
          supportedSubmitMethods: [], // This disables the "Try it out" button for all endpoints
        },
      }
    )
  }

  await app.listen(4000)
}
bootstrap()
  .then(() => {})
  .catch(() => {})
