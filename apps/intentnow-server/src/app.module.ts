import { MiddlewareConsumer, Module } from '@nestjs/common'
import { ConfigModule, ConfigService } from '@nestjs/config'
import { LoggerModule } from 'nestjs-pino'
import { CorrelationIdMiddleware } from '@eropple/nestjs-correlation-id'
import { AppController } from './app.controller'
import { AppService } from './app.service'
import { ShopifyModule } from './shopify/shopify.module'
import { getServerConfig, ServerConfig } from './config/config'
import { IntentnowModule } from './intentnow/intentnow.module'
import { AuthModule } from './auth/auth.module'
import { MongooseModule } from '@nestjs/mongoose'
import { FirebaseAdminModule } from './firebase-admin'
import { AdminModule } from './admin/admin.module'
import { OpenTelemetryModule } from 'nestjs-otel'
import { MemoryStoredFile, NestjsFormDataModule } from 'nestjs-form-data'
import { FirestoreClientModule } from './firestore-client.module'
import { CacheModule, CacheStore } from '@nestjs/cache-manager'
import { redisStore } from 'cache-manager-redis-yet'
import { memoryStore } from 'cache-manager'
import { MurLockModule } from 'murlock'
import { StoresModule } from './stores/stores.module'
import { UsersModule } from './users/users.module'
import { ClerkModule } from './clerk/clerk.module'
import { ClsModule } from 'nestjs-cls'
import { AnalyticsController } from './analytics/analytics.controller'
import { AnalyticsService } from './analytics/analytics.service'
import { AnalyticsModule } from './analytics/analytics.module'

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
      load: [getServerConfig],
    }),
    LoggerModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) =>
        configService.get('logger') ?? {},
      inject: [ConfigService],
    }),
    OpenTelemetryModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) =>
        configService.get<ServerConfig['openTelemetry']>('openTelemetry')!,
      inject: [ConfigService],
    }),
    ClsModule.forRoot({
      middleware: {
        // automatically mount the
        // ClsMiddleware for all routes
        mount: true,

        // and use the setup method to
        // provide default store values.
        // setup: (cls, req) => {
        //   cls.set('userId', req.headers['x-user-id'])
        // },
      },
    }),
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) =>
        configService.get<ServerConfig['mongoDB']>('mongoDB')!,
      inject: [ConfigService],
    }),
    FirebaseAdminModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) =>
        configService.get<ServerConfig['firebase']>('firebase') ?? {},
      inject: [ConfigService],
    }),
    NestjsFormDataModule.config({
      storage: MemoryStoredFile,
    }),
    ShopifyModule,
    IntentnowModule,
    AuthModule,
    AdminModule,
    FirestoreClientModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) =>
        configService.get<ServerConfig['firestore']>('firestore') ?? {},
      inject: [ConfigService],
    }),
    CacheModule.registerAsync({
      isGlobal: true,
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => {
        const redisUrl =
          configService.get<ServerConfig['cache']>('cache')?.redisUrl
        if (redisUrl) {
          const store = await redisStore({
            url: redisUrl,
          })
          return {
            store: () => store as unknown as CacheStore,
          }
        } else {
          // Fall back to in-memory cache
          const store = memoryStore()
          return {
            store: () => store,
          }
        }
      },
      inject: [ConfigService],
    }),
    MurLockModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => {
        const redisUrl =
          configService.get<ServerConfig['cache']>('cache')?.redisUrl
        if (!redisUrl) {
          return {
            redisOptions: {},
            wait: 2000,
            logLevel: 'log',
            maxAttempts: 600,
          }
        }
        return {
          redisOptions: { url: redisUrl },
          wait: 2000,
          maxAttempts: 600,
          logLevel: 'log',
          ignoreUnlockFail: false,
        }
      },
      inject: [ConfigService],
    }),
    StoresModule,
    UsersModule,
    AnalyticsModule,
    ClerkModule,
  ],
  controllers: [AppController, AnalyticsController],
  providers: [AppService, AnalyticsService],
})
export class AppModule {
  constructor() {}

  configure(consumer: MiddlewareConsumer) {
    // Set up the correlation ID for logging
    consumer.apply(CorrelationIdMiddleware()).forRoutes('*')
  }
}
