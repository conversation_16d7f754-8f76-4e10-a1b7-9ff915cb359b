import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common'
import { UsersService } from './users.service'
import { CrudQuery, CrudQueryDto } from 'src/dto/common.dto'
import {
  PaginatedUsersDto,
  PaginatedUserStoreAccessesDto,
  UserCreateDto,
  UserDto,
  UserStoreAccessCreateDto,
  UserStoreAccessDto,
  UserUpdateDto,
} from 'src/dto/user.dto'
import { ApiAuthGuard } from 'src/auth/api-auth-guard'
import { ParsedCrudQuery } from 'src/common/data-helper'

@UseGuards(ApiAuthGuard)
@Controller('api/intentnow/users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Get(':userId')
  async getOne(@Param('userId') userId: string) {
    return await this.usersService.getUser(userId)
  }

  @Get()
  async getList(
    @Query() q: CrudQueryDto, //Keep this for Swagger API Spec
    @ParsedCrudQuery({
      sortFields: ['createdAt', 'name'],
      filterFields: ['name'],
    })
    query: CrudQuery
  ): Promise<PaginatedUsersDto> {
    return await this.usersService.getUsers(query)
  }

  @Post()
  async createOne(@Body() dto: UserCreateDto): Promise<UserDto> {
    return await this.usersService.createUser(dto)
  }

  @Patch(':userId')
  async updateOne(
    @Param('userId') userId: string,
    @Body() dto: UserUpdateDto
  ): Promise<UserDto> {
    return await this.usersService.updateUser(userId, dto)
  }

  @Delete(':userId')
  async deleteOne(@Param('userId') userId: string): Promise<UserDto> {
    return await this.usersService.deleteUser(userId)
  }

  @Get(':userId/store-accesses')
  async getStoreAccessList(
    @Param('userId') userId: string,
    @Query() q: CrudQueryDto, //Keep this for Swagger API Spec
    @ParsedCrudQuery({
      sortFields: ['createdAt', 'name'],
      filterFields: ['name'],
    })
    query: CrudQuery
  ): Promise<PaginatedUserStoreAccessesDto> {
    return await this.usersService.getUserStoreAccesses(userId, query)
  }

  @Post(':userId/store-accesses')
  async createStoreAccess(
    @Param('userId') userId: string,
    @Body() dto: UserStoreAccessCreateDto
  ): Promise<UserStoreAccessDto> {
    return await this.usersService.createUserStoreAccess(userId, dto)
  }

  @Delete(':userId/store-accesses/:userStoreAccessId')
  async deleteStoreAccess(
    @Param('userId') userId: string,
    @Param('userStoreAccessId') userStoreAccessId: string
  ): Promise<UserStoreAccessDto> {
    return await this.usersService.deleteUserStoreAccess(
      userId,
      userStoreAccessId
    )
  }
}
