import {
  Inject,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common'
import { ShopifyRestOp, ShopifyService } from 'src/shopify/shopify.service'
import {
  DeleteWebPixelMutation,
  DeleteWebPixelMutationVariables,
  EnableWebPixelMutation,
  EnableWebPixelMutationVariables,
  GetIntentnowMetafieldsQuery,
  GetIntentnowMetafieldsQueryVariables,
  GetWebPixelQuery,
  GetWebPixelQueryVariables,
  UpdateWebPixelMutation,
  UpdateWebPixelMutationVariables,
} from 'src/types/shopify-api/admin.generated'
import { ConfigService } from '@nestjs/config'
import { ServerConfig } from 'src/config/config'
import { ShopifyRequestContext } from 'src/shopify/entities/shopify'
import { GetDiscountService } from './get-discount.service'
import * as crypto from 'crypto'
import { initializeApiFetchMetrics } from 'src/common/fetch'
import { Firestore } from '@google-cloud/firestore'
import * as amplitude from '@amplitude/analytics-node'
import { MetricService } from 'nestjs-otel'
import { Counter } from '@opentelemetry/api'
import { StoreConfigService } from './store-config.service'
import { EventService } from './event.service'
import { UserContext } from 'src/auth/entities/user-context'
import { StoresService } from 'src/stores/stores.service'
import { ApiAuthGuard } from 'src/auth/api-auth-guard'
import { FilterOp } from 'src/dto/common.dto'

@Injectable()
export class IntentnowService {
  private readonly logger = new Logger(IntentnowService.name)
  private readonly intentowConfig: ServerConfig['intentnow']
  private readonly discountOnCounter: Counter
  private readonly discountOffCounter: Counter
  private readonly discountInvalidRequestCounter: Counter

  constructor(
    private readonly configService: ConfigService,
    private readonly shopifyService: ShopifyService,
    private readonly eventService: EventService,
    private readonly getDiscountService: GetDiscountService,
    private readonly storeConfigService: StoreConfigService,
    private readonly metricService: MetricService,
    private readonly storesService: StoresService,
    private readonly apiAuthGuard: ApiAuthGuard,
    @Inject('FIRESTORE_CLIENT') private readonly firestore: Firestore
  ) {
    this.intentowConfig =
      this.configService.get<ServerConfig['intentnow']>('intentnow')!

    this.discountOnCounter = this.metricService.getCounter('discount_on', {
      prefix: 'intentnow_server',
    })
    this.discountOffCounter = this.metricService.getCounter('discount_off', {
      prefix: 'intentnow_server',
    })
    this.discountInvalidRequestCounter = this.metricService.getCounter(
      'discount_invalid_request',
      {
        prefix: 'intentnow_server',
      }
    )
    this.metricService && initializeApiFetchMetrics(this.metricService)
  }

  async getClientDiscount(
    requester: UserContext | undefined,
    clientId: string | undefined,
    preview?: boolean | undefined,
    previewWidgetId?: string | undefined
  ) {
    const request = this.shopifyService.validateRequester(requester)
    const shop = request.shop!

    this.logger.log(
      {
        shop,
        clientId,
        preview,
      },
      `getClientDiscount: started`
    )

    if (preview) {
      this.logger.log({ shop }, `getClientDiscount: preview mode`)
      return await this.getDiscountService.getDiscountAndWidgetPreview(
        requester,
        previewWidgetId
      )
    }

    if (!clientId) {
      this.logger.log({ shop }, `getClientDiscount: no clientId`)
      this.discountInvalidRequestCounter.add(1, {
        shop,
      })
      return {}
    }

    const dynamicStoreConfig = this.storeConfigService.getDyanmicStoreConfig(
      shop,
      clientId
    )
    const { getDiscount, modelSplit, statsigKeys, sendAmplitudeEvents } =
      dynamicStoreConfig
    const featureOn = Boolean(getDiscount && modelSplit)

    if (!featureOn) {
      this.logger.log(
        {
          shop,
          getDiscount,
          modelSplit,
          statsigKeys,
          clientId,
        },
        `getClientDiscount: feature off`
      )
      this.discountOffCounter.add(1, {
        shop,
      })
      return {}
    }
    this.logger.log(
      {
        shop,
        getDiscount,
        modelSplit,
        statsigKeys,
        clientId,
      },
      `getClientDiscount: feature on`
    )
    this.discountOnCounter.add(1, {
      shop,
    })

    try {
      const {
        dialog,
        discount,
        widgetId,
        widget2,
        widget3,
        promoCandidate,
        configName,
        configVariantIndex,
      } = await this.getDiscountService.getDiscountAndWidgetV2(
        requester,
        clientId,
        modelSplit!,
        statsigKeys.modelExperimentKey
      )

      if (sendAmplitudeEvents) {
        const identifyObj = new amplitude.Identify()
        identifyObj.set(
          'eventVersion',
          this.intentowConfig.analyticsEventVersion
        )

        if (configName && modelSplit === 'test') {
          identifyObj.set('launchConfig', `${configName}_${configVariantIndex}`)
        }

        if (promoCandidate !== undefined) {
          identifyObj.set(`promoCandidate`, promoCandidate)
        }

        amplitude.identify(identifyObj, {
          user_id: clientId,
          device_id: clientId,
        })

        if (promoCandidate !== undefined) {
          await this.eventService.saveEvent(
            {
              id: crypto.randomUUID(),
              name: 'intentnow-promo-candidate',
              shop,
              clientId,
              type: 'custom-intentnow',
              eventSource: 'intentnow-server',
              timestamp: new Date().toISOString(),
              data: {
                promoCandidate: promoCandidate,
              },
            },
            undefined,
            undefined,
            dynamicStoreConfig
          )
        }
      }

      return {
        configName,
        configVariantIndex,
        dialog,
        discount,
        widgetId,
        widget2,
        widget3,
      }
    } catch (e) {
      this.logger.error(
        e,
        `getClientDiscount: failed to create personalized discount code`
      )
    }

    this.logger.log(`getClientDiscount: no discount available`)
    return {}
  }

  async getWebPixel(requester: UserContext | undefined) {
    this.shopifyService.validateRequester(requester)
    try {
      const query = `#graphql
        query getWebPixel {
          webPixel {
            id
            settings
          }
        }`

      const response = await this.shopifyService.executeGraphQl<
        GetWebPixelQuery,
        GetWebPixelQueryVariables
      >(requester, { query }, true)

      return response.data?.webPixel
    } catch (e) {
      return undefined
    }
  }

  async deleteWebPixel(requester: UserContext | undefined) {
    this.shopifyService.validateRequester(requester)
    const id = (await this.getWebPixel(requester))?.id
    if (!id) {
      throw new NotFoundException('Web pixel not found')
    }

    this.logger.log(`deleteWebPixel: started`)

    const query = `#graphql
      mutation deleteWebPixel($id: ID!) {
        webPixelDelete(id: $id) {
          deletedWebPixelId
          userErrors {
            code
            field
            message
          }
        }
      }`
    const variables = {
      id,
    }

    const response = await this.shopifyService.executeGraphQl<
      DeleteWebPixelMutation,
      DeleteWebPixelMutationVariables
    >(requester, { query, variables })

    if (!response.data?.webPixelDelete?.deletedWebPixelId) {
      throw new Error(`Failed to delete web pixel`)
    }

    return {
      deletedWebPixelId: response.data.webPixelDelete.deletedWebPixelId,
    }
  }

  getWebPixelInput(request: ShopifyRequestContext) {
    const shop = request.shop!
    return {
      settings: JSON.stringify({
        shop,
        eventApi: this.intentowConfig.eventApi.url,
        eventApiToken: this.intentowConfig.eventApi.token,
        eventSource: this.shopifyService.getShopifyAppHandle(request.appHandle),
      }),
    }
  }

  async createWebPixel(requester: UserContext | undefined) {
    const request = this.shopifyService.validateRequester(requester)

    this.logger.log(`deleteWebPixel: started`)

    const query = `#graphql
      mutation enableWebPixel($input: WebPixelInput!) {
        webPixelCreate(webPixel: $input) {
          userErrors {
            code
            field
            message
          }
          webPixel {
            settings
            id
          }
        }
      }`

    const variables = {
      input: this.getWebPixelInput(request),
    }

    const response = await this.shopifyService.executeGraphQl<
      EnableWebPixelMutation,
      EnableWebPixelMutationVariables
    >(requester, {
      query,
      variables,
    })

    if (response.data?.webPixelCreate?.userErrors.length) {
      throw new Error(`Failed to create web pixel due to invalid input`)
    }
    if (!response.data?.webPixelCreate?.webPixel) {
      throw new Error(`No web pixel created`)
    }

    return {
      webPixel: {
        id: response.data?.webPixelCreate?.webPixel.id,
        settings: JSON.parse(response.data?.webPixelCreate?.webPixel.settings),
      },
    }
  }

  async updateWebPixel(requester: UserContext | undefined, id: string) {
    const request = this.shopifyService.validateRequester(requester)
    this.logger.log({ id }, `updateWebPixel: started`)

    const query = `#graphql
      mutation updateWebPixel($id: ID!, $input: WebPixelInput!) {
        webPixelUpdate(id: $id, webPixel: $input) {
          userErrors {
            code
            field
            message
          }
          webPixel {
            id
            settings
          }
        }
      }`

    const variables = {
      id,
      input: this.getWebPixelInput(request),
    }

    const response = await this.shopifyService.executeGraphQl<
      UpdateWebPixelMutation,
      UpdateWebPixelMutationVariables
    >(requester, {
      query,
      variables,
    })

    if (response.data?.webPixelUpdate?.userErrors.length) {
      throw new Error(`Failed to update web pixel due to invalid input`)
    }

    return response?.data
  }

  async getAppMetafields(requester: UserContext | undefined) {
    this.shopifyService.validateRequester(requester)

    const appInfo = await this.shopifyService.getAppInstallation(requester)
    if (!appInfo) {
      throw new NotFoundException('App info not found')
    }

    const query = `#graphql
      query getIntentnowMetafields($appInstId:ID!) {
        appInstallation(id:$appInstId) {
          metafields(first: 250, namespace: "intentnow") {
            nodes {
              id
              namespace
              key
              value
            }
          }
        }
      }`

    const variables = {
      appInstId: appInfo.id,
    }

    const response = await this.shopifyService.executeGraphQl<
      GetIntentnowMetafieldsQuery,
      GetIntentnowMetafieldsQueryVariables
    >(requester, {
      query,
      variables,
    })

    return response.data?.appInstallation?.metafields.nodes ?? []
  }

  async setupAppMetafields(requester: UserContext | undefined) {
    //Set up app proxy subpath for the promo widget
    //This subpath needs to be unique for each different app instance, so we simply use the app handle as the subpath.
    //The subpath is saved into the app metafield so the frontend (Liquid template) can retrieve it easily.
    const request = this.shopifyService.validateRequester(requester)

    const appInfo = await this.shopifyService.getAppInstallation(requester)
    if (!appInfo) {
      throw new NotFoundException('App info not found')
    }

    const metafields = [
      {
        namespace: 'intentnow',
        key: 'appProxySubpath',
        ownerId: appInfo.id,
        value: `${appInfo.app.handle}`,
        type: 'single_line_text_field',
      },
      {
        namespace: 'intentnow',
        key: 'eventApiUrl',
        ownerId: appInfo.id,
        value: `${this.intentowConfig.eventApi.url}`,
        type: 'single_line_text_field',
      },
      {
        namespace: 'intentnow',
        key: 'eventApiToken',
        ownerId: appInfo.id,
        value: `${this.intentowConfig.eventApi.token}`,
        type: 'single_line_text_field',
      },
      // {
      //   namespace: 'intentnow',
      //   key: 'amplitudeKey',
      //   ownerId: appInfo.id,
      //   value: `${this.amplitudeConfig.apiKey}`,
      //   type: 'single_line_text_field',
      // },
    ]
    this.logger.log(
      { shop: request.shop, metafields },
      `setupAppProxySubpath: metafields`
    )

    await this.shopifyService.setMetafield(requester, metafields)
    return {}
  }

  async getAppSettings(
    requester: UserContext | undefined,
    updateToLatest: boolean
  ) {
    const request = this.shopifyService.validateRequester(requester)
    const shop = request.shop
    this.logger.log({ shop }, `syncAppSettings: started`)

    try {
      const appHandle = this.shopifyService.getShopifyAppHandle(
        request.appHandle
      )

      const promises: [
        ReturnType<typeof this.getAppMetafieldsSettings>,
        ReturnType<typeof this.getWebPixelSettings>,
        ReturnType<typeof this.getPromoEmbedSettings>,
        ReturnType<typeof this.getMerchantPortalState>,
      ] = [
        this.getAppMetafieldsSettings(requester, updateToLatest),
        this.getWebPixelSettings(requester, updateToLatest),
        this.getPromoEmbedSettings(requester),
        this.getMerchantPortalState(requester),
      ]
      const [
        metafields,
        { webPixel },
        { promoEmbedActivated, promoEmbedActivationLink },
        { merchantPortal },
      ] = await Promise.all(promises)

      return {
        appHandle,
        webPixel,
        promoEmbedActivationLink,
        promoEmbedActivated,
        metafields,
        merchantPortal,
      }
    } catch (e) {
      this.logger.error(
        e,
        {
          shop,
        },
        `syncAppSettings: failed`
      )
      throw new InternalServerErrorException('Failed to sync app settings')
    }
  }

  async getAppMetafieldsSettings(
    requester: UserContext | undefined,
    updateToLatest = false
  ) {
    if (updateToLatest) {
      //Set up metafields for promo widget
      await this.setupAppMetafields(requester)
    }
    const metafields = await this.getAppMetafields(requester)
    return metafields
  }

  async getWebPixelSettings(
    requester: UserContext | undefined,
    updateToLatest = false
  ) {
    this.shopifyService.validateRequester(requester)
    let webPixel = await this.getWebPixel(requester)

    //Check if webPixel settings are up to date
    if (updateToLatest && webPixel) {
      if (
        webPixel.settings?.eventApi !== this.intentowConfig.eventApi.url ||
        webPixel.settings?.eventApiToken !== this.intentowConfig.eventApi.token
      ) {
        //Update the settings if they are stale
        await this.updateWebPixel(requester, webPixel.id)

        //Re-fetch the webPixel
        webPixel = await this.getWebPixel(requester)
      }
    }

    return {
      webPixel,
    }
  }

  async getPromoEmbedSettings(requester: UserContext | undefined) {
    const request = this.shopifyService.validateRequester(requester)
    const appHandle = this.shopifyService.getShopifyAppHandle(request.appHandle)
    const appHandle2 = this.shopifyService.getShopifyAppHandle2(
      request.appHandle
    )
    const promoEmbedId = this.shopifyService.getPromoEmbedId(request.appHandle)
    let promoEmbedActivationLink: string | undefined
    let promoEmbedActivated = false
    if (promoEmbedId) {
      promoEmbedActivationLink = `https://${request.shop}/admin/themes/current/editor?context=apps&activateAppId=${promoEmbedId}/intentnow-embed`

      const theme = await this.shopifyService.getCurrentTheme(requester)
      if (theme) {
        const themeSettingsData = await this.shopifyService.executeRest<{
          asset: {
            key: string
            value: string
            content_type: string
            theme_id: string
            size: number
          }
        }>(requester, {
          op: ShopifyRestOp.get,
          path: `/themes/${theme.id}/assets.json?asset[key]=config/settings_data.json`,
        })
        const themeSettings = JSON.parse(themeSettingsData.data.asset.value)
        const matchAppEmbedBlocks = [
          `shopify://apps/${appHandle}/blocks/intentnow-embed/${promoEmbedId}`,
        ]
        if (appHandle2) {
          //For some reason, Shopify may not use the standard app handle for the appembed block string
          matchAppEmbedBlocks.push(
            `shopify://apps/${appHandle2}/blocks/intentnow-embed/${promoEmbedId}`
          )
        }
        for (const block of Object.values(
          themeSettings.current?.blocks ?? {}
        ) as any) {
          if (
            block.disabled === false &&
            matchAppEmbedBlocks.includes(block.type)
          ) {
            promoEmbedActivated = true
            break
          }
        }
      }
    }

    return {
      promoEmbedActivated,
      promoEmbedActivationLink,
    }
  }

  async getMerchantPortalState(requester: UserContext | undefined) {
    const request = this.shopifyService.validateRequester(requester)
    const shop = request.shop!
    this.logger.log({ shop }, `getMerchantPortalState: started`)

    //We don't have a proper user context to read stores information, use a robot user with admin access
    this.apiAuthGuard.useRobotUser()
    const { data: stores } = await this.storesService.getStores({
      limit: 1,
      filters: [
        {
          field: 'shopifyConfig.myshopifyDomain',
          op: FilterOp.eq,
          value: shop,
        },
      ],
    })

    return {
      merchantPortal: {
        linkedStoreId: stores.length ? stores[0]._id : undefined,
      },
    }
  }

  async createLinkStoreRequest(requester: UserContext | undefined) {
    const request = this.shopifyService.validateRequester(requester)
    const shop = request.shop
    this.logger.log({ shop }, `createLinkStoreRequest: started`)

    const shopInfo = await this.shopifyService.getShopInfo(requester)

    if (!shopInfo) {
      throw new NotFoundException('Shop info not found')
    }

    return await this.storesService.createLinkStoreRequest({
      storeType: 'shopify',
      storeRef: shop,
      source: request.appHandle,
      storeName: shopInfo.name,
      storeWebsite: shopInfo.url,
    })
  }
}
