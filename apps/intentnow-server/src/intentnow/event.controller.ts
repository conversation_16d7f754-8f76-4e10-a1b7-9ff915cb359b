import { Body, Controller, Logger, Post, Req } from '@nestjs/common'
import { UserRequest } from 'src/auth/entities/user-context'
import { FormDataRequest } from 'nestjs-form-data'
import { MetricService } from 'nestjs-otel'
import { Counter } from '@opentelemetry/api'
import { EventService } from './event.service'
import {
  EventRequestDto,
  EventResponseDto,
  EventsRequestDto,
  RawEventResponseDto,
  RawEventsRequestDto,
} from '../dto/event.dto'
import { plainToInstance } from 'class-transformer'

@Controller()
export class IntentnowEventsController {
  private readonly logger = new Logger(IntentnowEventsController.name)
  private readonly eventCallsCounter: Counter

  constructor(
    private readonly eventService: EventService,
    private readonly metricService: MetricService
  ) {
    this.eventCallsCounter = this.metricService.getCounter('event_calls', {
      prefix: 'intentnow_server',
    })
  }

  async adaptiveSaveEvents(
    req: UserRequest,
    payload: EventRequestDto | EventsRequestDto
  ) {
    const xForwardedFor = req.headers['x-forwarded-for'] as string | undefined
    const ipAddress = xForwardedFor?.split?.(',')[0] ?? req.ip ?? 'unknown'
    const userAgent = req.headers['user-agent'] as string | undefined

    if ((payload as any)['events']) {
      const events = (payload as any).events as EventRequestDto[]
      const type = (payload as any).type
      this.eventCallsCounter.add(1, {
        type,
      })
      const resp = await this.eventService.saveEvents(
        events,
        ipAddress,
        userAgent
      )
      return resp.map((r) => plainToInstance(EventResponseDto, r))
    } else {
      this.eventCallsCounter.add(1, {
        type: 'single',
      })
      const resp = await this.eventService.saveEvent(
        payload as EventRequestDto,
        ipAddress,
        userAgent
      )
      return [plainToInstance(EventResponseDto, resp)]
    }
  }

  @Post('api/intentnow/events')
  @FormDataRequest()
  async saveEvents(
    @Req() req: UserRequest,
    @Body()
    payload: EventsRequestDto
  ): Promise<EventResponseDto[]> {
    return await this.adaptiveSaveEvents(req, payload)
  }

  @Post('api/intentnow/raw-events')
  async saveRawEvents(
    @Req() req: UserRequest,
    @Body()
    payload: RawEventsRequestDto
  ): Promise<RawEventResponseDto[]> {
    const resp = await this.eventService.saveRawEvents(payload.events)
    return resp.map((r) => plainToInstance(RawEventResponseDto, r))
  }

  //To be compatible with the old event endpoint
  @Post('v1/events')
  @FormDataRequest()
  async saveEvents2(
    @Req() req: UserRequest,
    @Body()
    payload: EventsRequestDto
  ): Promise<EventResponseDto[]> {
    return await this.adaptiveSaveEvents(req, payload)
  }
}
