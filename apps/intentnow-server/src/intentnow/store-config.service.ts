import {
  BadRequestException,
  Inject,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common'
import { ShopifyService } from 'src/shopify/shopify.service'
import { ConfigService } from '@nestjs/config'
import { ServerConfig } from 'src/config/config'
import { FieldValue, Firestore } from '@google-cloud/firestore'
import {
  defaultDiscountConfig,
  defaultWidget2Config,
  defaultWidget3Config,
  ModelParameters,
  shoipifyStoresCollectionPath,
  ShopifyStore,
  shopifyStoreLaunchConfigsCollectionPath,
  StoreLaunchConfig,
  ShopifyStoreImage,
  shopifyStoreImagesCollectionPath,
  ShopifyStoreWidget,
  shopifyStoreWidgetsCollectionPath,
  Widget2Config,
  Widget3Config,
  WidgetType,
  ActiveShopifyStoreConfig,
} from '@packages/shared-entities'
import { UserContext } from 'src/auth/entities/user-context'
import { validateIsAdmin } from 'src/auth/api-auth-guard'
import Statsig from 'statsig-node'
import * as amplitude from '@amplitude/analytics-node'
import { getApiFetch } from 'src/common/fetch'
import { StorageService } from '@codebrew/nestjs-storage'
import { GoogleCloudStorage } from '@slynova/flydrive-gcs'
import * as path from 'path'
import { Readable } from 'stream'
import { mapTimestampFieldsToDate } from 'src/common/utils'
import { Cache } from 'cache-manager'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { MurLock } from 'murlock'

@Injectable()
export class StoreConfigService {
  logger = new Logger(StoreConfigService.name)
  private readonly intentowConfig: ServerConfig['intentnow']
  private readonly statsigConfig: ServerConfig['statsig']
  private readonly appName: string

  private readonly shopifyStorePromoConfigCache: {
    [shop: string]:
      | {
          timestamp: Date
          shopifyStore: ShopifyStore
        }
      | undefined
  } = {}

  constructor(
    private readonly configService: ConfigService,
    private readonly shopifyService: ShopifyService,
    private readonly storageService: StorageService,
    @Inject('FIRESTORE_CLIENT') private readonly firestore: Firestore,
    @Inject(CACHE_MANAGER) private readonly cacheManager: Cache
  ) {
    this.intentowConfig =
      configService.get<ServerConfig['intentnow']>('intentnow')!
    this.statsigConfig =
      this.configService.get<ServerConfig['statsig']>('statsig')!

    this.storageService.registerDriver?.('gcs', GoogleCloudStorage)
    this.appName = configService.get<ServerConfig['app']>('app')!.name
  }

  getDyanmicStoreConfig(shop: string, clientId: string) {
    const statsigUser = {
      userID: clientId,
    }
    const globalConfig = Statsig.getConfig(
      statsigUser,
      'intentnow-global-config'
    )
    const globalSaveEvents = globalConfig.get<boolean>('saveEvents', false)
    const globalPredictWithEvents = globalConfig.get<boolean>(
      'predictWithEvents',
      false
    )
    const globalGetDiscount = globalConfig.get<boolean>('getDiscount', false)

    const { storeConfigKey, storeConfigPrefix } = this.getStatsigKeys(shop)
    const storeConfig = Statsig.getConfig(statsigUser, storeConfigKey)

    let saveEvents =
      globalSaveEvents &&
      storeConfig.get<boolean | undefined | null>('saveEvents', undefined)
    if (saveEvents === undefined || saveEvents === null) {
      //Default to true if not specified, because this config was added later on
      saveEvents = true
    }

    const predictWithEvents =
      globalPredictWithEvents &&
      storeConfig.get<boolean>('predictWithEvents', false)
    const getDiscount =
      globalGetDiscount && storeConfig.get<boolean>('getDiscount', false)
    const sendStatsigEvents = storeConfig.get<boolean>(
      'sendStatsigEvents',
      false
    )
    const sendAmplitudeEvents = storeConfig.get<boolean>(
      'sendAmplitudeEvents',
      false
    )
    const modelExperimentKeyPostfix = storeConfig.get<string>(
      'modelExperimentKeyPostfix',
      'model-exp'
    )
    const modelExperimentKey = `${storeConfigPrefix}_${modelExperimentKeyPostfix}`
    let modelSplit: string | undefined

    if (getDiscount || predictWithEvents) {
      modelSplit = storeConfig.get<string | undefined>(
        'modelSplitOverride',
        undefined
      )

      if (!modelSplit) {
        const modelExp = Statsig.getExperiment(statsigUser, modelExperimentKey)
        modelSplit = modelExp.get<string | undefined>('split', undefined)
      }
    }

    const identifyObj = new amplitude.Identify()
    if (sendAmplitudeEvents) {
      identifyObj.set('eventVersion', this.intentowConfig.analyticsEventVersion)
      if (modelSplit) {
        identifyObj.set(`split_${modelExperimentKey}`, modelSplit)
        identifyObj.set('split_model-exp', modelSplit)
      } else {
        identifyObj.unset(`split_${modelExperimentKey}`)
        identifyObj.unset('split_model-exp')
      }
    }

    if (sendAmplitudeEvents) {
      amplitude.identify(identifyObj, {
        user_id: clientId,
        device_id: clientId,
      })
    }

    return {
      statsigKeys: {
        storeConfigKey,
        modelExperimentKey,
      },
      saveEvents,
      predictWithEvents,
      getDiscount,
      sendStatsigEvents,
      sendAmplitudeEvents,
      modelSplit,
    }
  }

  getStatsigKeys(shop: string) {
    const storeConfigPrefix = shop.replace(/\./g, '-')
    const storeConfigKey = `${storeConfigPrefix}_store-config`

    return {
      storeConfigPrefix,
      storeConfigKey,
    }
  }

  async getAnalyticsSettings(
    requester: UserContext | undefined,
    isAdmin = false
  ) {
    this.shopifyService.validateRequester(requester, isAdmin)
    const { analyticsConfig } = await this.getStoreConfigByShop(
      requester,
      false
    )

    return {
      chartIds: analyticsConfig?.chartIds ?? [],
      ...(isAdmin
        ? {
            internalDashboards: analyticsConfig?.internalDashboards,
          }
        : {}),
    }
  }

  async getStoreConfigByShop(
    requester: UserContext | undefined,
    loadWidgets = false
  ) {
    const request = this.shopifyService.validateRequester(requester)
    const shop = request.shop

    this.logger.log({ shop }, `getStoreConfigByShop: started`)
    const snap = await this.firestore
      .collection(shoipifyStoresCollectionPath)
      .where('shop', '==', shop)
      .limit(1)
      .get()

    if (snap.empty) {
      this.logger.error({ shop }, `getStoreConfigByShop: not found`)
      throw new NotFoundException('Store config not found')
    }

    const shopifyStore = {
      ...snap.docs[0].data(),
      _id: snap.docs[0].id,
    } as ShopifyStore
    mapTimestampFieldsToDate(shopifyStore, ['createdAt', 'updatedAt'])

    this.logger.log({ shop }, `getStoreConfigByShop: config fetched`)

    await this.decorateStoreConfig(shopifyStore, loadWidgets)

    return shopifyStore
  }

  //@Deprecated
  async getStoreConfigForGetDiscount(
    shop: string,
    configVariantName?: string,
    skipCache?: boolean
  ) {
    this.logger.log({ shop, configVariantName }, `getStoreConfig: started`)
    const cachedConfig = this.shopifyStorePromoConfigCache[shop]
    let shopifyStore: ShopifyStore | undefined
    if (!skipCache && cachedConfig?.shopifyStore?.promoConfig) {
      const now = new Date()
      if (
        now.getTime() - cachedConfig.timestamp.getTime() <
        this.intentowConfig.storePromo.storeConfigCacheTTLInSeconds * 1000
      ) {
        this.logger.log({ shop }, `getStoreConfig: returning cached config`)
        shopifyStore = cachedConfig.shopifyStore
      }
    }

    if (!shopifyStore) {
      this.logger.log({ shop }, `getStoreConfig: fetching config from DB`)

      const snap = await this.firestore
        .collection(shoipifyStoresCollectionPath)
        .where('shop', '==', shop)
        .limit(1)
        .get()
      if (!snap.empty) {
        shopifyStore = {
          ...snap.docs[0].data(),
          _id: snap.docs[0].id,
        } as ShopifyStore
        mapTimestampFieldsToDate(shopifyStore, ['createdAt', 'updatedAt'])

        await this.decorateStoreConfig(shopifyStore, true)

        if (shopifyStore.promoConfig) {
          this.logger.log({ shop }, `getStoreConfig: config fetched`)
          this.shopifyStorePromoConfigCache[shop] = {
            timestamp: new Date(),
            shopifyStore: shopifyStore,
          }
        } else {
          this.logger.warn(
            { shop },
            `getStoreConfig: invalid config, no promoConfig`
          )
          this.shopifyStorePromoConfigCache[shop] = undefined
          shopifyStore = undefined
        }
      }
    }

    if (shopifyStore?.promoConfig) {
      let promoConfig = shopifyStore.promoConfig
      if (configVariantName && shopifyStore.promoConfigVariants) {
        const configVariant = shopifyStore.promoConfigVariants.find(
          (config) => config.name === configVariantName
        )
        if (configVariant) {
          this.logger.log(
            { shop, configVariantName },
            `getStoreConfig: configVariant found, calculating merged config`
          )
          promoConfig = {
            ...promoConfig,
            model: {
              ...promoConfig.model,
              parameters: {
                ...promoConfig.model?.parameters,
                ...configVariant.model?.parameters,
              } as ModelParameters,
            },
            dialog: promoConfig.dialog
              ? {
                  ...promoConfig.dialog,
                  ...configVariant.dialog,
                }
              : undefined,
            discount: promoConfig.discount
              ? {
                  ...promoConfig.discount,
                  ...configVariant.discount,
                }
              : configVariant.discount,
          }
        } else {
          this.logger.error(
            { shop, configVariantName },
            `getStoreConfig: configVariant not found`
          )
        }
      }

      return {
        shopifyStoreId: shopifyStore._id,
        promoConfig: promoConfig!,
        analyticsConfig: shopifyStore.analyticsConfig,
        promoConfigVariants: shopifyStore.promoConfigVariants?.map(
          (v) => v.name
        ),
        _widgets: shopifyStore._widgets,
      }
    } else {
      this.logger.warn({ shop }, `getStoreConfig: no valid config`)
      return {}
    }
  }

  async getStoreConfigForGetDiscountV2(
    shop: string,
    clientId: string,
    skipCache?: boolean,
    preview?: boolean
  ) {
    this.logger.log(
      { shop, clientId },
      `getConfigVariantForGetDiscount: started`
    )

    const activeShopifyStoreConfig = await this.getActiveLaunchConfig(
      shop,
      skipCache
    )

    if (activeShopifyStoreConfig.launchConfigVariants.length === 1) {
      return {
        activeShopifyStoreConfig,
        configVariantIndex: 0,
        configVariant: activeShopifyStoreConfig.launchConfigVariants[0],
        modelOverrides: activeShopifyStoreConfig.modelOverrides,
      }
    } else if (
      activeShopifyStoreConfig.launchConfigVariants.length > 1 &&
      activeShopifyStoreConfig.currentLaunchConfig?.experimentKey
    ) {
      const statsigUser = {
        userID: clientId,
      }
      let expIndex: number
      if (preview) {
        //Preview mode, pick a random config variant
        expIndex = Math.floor(
          Math.random() * activeShopifyStoreConfig.launchConfigVariants.length
        )
      } else {
        const exp = Statsig.getExperiment(
          statsigUser,
          activeShopifyStoreConfig.currentLaunchConfig.experimentKey
        )
        expIndex = exp.get<number>('index', 0)
      }
      if (
        expIndex < 0 ||
        expIndex >= activeShopifyStoreConfig.launchConfigVariants.length
      ) {
        this.logger.error(
          { shop, clientId, expIndex },
          `getConfigVariantForGetDiscount: invalid expIndex`
        )
        throw new InternalServerErrorException('Invalid expIndex')
      }

      return {
        activeShopifyStoreConfig,
        configVariantIndex: expIndex,
        configVariant: activeShopifyStoreConfig.launchConfigVariants[expIndex],
        modelOverrides: activeShopifyStoreConfig.modelOverrides,
      }
    } else {
      this.logger.error(
        { shop },
        `getConfigVariantForGetDiscount: invalid config variants`
      )
      throw new InternalServerErrorException('invalid config variants')
    }
  }

  async invalidateStoreConfigCache(shop: string, storeId?: string) {
    this.logger.log({ shop }, `invalidateStoreConfigCache: started`)
    await this.cacheManager.del(
      `intentnow-activeLaunchConfig:${this.appName}:${shop}`
    )
    await this.cacheManager.del(
      `intentnow-auth-configByShop:${this.appName}:${shop}`
    )
    storeId &&
      (await this.cacheManager.del(
        `intentnow-auth-configByStoreId:${this.appName}:${storeId}`
      ))
  }

  async getActiveLaunchConfig(shop: string, skipCache?: boolean) {
    this.logger.log({ shop }, `getActiveLaunchConfig: started`)

    const cacheKey = `intentnow-activeLaunchConfig:${this.appName}:${shop}`
    let activeShopifyStoreConfig = skipCache
      ? undefined
      : await this.cacheManager.get<ActiveShopifyStoreConfig>(cacheKey)

    if (!activeShopifyStoreConfig) {
      this.logger.log(
        { shop },
        `getActiveLaunchConfig: fetching config from DB`
      )

      const snap = await this.firestore
        .collection(shoipifyStoresCollectionPath)
        .where('shop', '==', shop)
        .limit(1)
        .get()
      if (!snap.empty) {
        const shopifyStore = {
          ...snap.docs[0].data(),
          _id: snap.docs[0].id,
        } as ShopifyStore
        mapTimestampFieldsToDate(shopifyStore, ['createdAt', 'updatedAt'])

        await this.decorateStoreConfig(shopifyStore, true)

        const experimentKey = shopifyStore._currentLaunchConfig?.experimentKey
        let configVariants = shopifyStore._currentLaunchConfig?.configVariants
        if (!configVariants) {
          if (shopifyStore.promoConfig?.model?.parameters) {
            this.logger.warn(
              { shop },
              `getActiveLaunchConfig: no current launch config, falling back to legacy config`
            )
            configVariants = [
              {
                modelConfig: {
                  parameters: shopifyStore.promoConfig.model.parameters,
                },
              },
            ]
          } else {
            this.logger.error(
              { shop },
              `getActiveLaunchConfig: no model config found`
            )
            throw new InternalServerErrorException('No model config found')
          }
        }

        if (configVariants.length >= 1) {
          const launchConfigVariants = configVariants.map(
            (
              variant
            ): NonNullable<
              ActiveShopifyStoreConfig['launchConfigVariants']
            >[number] => {
              const widgetId =
                variant.widgetId ?? shopifyStore.promoConfig?.selectedWidgetId

              const widget = widgetId
                ? shopifyStore._widgets?.find((w) => w._id === widgetId)
                : undefined

              if (widgetId && !widget) {
                this.logger.error(
                  { shop, widgetId },
                  `getActiveLaunchConfig: widget not found`
                )
                throw new NotFoundException('Widget not found')
              }

              if (widget) {
                return {
                  modelConfig: variant.modelConfig,
                  discountConfig: widget.discount,
                  widget: {
                    widgetId: widget._id,
                    widget2: widget.widget2,
                    widget3: widget.widget3,
                  },
                }
              } else if (
                shopifyStore.promoConfig?.widget2 &&
                shopifyStore.promoConfig?.discount
              ) {
                return {
                  modelConfig: variant.modelConfig,
                  discountConfig: shopifyStore.promoConfig.discount,
                  widget: {
                    widget2: shopifyStore.promoConfig.widget2,
                  },
                }
              } else if (
                shopifyStore.promoConfig?.dialog &&
                shopifyStore.promoConfig?.discount
              ) {
                return {
                  modelConfig: variant.modelConfig,
                  discountConfig: shopifyStore.promoConfig.discount,
                  widget: {
                    dialog: shopifyStore.promoConfig.dialog,
                  },
                }
              } else {
                this.logger.error(
                  { shop, widgetId },
                  `getActiveLaunchConfig: valid widget/discount config not found`
                )
                throw new InternalServerErrorException(
                  'valid widget/discount config not found'
                )
              }
            }
          )

          if (launchConfigVariants.length > 1 && !experimentKey) {
            this.logger.error(
              { shop },
              `getActiveLaunchConfig: multiple config variants but no experiment key`
            )
            throw new InternalServerErrorException(
              'multiple config variants but no experiment key'
            )
          }

          activeShopifyStoreConfig = {
            storeId: shopifyStore._id,
            shop: shopifyStore.shop,
            currentLaunchConfig: shopifyStore._currentLaunchConfig,
            launchConfigVariants,
            modelOverrides:
              shopifyStore.promoConfig?.model?.overrides ||
              shopifyStore.promoConfig?.model?.clientOverrides
                ? {
                    overrides: shopifyStore.promoConfig?.model?.overrides,
                    clientOverrides:
                      shopifyStore.promoConfig?.model?.clientOverrides,
                  }
                : undefined,
          }

          //Cache for one minute
          await this.cacheManager.set(cacheKey, activeShopifyStoreConfig, 60000)
        } else {
          this.logger.error(
            { shop },
            `getActiveLaunchConfig: no config variants`
          )
          throw new InternalServerErrorException('No config variants')
        }
      }
    }

    if (!activeShopifyStoreConfig) {
      this.logger.error(
        { shop },
        `getActiveLaunchConfig: no active store config created`
      )
      throw new InternalServerErrorException('No active store config created')
    }

    this.logger.log(
      { shop, activeShopifyStoreConfig },
      `getActiveLaunchConfig: done`
    )

    return activeShopifyStoreConfig
  }

  private async decorateStoreConfig(
    shopifyStore: ShopifyStore,
    loadWidgets: boolean
  ) {
    shopifyStore._currentLaunchConfig =
      await this.getCurrentLaunchConfigByStoreId(shopifyStore._id)

    if (loadWidgets) {
      const widgetsSnap = await this.firestore
        .collection(shopifyStoreWidgetsCollectionPath(shopifyStore._id))
        .orderBy('createdAt', 'asc')
        .get()
      const widgets = widgetsSnap.docs.map(
        (doc) =>
          ({
            ...doc.data(),
            _id: doc.id,
          }) as ShopifyStoreWidget
      )
      shopifyStore._widgets = widgets
    }

    return shopifyStore
  }

  async updateStoreConfig(
    requester: UserContext | undefined,
    data: Partial<ShopifyStore>
  ) {
    const shop = requester?.shopifyContext?.shop
    this.logger.log(
      {
        shop,
      },
      `updateStoreConfig: started`
    )

    const storeConfig = await this.getStoreConfigByShop(requester)

    //We tightly control which field can be updated through API
    const update: any = {
      ...(data.appHandle !== undefined
        ? {
            appHandle:
              data.appHandle === '' ? FieldValue.delete() : data.appHandle,
          }
        : {}),
      ...(data.name !== undefined
        ? { name: data.name === '' ? FieldValue.delete() : data.name }
        : {}),
      ...(data.website !== undefined
        ? { website: data.website === '' ? FieldValue.delete() : data.website }
        : {}),
      ...(data.pendingInstall !== undefined
        ? { pendingInstall: data.pendingInstall }
        : {}),
    }

    let doUpdate = Object.values(update).some((x) => x !== undefined)

    if (data.promoConfig?.model?.overrides) {
      update['promoConfig.model.overrides'] = data.promoConfig.model.overrides
      doUpdate = true
    }

    if (data.promoConfig?.model?.parameters) {
      update['promoConfig.model.parameters'] = data.promoConfig.model.parameters
      doUpdate = true
    }

    if (data.analyticsConfig?.chartIds) {
      update['analyticsConfig.chartIds'] = data.analyticsConfig.chartIds
      doUpdate = true
    }

    if (data.analyticsConfig?.internalDashboards) {
      update['analyticsConfig.internalDashboards'] =
        data.analyticsConfig.internalDashboards
      doUpdate = true
    }

    if (data.promoConfig?.widget2) {
      update['promoConfig.widget2'] = data.promoConfig.widget2
      doUpdate = true
    }

    if (data.promoConfig?.discount) {
      update['promoConfig.discount'] = data.promoConfig.discount
      doUpdate = true
    }

    if (data.promoConfig?.dialog) {
      update['promoConfig.dialog'] = data.promoConfig.dialog
      doUpdate = true
    }

    if (data.promoConfig?.selectedWidgetId !== undefined) {
      update['promoConfig.selectedWidgetId'] = data.promoConfig.selectedWidgetId
      doUpdate = true
    }

    if (!doUpdate) {
      this.logger.error(
        {
          shop,
        },
        `updateStoreConfig: no data updated`
      )
      return
    }

    update.updatedAt = new Date()

    //Update within a transaction so we make sure that any following read will get the updated value
    await this.firestore.runTransaction(async (transaction) => {
      const docRef = this.firestore
        .collection(shoipifyStoresCollectionPath)
        .doc(storeConfig._id)
      transaction.update(docRef, update)
    })

    await this.invalidateStoreConfigCache(shop!, storeConfig._id)

    this.logger.log(
      {
        shop,
      },
      `updateStoreConfig: done`
    )

    return {}
  }

  async createStoreWidget(
    requester: UserContext | undefined,
    type: WidgetType
  ) {
    const request = this.shopifyService.validateRequester(requester)
    this.logger.log(
      {
        shop: request.shop,
        type,
      },
      `createStoreWidget: started`
    )

    const storeConfig = await this.getStoreConfigByShop(requester, true)

    const newDoc = this.firestore
      .collection(shopifyStoreWidgetsCollectionPath(storeConfig._id))
      .doc()

    const now = new Date()
    const widgetData =
      type === WidgetType.widget3
        ? ({
            storeId: storeConfig._id,
            type: WidgetType.widget3,
            name: 'New Widget',
            createdAt: now,
            updatedAt: now,

            discount: defaultDiscountConfig,
            widget3: defaultWidget3Config,
          } satisfies Omit<ShopifyStoreWidget, '_id'>)
        : ({
            storeId: storeConfig._id,
            type: WidgetType.widget2,
            name: 'New Widget',
            createdAt: now,
            updatedAt: now,

            discount: defaultDiscountConfig,
            widget2: defaultWidget2Config,
          } satisfies Omit<ShopifyStoreWidget, '_id'>)

    await newDoc.create(widgetData)

    await this.invalidateStoreConfigCache(request.shop!, storeConfig._id)

    this.logger.log(
      {
        shop: request.shop,
        widgetId: newDoc.id,
      },
      `createStoreWidget: done`
    )

    return {
      shop: request.shop,
      storeId: storeConfig._id,
      widgetId: newDoc.id,
    }
  }

  async duplicateStoreWidget(
    requester: UserContext | undefined,
    fromWidgetId: string
  ) {
    const request = this.shopifyService.validateRequester(requester)
    this.logger.log(
      {
        shop: request.shop,
        fromWidgetId,
      },
      `duplicateStoreWidget: started`
    )

    const storeConfig = await this.getStoreConfigByShop(requester, true)
    const fromWidget = storeConfig._widgets?.find((w) => w._id === fromWidgetId)

    if (!fromWidget) {
      this.logger.error(
        {
          shop: request.shop,
          fromWidgetId,
        },
        `duplicateStoreWidget: source widget not found`
      )
      throw new NotFoundException('Source widget not found')
    }

    const newDoc = this.firestore
      .collection(shopifyStoreWidgetsCollectionPath(storeConfig._id))
      .doc()

    const now = new Date()
    await newDoc.create({
      storeId: storeConfig._id,
      type: fromWidget.type,
      name: `${fromWidget.name} (copy)`,
      createdAt: now,
      updatedAt: now,

      discount: fromWidget.discount,
      widget2: fromWidget.widget2,
      widget3: fromWidget.widget3,
    } satisfies Omit<ShopifyStoreWidget, '_id'>)

    this.logger.log(
      {
        shop: request.shop,
        fromWidgetId,
        widgetId: newDoc.id,
      },
      `duplicateStoreWidget: done`
    )

    return {
      shop: request.shop,
      storeId: storeConfig._id,
      widgetId: newDoc.id,
    }
  }

  async updateStoreWidget(
    requester: UserContext | undefined,
    widgetId: string,
    data: Partial<ShopifyStoreWidget>
  ) {
    const request = this.shopifyService.validateRequester(requester)
    this.logger.log(
      {
        shop: request.shop,
        widgetId,
      },
      `updateStoreWidget: started`
    )

    const storeConfig = await this.getStoreConfigByShop(requester, true)
    const existingWidget = storeConfig._widgets?.find((w) => w._id === widgetId)
    if (!existingWidget) {
      this.logger.error(
        {
          shop: request.shop,
          widgetId,
        },
        `updateStoreWidget: widget not found`
      )
      throw new NotFoundException('Widget not found')
    }

    if (existingWidget.type === WidgetType.widget2 && data.widget3) {
      this.logger.error(
        {
          shop: request.shop,
          widgetId,
        },
        `updateStoreWidget: widget type mismatch`
      )
      throw new BadRequestException('Widget type mismatch')
    } else if (existingWidget.type === WidgetType.widget3 && data.widget2) {
      this.logger.error(
        {
          shop: request.shop,
          widgetId,
        },
        `updateStoreWidget: widget type mismatch`
      )
      throw new BadRequestException('Widget type mismatch')
    }

    await this.firestore
      .collection(shopifyStoreWidgetsCollectionPath(storeConfig._id))
      .doc(existingWidget._id)
      .update({
        ...data,

        createdAt: existingWidget.createdAt,
        updatedAt: new Date(),
        storeId: storeConfig._id,
        type: existingWidget.type,
      })

    await this.invalidateStoreConfigCache(request.shop!, storeConfig._id)

    this.logger.log(
      {
        shop: request.shop,
      },
      `updateStoreWidget: done`
    )

    return {}
  }

  async deleteStoreWidget(
    requester: UserContext | undefined,
    widgetId: string
  ) {
    const request = this.shopifyService.validateRequester(requester)
    this.logger.log(
      {
        shop: request.shop,
        widgetId,
      },
      `deleteStoreWidget: started`
    )

    const storeConfig = await this.getStoreConfigByShop(requester, true)

    if (storeConfig.promoConfig?.selectedWidgetId === widgetId) {
      this.logger.log(
        {
          shop: request.shop,
          widgetId,
        },
        `deleteStoreWidget: unsetting selectedWidgetId`
      )
      await this.firestore
        .collection(shoipifyStoresCollectionPath)
        .doc(storeConfig._id)
        .update({
          'promoConfig.selectedWidgetId': null,
        })
    }

    await this.firestore
      .collection(shopifyStoreWidgetsCollectionPath(storeConfig._id))
      .doc(widgetId)
      .delete()

    await this.invalidateStoreConfigCache(request.shop!, storeConfig._id)

    this.logger.log(
      {
        shop: request.shop,
        widgetId,
      },
      `deleteStoreWidget: done`
    )

    return {}
  }

  async createLaunchConfig(
    requester: UserContext | undefined,
    data: Omit<
      StoreLaunchConfig,
      '_id' | 'experimentKey' | 'storeId' | 'createdAt'
    >
  ) {
    const request = this.shopifyService.validateRequester(requester)
    return await this.createLaunchConfigInternal(requester, request.shop, data)
  }

  @MurLock(120000, 'shop')
  async createLaunchConfigInternal(
    requester: UserContext | undefined,
    shop: string | undefined,
    data: Omit<
      StoreLaunchConfig,
      '_id' | 'experimentKey' | 'storeId' | 'createdAt'
    >
  ) {
    const request = this.shopifyService.validateRequester(requester)
    this.logger.log({ shop: request.shop }, `createLaunchConfig: started`)

    try {
      const storeConfig = await this.getStoreConfigByShop(requester, false)
      shop = request.shop!

      if (data.configVariants.length < 1) {
        this.logger.error(
          {
            shop: request.shop,
          },
          `createLaunchConfig: no config variants`
        )
        throw new BadRequestException('No config variants')
      }

      const prevLaunchConfig = storeConfig._currentLaunchConfig

      const docRef = this.firestore
        .collection(shopifyStoreLaunchConfigsCollectionPath(storeConfig._id))
        .doc()

      const { apiPostFetch, apiPutFetch } = this.getStatsigConsoleApiFetch()

      let experimentKey: string | undefined
      if (data.configVariants.length > 1) {
        //Create a new Statsig experiment
        experimentKey = crypto.randomUUID()

        const groups: {
          name: string
          size: number
          parameterValues: Record<string, string | number>
        }[] = []
        let totalSize = 0
        data.configVariants.forEach((variant, index) => {
          let size: number
          if (index === data.configVariants.length - 1) {
            size = 100 - totalSize
          } else {
            size = Math.floor(100 / data.configVariants.length)
          }
          totalSize += size

          groups.push({
            name: `config-${index}`,
            size,
            parameterValues: {
              index,
            },
          })
        })

        await apiPostFetch<
          {
            message: string
            data: any
          },
          {
            name: string
            description?: string
            tags?: string[]
            hypothesis: string
            primaryMetrics: {
              name: string
              type: string
            }[]

            secondaryMetrics: {
              name: string
              type: string
            }[]

            groups: {
              name: string
              size: number
              parameterValues: Record<string, string | number>
            }[]
          }
        >([
          `/v1/experiments`,
          {
            name: experimentKey,
            description: `${shop} internal experiment (launchConfigId: ${docRef.id})`,
            tags: ['auto-created', 'launch-config'],
            hypothesis: 'IntentNow promos will boost the sales!',
            primaryMetrics: [
              {
                name: 'checkout_conversion_rate',
                type: 'funnel',
              },
              {
                name: 'revenue_per_user',
                type: 'composite',
              },
            ],
            secondaryMetrics: [
              {
                name: 'dau',
                type: 'user',
              },
              {
                name: 'wau',
                type: 'user',
              },
            ],
            groups,
          },
        ])

        //Start the experiment
        await apiPutFetch([`/v1/experiments/${experimentKey}/start`, {}])
      }

      const now = new Date()
      const newConfig = {
        storeId: storeConfig._id,
        createdAt: now,
        name: data.name,
        experimentKey,
        configVariants: data.configVariants,
      }

      await docRef.create(newConfig)

      //Stop the previous experiment if it exists
      if (prevLaunchConfig?.experimentKey) {
        await apiPutFetch([
          `/v1/experiments/${prevLaunchConfig.experimentKey}/abandon`,
          {
            decisionReason: 'replaced by new launch config',
          },
        ])

        await apiPutFetch([
          `/v1/experiments/${prevLaunchConfig.experimentKey}/archive`,
          {
            decisionReason: 'replaced by new launch config',
          },
        ])
      }

      await this.invalidateStoreConfigCache(shop, storeConfig._id)

      this.logger.log({ shop: request.shop }, `createLaunchConfig: done`)

      return {
        storeId: storeConfig._id,
        launchConfigId: docRef.id,
      }
    } catch (err) {
      this.logger.error(
        {
          shop: request.shop,
          err,
        },
        `createLaunchConfig: failed`
      )
      throw err
    }
  }

  private async getCurrentLaunchConfigByStoreId(storeId: string) {
    const snap = await this.firestore
      .collection(shopifyStoreLaunchConfigsCollectionPath(storeId))
      .orderBy('createdAt', 'desc')
      .limit(1)
      .get()

    if (snap.empty) {
      return undefined
    }

    const internalExperiment = {
      ...snap.docs[0].data(),
      _id: snap.docs[0].id,
    } as StoreLaunchConfig
    mapTimestampFieldsToDate(internalExperiment, ['createdAt'])

    return internalExperiment
  }

  async getStoreLaunchConfigs(requester: UserContext | undefined) {
    //TODO: add pagination
    const request = this.shopifyService.validateRequester(requester)
    this.logger.log(
      {
        shop: request.shop,
      },
      `getStoreLaunchConfigs: started`
    )

    const storeConfig = await this.getStoreConfigByShop(requester, false)

    const snap = await this.firestore
      .collection(shopifyStoreLaunchConfigsCollectionPath(storeConfig._id))
      .orderBy('createdAt', 'desc')
      .get()

    const configs = snap.docs.map((doc) => {
      const config = {
        ...doc.data(),
        _id: doc.id,
      } as StoreLaunchConfig
      mapTimestampFieldsToDate(config, ['createdAt'])
      return config
    })

    this.logger.log(
      {
        shop: request.shop,
        configsCount: configs.length,
      },
      `getStoreLaunchConfigs: done`
    )

    return configs
  }

  async migrateWidget2(requester: UserContext | undefined) {
    const request = this.shopifyService.validateRequester(requester)
    this.logger.log(
      {
        shop: request.shop,
      },
      `migrateStoreWidget2: started`
    )

    const storeConfig = await this.getStoreConfigByShop(requester, false)
    if (storeConfig.promoConfig?.widget2) {
      return this.convertWidget2ToWidget3(storeConfig.promoConfig.widget2)
    }

    return {}
  }

  private convertWidget2ToWidget3(widget2: Widget2Config): Widget3Config {
    const dialog = {
      root: {
        props: {
          styles: {
            colors: {
              color: widget2.dialog.mainStyles.color,
              backgroundColor: widget2.dialog.mainStyles.backgroundColor,
            },
            font: {
              name: widget2.dialog.mainStyles.font.family,
              size: widget2.dialog.mainStyles.font.size,
            },
          },
        },
      },
      content: [
        {
          type: 'DialogContainer',
          props: {
            image: {
              imageUrl: `https://api.intentnow.com/cdn/shopify-images/${widget2.dialog.mainImage.path}`,
              desktopImagePostion: widget2.dialog.mainImage.desktopPosition,
              showMobileImage: Boolean(widget2.dialog.mainImage.mobilePosition),
            },
            id: 'DialogContainer-f909ae55-48d6-46fc-808c-395fb6d56db0',
          },
        },
      ],
      zones: {
        'DialogContainer-f909ae55-48d6-46fc-808c-395fb6d56db0:content-panel': [
          {
            type: 'Image',
            props: {
              imageUrl: widget2.dialog.logoImage?.path
                ? `https://api.intentnow.com/cdn/shopify-images/${widget2.dialog.logoImage.path}`
                : '',
              styles: {
                size: {
                  width: widget2.dialog.logoImage?.styles?.size?.width,
                  height: widget2.dialog.logoImage?.styles?.size?.height,
                },
              },
              id: 'Image-eee8a6b4-8736-4584-8e74-6ec91c704d1c',
            },
          },
          ...widget2.dialog.texts.map((text) => ({
            type: 'Text',
            props: {
              text: text.text,
              styles: {
                font: {
                  name: text.styles?.font?.family,
                  size: text.styles?.font?.size,
                },
                box: {
                  paddingTop: text.styles?.box?.paddingTop,
                  paddingBottom: text.styles?.box?.paddingBottom,
                  paddingLeft: text.styles?.box?.paddingLeft,
                  paddingRight: text.styles?.box?.paddingRight,
                },
                align: 'center',
              },
              id: `Text-${crypto.randomUUID()}`,
            },
          })),
          {
            type: 'Container',
            props: {
              styles: {
                border: { style: 'solid', width: 1 },
                box: { paddingTop: 10, paddingBottom: 10 },
              },
              id: 'Container-57726378-d1d5-466e-ac78-5b9a33d3a50c',
            },
          },
          {
            type: 'Button',
            props: {
              text: widget2.dialog.copyCodeBlock.cancelText.text,
              styles: {
                variant: 'link',
                box: {
                  paddingTop:
                    widget2.dialog.copyCodeBlock.cancelText.styles?.box
                      ?.paddingTop,
                  paddingBottom:
                    widget2.dialog.copyCodeBlock.cancelText.styles?.box
                      ?.paddingBottom,
                  paddingLeft:
                    widget2.dialog.copyCodeBlock.cancelText.styles?.box
                      ?.paddingLeft,
                  paddingRight:
                    widget2.dialog.copyCodeBlock.cancelText.styles?.box
                      ?.paddingRight,
                },
              },
              id: 'Button-e5997530-611f-4fa5-924f-baa00bb9c70a',
              action: { action: 'minimize-dialog' },
            },
          },
        ],
        'Container-57726378-d1d5-466e-ac78-5b9a33d3a50c:content-zone': [
          {
            type: 'Text',
            props: {
              text: '{{discountCode}}',
              styles: {
                align: 'center',
                font: {
                  name: widget2.dialog.copyCodeBlock.codeTextStyles?.font
                    ?.family,
                  size: widget2.dialog.copyCodeBlock.codeTextStyles?.font?.size,
                },
                box: { marginTop: 0, marginBottom: 10 },
              },
              id: 'Text-49836466-119f-4d41-b725-5dc632d8c962',
            },
          },
          {
            type: 'Button',
            props: {
              text: 'COPY CODE',
              styles: {
                colors: {
                  color: widget2.dialog.copyCodeBlock.copyButton.styles?.color,
                  backgroundColor:
                    widget2.dialog.copyCodeBlock.copyButton.styles
                      ?.backgroundColor,
                  hoverBackgroundColor: 'gray',
                },
                box: {
                  paddingTop:
                    widget2.dialog.copyCodeBlock.copyButton.styles?.box
                      ?.paddingTop,
                  paddingBottom:
                    widget2.dialog.copyCodeBlock.copyButton.styles?.box
                      ?.paddingBottom,
                  paddingLeft:
                    widget2.dialog.copyCodeBlock.copyButton.styles?.box
                      ?.paddingLeft,
                  paddingRight:
                    widget2.dialog.copyCodeBlock.copyButton.styles?.box
                      ?.paddingRight,
                },
                size: {
                  width:
                    widget2.dialog.copyCodeBlock.copyButton.styles?.size?.width,
                  height:
                    widget2.dialog.copyCodeBlock.copyButton.styles?.size
                      ?.height,
                },
                variant: 'default',
              },
              id: 'Button-e9b8f39c-feb0-4b89-b472-498aa83a60d5',
              action: { action: 'copy-code' },
            },
          },
          ...(widget2.dialog.copyCodeBlock.texts?.map((text) => ({
            type: 'Text',
            props: {
              text: text.text,
              styles: {
                font: {
                  name: text.styles?.font?.family,
                  size: text.styles?.font?.size,
                },
                box: {
                  paddingTop: text.styles?.box?.paddingTop,
                  paddingBottom: text.styles?.box?.paddingBottom,
                  paddingLeft: text.styles?.box?.paddingLeft,
                  paddingRight: text.styles?.box?.paddingRight,
                },
              },
              id: `Text-${crypto.randomUUID()}`,
            },
          })) ?? []),
        ],
      },
    }

    const teaser = {
      root: {
        props: {
          styles: {
            colors: {
              color: widget2.teaser.mainStyles.color,
              backgroundColor: widget2.teaser.mainStyles.backgroundColor,
            },
            font: {
              name: widget2.teaser.mainStyles.font.family,
              size: widget2.teaser.mainStyles.font.size,
            },
          },
          position: {
            verticalGap: widget2.teaser.position?.verticalGap,
            horizontalGap: widget2.teaser.position?.horizontalGap,
          },
        },
      },
      content: [
        ...widget2.teaser.texts.map((text) => ({
          type: 'Text',
          props: {
            text: text.text,
            styles: {
              align: 'center',
              font: {
                name: text.styles?.font?.family,
                size: text.styles?.font?.size,
              },
              box: {
                paddingTop: text.styles?.box?.paddingTop,
                paddingBottom: text.styles?.box?.paddingBottom,
                paddingLeft: text.styles?.box?.paddingLeft,
                paddingRight: text.styles?.box?.paddingRight,
              },
            },
            id: `Text-${crypto.randomUUID()}`,
          },
        })),
      ],
      zones: {},
    }

    return {
      dialog,
      teaser,
    }
  }

  async uploadStoreImage(
    requester: UserContext | undefined,
    imageName: string | undefined,
    imageFile: Express.Multer.File
  ) {
    const request = this.shopifyService.validateRequester(requester)
    const shop = request.shop

    if (!imageName) {
      imageName = imageFile.originalname
    }

    this.logger.log(
      {
        shop,
        imageName,
      },
      `uploadImage: started`
    )

    //Make sure that the store config exists
    const storeConfig = await this.getStoreConfigByShop(requester)

    const ext = path.extname(imageFile.originalname)

    const uploadName = `${crypto.randomUUID()}${ext}`
    const uploadPath = `${this.intentowConfig.imageStorage.uploadPath}/${uploadName}`

    const stream = Readable.from(imageFile.buffer)
    await this.storageService.getDisk('gcs').put(uploadPath, stream)

    const imageRecord: Omit<ShopifyStoreImage, '_id'> = {
      storeId: storeConfig._id,
      name: imageName,
      fileInfo: {
        filename: imageFile.originalname,
        mimetype: imageFile.mimetype,
        size: imageFile.size,
        ext,
      },
      createdAt: new Date(),
      uploadInfo: {
        cdnBucket: this.intentowConfig.imageStorage.cdnBucket,
        path: uploadPath,
      },
      imageUrl: `${this.intentowConfig.imageStorage.imageUrlPrefix}${uploadPath}`,
    }

    const imageDoc = this.firestore
      .collection(shopifyStoreImagesCollectionPath(storeConfig._id))
      .doc()
    await imageDoc.create(imageRecord)

    this.logger.log(
      {
        shop,
        imageName,
        imageId: imageDoc.id,
      },
      `uploadImage: done`
    )

    return {
      shop,
      storeId: storeConfig._id,
      imageId: imageDoc.id,
    }
  }

  async getStoreImages(requester: UserContext | undefined) {
    //TODO: add pagination later
    const request = this.shopifyService.validateRequester(requester)
    this.logger.log(
      {
        shop: request.shop,
      },
      `getImages: started`
    )

    const storeConfig = await this.getStoreConfigByShop(requester, true)
    //read all data from shopifyStoreImagesCollectionPath
    const imagesSnap = await this.firestore
      .collection(shopifyStoreImagesCollectionPath(storeConfig._id))
      .orderBy('createdAt', 'desc')
      .get()
    const images = imagesSnap.docs.map((doc) => {
      const image = {
        ...doc.data(),
        _id: doc.id,
      } as ShopifyStoreImage
      mapTimestampFieldsToDate(image, ['createdAt'])
      return image
    })

    this.logger.log(
      {
        shop: request.shop,
        imagesCount: images.length,
      },
      `getImages: done`
    )

    return images
  }

  async deleteStoreImage(requester: UserContext | undefined, imageId: string) {
    const request = this.shopifyService.validateRequester(requester)
    this.logger.log(
      {
        shop: request.shop,
        imageId,
      },
      `deleteStoreImage: started`
    )

    const storeConfig = await this.getStoreConfigByShop(requester, true)
    const imageSnap = await this.firestore
      .collection(shopifyStoreImagesCollectionPath(storeConfig._id))
      .doc(imageId)
      .get()

    if (imageSnap.exists) {
      const image = {
        ...imageSnap.data(),
        _id: imageSnap.id,
      } as ShopifyStoreImage
      mapTimestampFieldsToDate(image, ['createdAt'])

      await this.firestore
        .collection(shopifyStoreImagesCollectionPath(storeConfig._id))
        .doc(imageId)
        .delete()

      //Also delete the image file from GCP
      if (image.uploadInfo) {
        this.logger.log(
          {
            shop: request.shop,
            imageId,
            path: image.uploadInfo.path,
          },
          `deleteStoreImage: deleting image file from storage`
        )
        await this.storageService.getDisk('gcs').delete(image.uploadInfo.path)
      }
    } else {
      this.logger.error(
        {
          shop: request.shop,
          imageId,
        },
        `deleteStoreImage: image not found`
      )
      throw new NotFoundException('Image not found')
    }

    this.logger.log(
      {
        shop: request.shop,
        imageId,
      },
      `deleteStoreImage: done`
    )

    return {}
  }

  //Admin only
  async getAllStoreConfigs(requester: UserContext | undefined) {
    validateIsAdmin(requester)

    const snap = await this.firestore
      .collection(shoipifyStoresCollectionPath)
      .orderBy('shop')
      .get()

    const storeConfigs = snap.docs
      .map((x) => {
        const storeConfig = {
          ...x.data(),
          _id: x.id,
        } as ShopifyStore
        mapTimestampFieldsToDate(storeConfig, ['createdAt', 'updatedAt'])
        return storeConfig
      })
      .filter((x) => !x.disabled)

    return storeConfigs
  }

  //Admin only
  async initializeStoreConfig(
    requester: UserContext | undefined,
    name: string,
    website: string,
    fromShop: string | undefined
  ) {
    const request = this.shopifyService.validateRequester(requester, true)
    const shop = request.shop!

    this.logger.log(
      {
        shop,
        fromShop,
      },
      `initializeStoreConfig: started`
    )

    const { storeId } = await this.firestore.runTransaction(
      async (transaction) => {
        const storeSnap = await transaction.get(
          this.firestore
            .collection(shoipifyStoresCollectionPath)
            .where('shop', '==', shop)
            .limit(1)
        )

        if (!storeSnap.empty) {
          this.logger.warn(
            { shop },
            'initializeStoreConfig: store config already exists'
          )
          return {}
        }

        let promoConfig: any = {
          model: {},
        }
        if (fromShop) {
          const fromStoreSnap = await transaction.get(
            this.firestore
              .collection(shoipifyStoresCollectionPath)
              .where('shop', '==', fromShop)
              .limit(1)
          )

          if (fromStoreSnap.empty) {
            this.logger.error(
              { shop, fromShop },
              `initializeStoreConfig: fromShop doesn't exist`
            )
            throw new BadRequestException(`fromShop doesn't exist`)
          }

          promoConfig = {
            ...fromStoreSnap.docs[0].data().promoConfig,
            selectedWidgetId: undefined,
          }
        }

        const newStoreConfig: Omit<ShopifyStore, '_id'> = {
          createdAt: new Date(),
          appHandle: request.appHandle,
          promoConfig,
          shop,
          name,
          website,
          analyticsConfig: {},
          promoConfigVariants: [],
        }

        const newDoc = this.firestore
          .collection(shoipifyStoresCollectionPath)
          .doc()
        transaction.create(newDoc, newStoreConfig)

        return {
          storeId: newDoc.id,
        }
      }
    )

    await this.invalidateStoreConfigCache(request.shop!, storeId)

    this.logger.log(
      { shop, storeId },
      `initializeStoreConfig: new store config created`
    )
    return {
      storeId,
    }
  }

  //Admin only
  async getFeatureSettings(requester: UserContext | undefined) {
    const request = this.shopifyService.validateRequester(requester, true)
    const shop = request.shop!
    const { storeConfigPrefix, storeConfigKey } = this.getStatsigKeys(shop)

    const { apiFetch } = this.getStatsigConsoleApiFetch()

    const storeConfig = await apiFetch<{
      data: {
        id: string
        name: string
        isEnabled: boolean
        defaultValue: any
      }
    }>(`/v1/dynamic_configs/${storeConfigKey}`)

    const modelExperimentKeyPostfix =
      storeConfig.data.defaultValue.modelExperimentKeyPostfix || 'model-exp'
    const modelExperimentKey = `${storeConfigPrefix}_${modelExperimentKeyPostfix}`
    const modelExperiment = await apiFetch<{
      data: {
        id: string
        name: string
        status: string
        groups: {
          id: string
          name: string
          size: number
        }[]
      }
    }>(`/v1/experiments/${modelExperimentKey}`)

    const storeAdminConfig = Statsig.getConfig(
      {
        userID: shop,
      },
      'shopify-admin-app-config'
    )

    return {
      storeConfig: {
        id: storeConfig.data.id,
        name: storeConfig.data.name,
        isEnabled: storeConfig.data.isEnabled,
        defaultValue: storeConfig.data.defaultValue,
      },
      modelExperiment: {
        id: modelExperiment.data.id,
        name: modelExperiment.data.name,
        status: modelExperiment.data.status,
        groups: modelExperiment.data.groups,
      },
      storeAdminConfig: storeAdminConfig.value,
    }
  }

  //Admin only
  async initializeStore(
    requester: UserContext | undefined,
    name: string,
    website: string,
    fromShop: string | undefined
  ) {
    const request = this.shopifyService.validateRequester(requester, true)
    const shop = request.shop!

    this.logger.log({ shop, fromShop }, `initializeStore: started`)

    //Create new store config
    const { storeId } = await this.initializeStoreConfig(
      requester,
      name,
      website,
      fromShop
    )

    const { apiFetch, apiPostFetch } = this.getStatsigConsoleApiFetch()

    const { storeConfigKey, storeConfigPrefix } = this.getStatsigKeys(shop)
    const modelExperimentKey = `${storeConfigPrefix}_model-exp`

    //Create statsig experiment
    let createExperiment = true
    try {
      const modelExperiment = await apiFetch<{
        message: string
        data: any
      }>(`/v1/experiments/${modelExperimentKey}`)

      if (modelExperiment.data) {
        createExperiment = false
      }
    } catch (e) {
      createExperiment = true
    }

    if (createExperiment) {
      this.logger.log(
        { shop, modelExperimentKey },
        `initializeStore: start creating new experiment`
      )
      await apiPostFetch<
        {
          message: string
          data: any
        },
        {
          name: string
          description?: string
          hypothesis: string
          primaryMetrics: {
            name: string
            type: string
          }[]

          secondaryMetrics: {
            name: string
            type: string
          }[]

          groups: {
            name: string
            size: number
            parameterValues: Record<string, string>
          }[]
        }
      >([
        `/v1/experiments`,
        {
          name: modelExperimentKey,
          description: `${shop} experiment`,
          hypothesis: 'IntentNow promos will boost the sales!',
          primaryMetrics: [
            {
              name: 'checkout_conversion_rate',
              type: 'funnel',
            },
            {
              name: 'revenue_per_user',
              type: 'composite',
            },
          ],
          secondaryMetrics: [
            {
              name: 'dau',
              type: 'user',
            },
            {
              name: 'wau',
              type: 'user',
            },
          ],
          groups: [
            {
              name: 'control',
              size: 50,
              parameterValues: {
                split: 'control',
              },
            },
            {
              name: 'test',
              size: 50,
              parameterValues: {
                split: 'test',
              },
            },
            {
              name: 'demo',
              size: 0,
              parameterValues: {
                split: 'demo',
              },
            },
          ],
        },
      ])
    } else {
      this.logger.log(
        { shop, modelExperimentKey },
        `initializeStore: skip creating experiment`
      )
    }

    //Create statisg dynamic config
    let createDynamicConfig = true
    try {
      const dynamicConfig = await apiFetch<{
        message: string
        data: {
          id: string
        }
      }>(`/v1/dynamic_configs/${storeConfigKey}`)

      if (dynamicConfig.data) {
        createDynamicConfig = false
      }
    } catch (e) {
      createDynamicConfig = true
    }

    if (createDynamicConfig) {
      this.logger.log(
        { shop, storeConfigKey },
        `initializeStore: start creating new dynamic config`
      )
      await apiPostFetch<
        {
          message: string
          data: {
            id: string
          }
        },
        {
          name: string
          isEnabled?: boolean
          description?: string
          rules: {
            name: string
            passPercentage: number
            conditions: {
              type: string
              targetValue: string
            }[]
            returnValue: Record<string, string | number | boolean>
          }[]
          defaultValue: Record<string, string | number | boolean>
        }
      >([
        `/v1/dynamic_configs`,
        {
          name: storeConfigKey,
          isEnabled: false,
          description: `${shop} dynamic config`,
          rules: [
            {
              name: 'Internal Test Users',
              passPercentage: 100,
              conditions: [
                {
                  type: 'passes_segment',
                  targetValue: 'internal_test_users',
                },
              ],
              returnValue: {
                predictWithEvents: false,
                getDiscount: true,
                sendStatsigEvents: true,
                sendGaEvents: false,
                sendAmplitudeEvents: true,
                modelSplitOverride: 'demo',
              },
            },
          ],
          defaultValue: {
            predictWithEvents: false,
            getDiscount: false,
            sendStatsigEvents: true,
            sendAmplitudeEvents: true,
          },
        },
      ])
    } else {
      this.logger.log(
        { shop, storeConfigKey },
        `initializeStore: skip creating dynamic config`
      )
    }

    this.logger.log({ shop, fromShop }, `initializeStore: done`)

    return {
      shop,
      storeIdCreated: storeId,
      experimentCreated: createExperiment ? modelExperimentKey : undefined,
      dynamicConfigCreated: createDynamicConfig ? storeConfigKey : undefined,
    }
  }

  private getStatsigConsoleApiFetch() {
    return getApiFetch(
      this.statsigConfig.consoleApi.baseUrl,
      undefined,
      this.logger,
      {
        [`STATSIG-API-KEY`]: this.statsigConfig.consoleApi.token,
        [`STATSIG-API-VERSION`]: this.statsigConfig.consoleApi.version,
      }
    )
  }
}
