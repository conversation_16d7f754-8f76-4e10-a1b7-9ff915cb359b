import {
  Inject,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common'
import { ShopifyService } from 'src/shopify/shopify.service'
import {
  CheckCodeDiscountByCodeQuery,
  CheckCodeDiscountByCodeQueryVariables,
  DiscountCodeBasicCreateMutation,
  DiscountCodeBasicCreateMutationVariables,
} from 'src/types/shopify-api/admin.generated'
import { ConfigService } from '@nestjs/config'
import { ServerConfig } from 'src/config/config'
import {
  DiscountCodeBasicInput,
  DiscountStatus,
} from 'src/types/shopify-api/admin.types'
import { customAlphabet } from 'nanoid'
import { FieldValue, Firestore, UpdateData } from '@google-cloud/firestore'
import {
  DiscountConfig,
  ModelParameters,
  ShopifyStoreClient,
  shopifyStoreClientCollectionPath,
  ShopifyStoreGeneratedDiscount,
  shopifyStoreGeneratedDiscountsCollectionPath,
} from '@packages/shared-entities'
import {
  DialogContentDto,
  DiscountContentDto,
  Widget2ConfigDto,
  Widget3ConfigDto,
} from '../dto/widget.dto'
import { mapTimestampFieldsToDate } from 'src/common/utils'
import { getApiFetch } from 'src/common/fetch'
import { Model } from 'mongoose'
import { InjectModel } from '@nestjs/mongoose'
import { Event } from './entities/event.mongo'
import { DataPageInfo } from '@packages/shared-entities'
import { pagedFirestoreDataReader } from 'src/common/firestore-data-helper'
import { Counter } from '@opentelemetry/api'
import { MetricService } from 'nestjs-otel'
import { StoreConfigService } from './store-config.service'
import { UserContext } from 'src/auth/entities/user-context'

@Injectable()
export class GetDiscountService {
  logger = new Logger(GetDiscountService.name)
  private readonly intentowConfig: ServerConfig['intentnow']
  private readonly modelApiPostFetch: ReturnType<
    typeof getApiFetch
  >['apiPostFetch']

  private readonly createDiscountCounter: Counter
  private readonly callModelCounter: Counter

  constructor(
    configService: ConfigService,
    private readonly shopifyService: ShopifyService,
    private readonly metricService: MetricService,
    private readonly storeConfigService: StoreConfigService,
    @Inject('FIRESTORE_CLIENT') private readonly firestore: Firestore,
    @InjectModel(Event.name) private eventModel: Model<Event>
  ) {
    this.intentowConfig =
      configService.get<ServerConfig['intentnow']>('intentnow')!

    const { apiPostFetch } = getApiFetch(
      this.intentowConfig.modelApi.baseUrl,
      this.intentowConfig.modelApi.token,
      this.logger
    )
    this.modelApiPostFetch = apiPostFetch
    this.createDiscountCounter = this.metricService.getCounter(
      'discount_created',
      {
        prefix: 'intentnow_server',
      }
    )
    this.callModelCounter = this.metricService.getCounter('model_called', {
      prefix: 'intentnow_server',
    })
  }

  async getDiscountAndWidgetPreview(
    requester: UserContext | undefined,
    previewWidgetId: string | undefined
  ): Promise<{
    preview: boolean
    dialog?: DialogContentDto
    discount?: DiscountContentDto
    widgetId?: string
    widget2?: Widget2ConfigDto
    widget3?: Widget3ConfigDto
  }> {
    const request = this.shopifyService.validateRequester(requester)
    const shop = request.shop!
    this.logger.log(
      {
        shop,
      },
      `getDiscountAndWidgetPreview: started`
    )

    if (previewWidgetId) {
      const storeConfig = await this.storeConfigService.getStoreConfigByShop(
        requester,
        true
      )

      let widgetConfig:
        | {
            discount: DiscountConfig
            widget2: Widget2ConfigDto
          }
        | {
            discount: DiscountConfig
            widget3: Widget3ConfigDto
          }
        | undefined

      const matchedWidget = storeConfig._widgets?.find(
        (w) => w._id === previewWidgetId
      )
      if (matchedWidget?.discount) {
        if (matchedWidget?.widget2) {
          widgetConfig = {
            discount: matchedWidget.discount,
            widget2: matchedWidget.widget2,
          }
        } else if (matchedWidget?.widget3) {
          widgetConfig = {
            discount: matchedWidget.discount,
            widget3: matchedWidget.widget3,
          }
        }
      }

      if (widgetConfig) {
        return {
          preview: true,
          discount: this.generatePreviewDiscount(widgetConfig.discount),
          widgetId: matchedWidget?._id,
          widget2: (widgetConfig as any).widget2,
          widget3: (widgetConfig as any).widget3,
        }
      } else {
        this.logger.error(
          {
            shop,
            previewWidgetId,
          },
          `getDiscountAndWidgetPreview: widget not found`
        )
        throw new NotFoundException('Preview widget not found')
      }
    } else {
      //No preview widget id given, return based on the active config
      const { configVariant } =
        await this.storeConfigService.getStoreConfigForGetDiscountV2(
          shop,
          '',
          true,
          true
        )

      if (configVariant) {
        return {
          preview: true,
          discount: this.generatePreviewDiscount(configVariant.discountConfig),
          widgetId: configVariant.widget.widgetId,
          dialog: configVariant.widget.dialog,
          widget2: configVariant.widget.widget2,
          widget3: configVariant.widget.widget3,
        }
      } else {
        this.logger.error(
          {
            shop,
          },
          `getDiscountAndWidgetPreview: launch config variant not found`
        )
        throw new NotFoundException('Preview launch config variant not found')
      }
    }
  }

  private generatePreviewDiscount(discountConfig: DiscountConfig) {
    const prefix = discountConfig.codeGen.prefix ?? ''
    return {
      title: discountConfig.discountInput.title!,
      code: `${prefix}${discountConfig.codeGen.alphabet.slice(
        0,
        discountConfig.codeGen.length - prefix.length
      )}`,
      startsAt: new Date(),
      endsAt: new Date(),
    }
  }

  async getDiscountAndWidgetV2(
    requester: UserContext | undefined,
    clientId: string,
    modelSplit: string,
    modelExperiment: string
  ): Promise<{
    dialog?: DialogContentDto
    discount?: DiscountContentDto
    widgetId?: string
    widget2?: Widget2ConfigDto
    widget3?: Widget3ConfigDto
    promoCandidate?: boolean
    configName?: string
    configVariantIndex: number
  }> {
    const request = this.shopifyService.validateRequester(requester)
    const shop = request.shop!
    this.logger.log(
      {
        shop,
        clientId,
        modelSplit,
        modelExperiment,
      },
      `getDiscountAndWidgetV2: started`
    )

    const {
      configVariantIndex,
      configVariant,
      modelOverrides,
      activeShopifyStoreConfig,
    } = await this.storeConfigService.getStoreConfigForGetDiscountV2(
      shop,
      clientId
    )
    const configName = activeShopifyStoreConfig.currentLaunchConfig?.name
    const configCreatedAt =
      activeShopifyStoreConfig.currentLaunchConfig?.createdAt

    const testClients = modelOverrides?.clientOverrides

    try {
      const lastCheckoutEvent = await this.eventModel.findOne(
        {
          clientId,
          shop,
          name: 'checkout_completed',
        },
        undefined,
        {
          sort: {
            serverTimestamp: -1,
          },
        }
      )
      if (lastCheckoutEvent?.serverTimestamp) {
        //Don't show the promo if the user has checked out in the last 3 hours
        const now = new Date()
        if (
          now.getTime() <
          lastCheckoutEvent.serverTimestamp.getTime() + 1000 * 60 * 60 * 3
        ) {
          this.logger.log(`getDiscountAndWidget: last checkout is too recent`)
          return {
            configName,
            configVariantIndex,
          }
        }
      }
    } catch (error) {
      this.logger.error(
        error,
        `getDiscountAndWidget: failed to check last checkout event`
      )
      return {
        configName,
        configVariantIndex,
      }
    }

    let modelPromo = false
    let promoCandidate = false
    try {
      const { promo, promo_candidate } = await this.getModel(
        shop,
        clientId,
        modelSplit,
        modelExperiment,
        configVariant.modelConfig.parameters
      )
      modelPromo = promo ?? false
      promoCandidate = promo_candidate ?? false
      this.logger.log(`getDiscountAndWidget: model returns promo=${modelPromo}`)
    } catch (error) {
      this.logger.error(
        {
          shop,
          clientId,
          modelSplit,
          error,
        },
        `getDiscountAndWidget: failed to call model from model service`
      )
      modelPromo = false
    }

    if (testClients?.[clientId]) {
      modelPromo = testClients[clientId]!.promo
      this.logger.log(
        `getDiscountAndWidget: test client overrides, promo=${modelPromo}`
      )
    } else if (modelOverrides?.overrides) {
      modelPromo = modelOverrides.overrides.promo
      this.logger.log(
        `getDiscountAndWidget: promoConfig overrides, promo=${modelPromo}`
      )
    }

    const discountConfig = configVariant.discountConfig
    const widgetId = configVariant.widget.widgetId
    const widget2 = configVariant.widget.widget2
    const widget3 = configVariant.widget.widget3
    const dialog = configVariant.widget.dialog

    if (!dialog && !widget2 && !widget3) {
      this.logger.error(
        { shop },
        `getDiscountAndWidget: Widget or dialog config not found`
      )
      throw new InternalServerErrorException(
        'Widget or dialog config not found'
      )
    }

    if (modelPromo) {
      //Check if we have a cached discount and create a new one if not
      const { discount } = await this.getCachedPromo2(
        requester,
        activeShopifyStoreConfig.storeId,
        configVariant.widget.widgetId,
        discountConfig,
        clientId,
        true,
        configCreatedAt
      )

      if (discount) {
        this.logger.log(`getDiscountAndWidget: returning a discount`)
        return {
          dialog: dialog
            ? {
                ...dialog,
                title: dialog.title ?? discount.title,
              }
            : undefined,
          discount,
          widgetId,
          widget2,
          widget3,
          promoCandidate,
          configName,
          configVariantIndex,
        }
      }
    } else {
      //Check if we have a cached discount
      const { discount } = await this.getCachedPromo2(
        requester,
        activeShopifyStoreConfig.storeId,
        configVariant.widget.widgetId,
        discountConfig,
        clientId,
        false,
        configCreatedAt
      )

      if (discount) {
        this.logger.log(
          `getDiscountAndWidget: returning a cached discount regardless of the model`
        )
        return {
          dialog: dialog
            ? {
                ...dialog,
                title: dialog.title ?? discount.title,
              }
            : undefined,
          discount,
          widgetId,
          widget2,
          widget3,
          promoCandidate,
          configName,
          configVariantIndex,
        }
      }
    }

    this.logger.log(`getDiscountAndWidget: no discount returned`)
    return {
      promoCandidate,
      configName,
      configVariantIndex,
    }
  }

  async runPrediction(
    requester: UserContext | undefined,
    clientId: string,
    modelSplit: string,
    modelExperiment: string
  ) {
    const request = this.shopifyService.validateRequester(requester)
    const shop = request.shop
    this.logger.log(
      {
        shop,
        clientId,
        modelSplit,
        modelExperiment,
      },
      `runPrediction: started`
    )

    const { activeShopifyStoreConfig, configVariant, configVariantIndex } =
      await this.storeConfigService.getStoreConfigForGetDiscountV2(
        shop,
        clientId
      )
    const configName = activeShopifyStoreConfig.currentLaunchConfig?.name

    const { promo, promo_candidate } = await this.getModel(
      shop,
      clientId,
      modelSplit,
      modelExperiment,
      configVariant.modelConfig.parameters
    )

    this.logger.log(
      {
        shop,
        clientId,
      },
      `runPrediction: ended`
    )

    return {
      configName,
      configVariantIndex,
      promo: promo ?? false,
      promoCandidate: promo_candidate ?? false,
    }
  }

  async createShopifyDiscountCode(
    requester: UserContext | undefined,
    discountCodeInput: DiscountCodeBasicInput
  ) {
    const request = this.shopifyService.validateRequester(requester)
    const query = `#graphql
      mutation discountCodeBasicCreate($basicCodeDiscount: DiscountCodeBasicInput!) {
        discountCodeBasicCreate(basicCodeDiscount: $basicCodeDiscount) {
          codeDiscountNode {
            id
            codeDiscount {
              ... on DiscountCodeBasic {
                title
                codes(first: 10) {
                  nodes {
                    id
                    code
                  }
                }
                shareableUrls {
                  url
                }
                startsAt
                endsAt
                customerSelection {
                  ... on DiscountCustomerAll {
                    allCustomers
                  }
                }
                customerGets {
                  value {
                    ... on DiscountPercentage {
                      percentage
                    }
                  }
                  items {
                    ... on AllDiscountItems {
                      allItems
                    }
                  }
                }
                appliesOncePerCustomer
              }
            }
          }
          userErrors {
            field
            code
            message
          }
        }
      }`
    const variables = {
      basicCodeDiscount: discountCodeInput,
    }

    const response = await this.shopifyService.executeGraphQl<
      DiscountCodeBasicCreateMutation,
      DiscountCodeBasicCreateMutationVariables
    >(requester, {
      query,
      variables,
    })

    if (response.data?.discountCodeBasicCreate?.userErrors.length) {
      this.logger.error(
        {
          errors: response.data.discountCodeBasicCreate.userErrors,
        },
        `Failed to create discount code`
      )
      throw new Error(`Failed to create discount code`)
    }

    this.createDiscountCounter.add(1, {
      shop: request.shop,
    })

    return response.data
  }

  async syncShopifyDiscountStatuses(requester: UserContext | undefined) {
    const request = this.shopifyService.validateRequester(requester)
    const shop = request.shop!
    const { shopifyStoreId } =
      (await this.storeConfigService.getStoreConfigForGetDiscount(shop)) ?? {}

    if (!shopifyStoreId) {
      throw new NotFoundException('Shopify store config not found')
    }

    //TODO: add a time window to limit the number of discounts we sync
    const snap = await this.firestore
      .collection(shopifyStoreGeneratedDiscountsCollectionPath(shopifyStoreId))
      .get()

    for (const doc of snap.docs) {
      const generatedDiscount = {
        ...doc.data(),
        _id: doc.id,
      } as ShopifyStoreGeneratedDiscount
      const { discountStatus } = await this.checkShopifyDiscountCode(
        requester,
        generatedDiscount.discount.code
      )
      if (discountStatus) {
        await this.firestore
          .collection(
            shopifyStoreGeneratedDiscountsCollectionPath(shopifyStoreId)
          )
          .doc(generatedDiscount._id)
          .update({
            discountStatus: {
              syncedAt: new Date(),
              status: discountStatus.status,
              usageCount: discountStatus.usageCount,
            },
          } satisfies UpdateData<ShopifyStoreGeneratedDiscount>)
      }
    }
  }

  async checkShopifyDiscountCode(
    requester: UserContext | undefined,
    code: string
  ) {
    const query = `#graphql
      query checkCodeDiscountByCode($code: String!) {
        codeDiscountNodeByCode(code: $code) {
          codeDiscount {
            __typename
            ... on DiscountCodeBasic {
              createdAt
              asyncUsageCount
              status
            }
          }
        }
      }`

    const variables = {
      code,
    }
    const response = await this.shopifyService.executeGraphQl<
      CheckCodeDiscountByCodeQuery,
      CheckCodeDiscountByCodeQueryVariables
    >(requester, {
      query,
      variables,
    })

    let isValid = false
    let discountStatus:
      | {
          status: string
          usageCount: number
        }
      | undefined
    if (response.data?.codeDiscountNodeByCode?.codeDiscount) {
      if (
        response.data.codeDiscountNodeByCode.codeDiscount.__typename ===
        'DiscountCodeBasic'
      ) {
        discountStatus = {
          status: response.data.codeDiscountNodeByCode.codeDiscount.status,
          usageCount:
            response.data.codeDiscountNodeByCode.codeDiscount.asyncUsageCount,
        }
        isValid =
          response.data.codeDiscountNodeByCode.codeDiscount.status ===
            DiscountStatus.Active &&
          response.data.codeDiscountNodeByCode.codeDiscount.asyncUsageCount <= 0
      }
    }
    return {
      isValid,
      discountStatus,
    }
  }

  async findStoredDiscountByCode(
    shop: string,
    clientId: string,
    code: string
  ): Promise<{
    storedDiscount?: ShopifyStoreGeneratedDiscount
  }> {
    this.logger.log(
      {
        shop,
        code,
        clientId,
      },
      `findStoredDiscountByCode: started`
    )

    const { shopifyStoreId, promoConfig } =
      (await this.storeConfigService.getStoreConfigForGetDiscount(shop)) ?? {}

    if (!shopifyStoreId || !promoConfig) {
      this.logger.warn(
        { shop },
        `findStoredDiscountByCode: no promo config found`
      )
      return {}
    }

    const snap = await this.firestore
      .collection(shopifyStoreGeneratedDiscountsCollectionPath(shopifyStoreId))
      .where('discount.code', '==', code)
      .orderBy('createdAt', 'desc')
      .limit(1)
      .get()

    if (snap.empty) {
      return {}
    }

    const storedDiscount = {
      ...snap.docs[0].data(),
      _id: snap.docs[0].id,
    } as ShopifyStoreGeneratedDiscount

    if (storedDiscount.clientId !== clientId) {
      this.logger.warn(
        {
          shop,
          code,
          clientId,
          storedDiscount,
        },
        `findStoredDiscountByCode: found stored discount but clientId does not match`
      )
    } else {
      this.logger.log(
        {
          shop,
          clientId,
          code,
          storedDiscount,
        },
        `findStoredDiscountByCode: found matching stored discount`
      )
    }

    return {
      storedDiscount,
    }
  }

  async getModel(
    shop: string,
    clientId: string,
    split: string,
    experiment: string,
    parameters: ModelParameters | undefined
  ) {
    this.logger.log(
      {
        shop,
        clientId,
        split,
        experiment,
        parameters,
      },
      `getModel: started`
    )

    try {
      if (
        !parameters ||
        !parameters.model ||
        parameters.floor === undefined ||
        parameters.floor === null ||
        parameters.ceiling === undefined ||
        parameters.ceiling === null ||
        parameters.start === undefined ||
        parameters.start === null ||
        parameters.end === undefined ||
        parameters.end === null
      ) {
        this.logger.error(
          {
            shop,
            clientId,
            split,
            experiment,
            parameters,
          },
          `getModel: Missing required model parameters`
        )
        throw new InternalServerErrorException(
          'Missing required model parameters'
        )
      }

      const response = await this.modelApiPostFetch<
        {
          promo: boolean | undefined
          promo_candidate: boolean | undefined
        },
        {
          shop: string
          clientId: string
          split: string
          experiment?: string
          model?: string
          floor?: number
          ceiling?: number
          start?: number
          end?: number
        }
      >([
        '/api/model',
        {
          shop,
          clientId,
          split,
          experiment,
          model: parameters?.model,
          floor: parameters?.floor,
          ceiling: parameters?.ceiling,
          start: parameters?.start,
          end: parameters?.end,
        },
      ])

      this.logger.log(
        {
          shop,
          clientId,
          split,
          response,
          experiment,
          parameters,
        },
        `getModel: done`
      )
      this.callModelCounter.add(1, {
        success: true,
        promo: response.promo,
        shop: shop,
      })

      return response
    } catch (e) {
      this.logger.error(
        {
          shop,
          clientId,
          split,
          experiment,
          parameters,
          error: e,
        },
        `getModel: failed`
      )
      this.callModelCounter.add(1, {
        success: false,
        shop: shop,
      })
      throw e
    }
  }

  //Get discount from cache or create a new one
  async getCachedPromo2(
    requester: UserContext | undefined,
    shopifyStoreId: string,
    discountConfigId: string | undefined,
    discountConfig: DiscountConfig,
    clientId: string,
    createIfNonExisting: boolean,
    validAfter: Date | undefined
  ) {
    const request = this.shopifyService.validateRequester(requester)
    const shop = request.shop!
    this.logger.log(
      { shop, clientId, createIfNonExisting, validAfter, discountConfigId },
      `getCachedPromo: started`
    )

    //Save the created discount to prevent creating a code multiple times within the transaction
    let createdDiscount:
      | {
          discountId: string
          discountId2: string
          discount: DiscountContentDto
        }
      | undefined

    const result = await this.firestore.runTransaction(async (transaction) => {
      const docRef = this.firestore
        .collection(shopifyStoreClientCollectionPath)
        .doc(clientId)
      const docSnap = await transaction.get(docRef)
      let discount: DiscountContentDto | undefined
      let createNew = createIfNonExisting

      if (docSnap.exists) {
        const storeClientData = {
          _id: docSnap.id,
          ...docSnap.data(),
        } as ShopifyStoreClient
        mapTimestampFieldsToDate(storeClientData, ['createdAt', 'updatedAt'])

        if (storeClientData.cachedPromos?.[btoa(shop!)]) {
          createNew = false
          const now = new Date()
          const cachedPromo = storeClientData.cachedPromos[btoa(shop!)]
          discount = cachedPromo.discount
          mapTimestampFieldsToDate(cachedPromo, ['createdAt', 'validUntil'])
          mapTimestampFieldsToDate(discount, ['startsAt', 'endsAt'])

          let isValid = !cachedPromo.validUntil || cachedPromo.validUntil > now
          if (!isValid) {
            this.logger.log(
              {
                shop,
                clientId,
              },
              `getCachedPromo: validUntil check failed`
            )
          }

          if (isValid) {
            isValid = Boolean(discount && discount.endsAt > now)
            if (!isValid) {
              this.logger.log(
                {
                  shop,
                  clientId,
                },
                `getCachedPromo: discount expired`
              )
            }
          }

          // if (isValid && validAfter) {
          //   isValid = Boolean(cachedPromo && cachedPromo.createdAt > validAfter)
          //   if (!isValid) {
          //     this.logger.log(
          //       {
          //         shop,
          //         clientId,
          //       },
          //       `getCachedPromo: validAfter check failed`
          //     )
          //   }
          // }

          if (isValid) {
            if (
              (discountConfigId || cachedPromo.discountConfigId) &&
              discountConfigId !== cachedPromo.discountConfigId
            ) {
              this.logger.warn(
                {
                  shop,
                  clientId,
                  discountConfigId,
                  cachedDiscountConfigId: cachedPromo.discountConfigId,
                },
                `getCachedPromo: discountConfigId mismatches`
              )
              isValid = false
            }
          }

          if (!isValid) {
            //If the promo is expired, we could create a new one if needed
            createNew = createIfNonExisting
            this.logger.log(
              {
                shop,
                clientId,
              },
              `getCachedPromo: cached promo is not valid`
            )
          }

          if (isValid) {
            //Check if code has been redeemed
            //Note: this is not reliable and can have big latency
            const { isValid: validCode } = await this.checkShopifyDiscountCode(
              requester,
              discount!.code
            )
            isValid = validCode
            if (!validCode) {
              this.logger.log(
                {
                  shop,
                  clientId,
                },
                `getCachedPromo: checkShopifyDiscountCode failed`
              )
            }
          }
          this.logger.log(
            {
              isValid,
              createNew,
              shop,
              clientId,
              discount,
              cachedPromo,
              validAfter,
              typeOfCreatedAt: typeof cachedPromo.createdAt,
              typeOfValidAfter: typeof validAfter,
              createdAtIsDate: cachedPromo.createdAt instanceof Date,
              validAfterIsDate: validAfter instanceof Date,
            },
            `getCachedPromo: found cached promo`
          )
          if (!isValid) {
            discount = undefined
          }
        }
      } else {
        this.logger.log(
          { clientId },
          `getCachedPromo: ShopifyStoreClient data not found, creating new one`
        )
      }

      if (discount) {
        this.logger.log(
          {
            shop,
            clientId,
            discount,
          },
          `getCachedPromo: valid cached promo found`
        )
      } else {
        this.logger.log(
          {
            shop,
            clientId,
          },
          `getCachedPromo: valid cached promo not found`
        )
        if (createNew) {
          const now = new Date()
          let discountId: string | undefined
          let discountId2: string | undefined
          if (!createdDiscount) {
            this.logger.log(
              {
                shop,
                clientId,
              },
              `getCachedPromo: creating new promo`
            )

            const discountCodeGen = customAlphabet(
              discountConfig.codeGen.alphabet,
              discountConfig.codeGen.length -
                (discountConfig.codeGen.prefix?.length ?? 0)
            )
            const code = `${discountConfig.codeGen.prefix ?? ''}${discountCodeGen()}`

            const startsAt = now
            const endsAt = new Date(
              startsAt.getTime() +
                discountConfig.discountDurationInMinutes * 60 * 1000
            )
            const discountCodeInput = discountConfig.discountInput

            const response = await this.createShopifyDiscountCode(requester, {
              ...discountCodeInput,
              code,
              startsAt,
              endsAt,
            })
            if (
              response?.discountCodeBasicCreate?.codeDiscountNode?.codeDiscount
                ?.codes?.nodes.length
            ) {
              const codeDiscount =
                response.discountCodeBasicCreate.codeDiscountNode.codeDiscount

              this.logger.log(
                {
                  shop,
                  clientId,
                  codeDiscount,
                },
                //Keep this log line intact for monitoring purposes
                `getPersonalizedDiscountCode: created personalized discount code`
              )

              discountId = codeDiscount.codes.nodes[0].id
              discountId2 = response.discountCodeBasicCreate.codeDiscountNode.id
              discount = {
                title: codeDiscount.title,
                code: codeDiscount.codes.nodes[0].code,
                link: codeDiscount.shareableUrls[0]?.url,
                startsAt: new Date(codeDiscount.startsAt),
                endsAt: new Date(codeDiscount.endsAt),
              }

              createdDiscount = {
                discountId,
                discountId2,
                discount,
              }

              //Store the discount code in the database so we can clean it up from the Shopify store later
              //Doing it not with the transaction because we want to store it all the time
              await this.firestore
                .collection(
                  shopifyStoreGeneratedDiscountsCollectionPath(shopifyStoreId)
                )
                .add({
                  createdAt: now,
                  shop,
                  clientId,
                  discountId,
                  discountId2,
                  discount,
                } satisfies Omit<ShopifyStoreGeneratedDiscount, '_id'>)
            }
          } else {
            this.logger.log(
              {
                shop,
                clientId,
              },
              `getCachedPromo: reusing new promo created in previous transaction run`
            )

            discountId = createdDiscount.discountId
            discount = createdDiscount.discount
            discountId2 = createdDiscount.discountId2
          }

          if (discountId && discountId2 && discount) {
            //Cache the discount code for this client
            const validUntil = discountConfig.cacheValidDurationInMinutes
              ? new Date(
                  now.getTime() +
                    discountConfig.cacheValidDurationInMinutes * 60 * 1000
                )
              : undefined

            if (docSnap.exists) {
              transaction.update(docRef, {
                updatedAt: new Date(),
                shops: FieldValue.arrayUnion(shop),
                [`cachedPromos.${btoa(shop!)}`]: {
                  shop,
                  createdAt: now,
                  validUntil,
                  discountId,
                  discountId2,
                  discount,
                  discountConfigId,
                },
              } as UpdateData<ShopifyStoreClient>)
            } else {
              transaction.create(docRef, {
                clientId,
                createdAt: now,
                updatedAt: now,
                shops: [shop],
                cachedPromos: {
                  [`${btoa(shop!)}`]: {
                    shop,
                    createdAt: now,
                    validUntil,
                    discountId,
                    discountId2,
                    discount,
                    discountConfigId,
                  },
                },
              } as Omit<ShopifyStoreClient, '_id'>)
            }
          } else {
            discountId = undefined
            discountId2 = undefined
            discount = undefined
          }
        }
      }

      const created = Boolean(discount && createNew)
      this.logger.log(
        {
          shop,
          clientId,
          discount,
          created,
        },
        `getCachedPromo: done`
      )

      return {
        discount,
        created,
      }
    })

    return result
  }

  async getGeneratedDiscounts(
    requester: UserContext | undefined,
    pageSize: number,
    startAfter: string | undefined,
    endBefore: string | undefined
  ): Promise<{
    data: ShopifyStoreGeneratedDiscount[]
    pageInfo: DataPageInfo
  }> {
    const request = this.shopifyService.validateRequester(requester)
    const shop = request.shop!
    const { shopifyStoreId } =
      await this.storeConfigService.getStoreConfigForGetDiscount(shop)
    if (!shopifyStoreId) {
      throw new NotFoundException('Store config not found')
    }

    if (pageSize <= 0) {
      pageSize = 1000
    }
    const baseQuery = this.firestore
      .collection(shopifyStoreGeneratedDiscountsCollectionPath(shopifyStoreId))
      .orderBy('createdAt', 'desc')

    const { data, pageInfo } =
      await pagedFirestoreDataReader<ShopifyStoreGeneratedDiscount>(
        this.firestore,
        shopifyStoreGeneratedDiscountsCollectionPath(shopifyStoreId),
        baseQuery,
        pageSize,
        startAfter,
        endBefore,
        true
      )

    //Decorate the data
    const now = new Date()
    await Promise.all(
      data.map(async (discount) => {
        mapTimestampFieldsToDate(discount, ['createdAt'])
        mapTimestampFieldsToDate(discount.discount, ['startsAt', 'endsAt'])
        if (discount.discountStatus) {
          mapTimestampFieldsToDate(discount.discountStatus, ['syncedAt'])
        }

        const { discountStatus } = await this.checkShopifyDiscountCode(
          requester,
          discount.discount.code
        )
        if (discountStatus) {
          const status = discountStatus.usageCount
            ? 'REDEEMED'
            : discountStatus.status

          discount.discountStatus = {
            syncedAt: now,
            status,
            usageCount: discountStatus.usageCount,
          }
        }
      })
    )

    return {
      data,
      pageInfo,
    }
  }
}
