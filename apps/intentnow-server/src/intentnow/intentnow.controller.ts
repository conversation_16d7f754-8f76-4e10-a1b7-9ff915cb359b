import {
  BadRequestException,
  Body,
  Controller,
  HttpCode,
  Logger,
  Param,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common'
import { IntentnowService } from './intentnow.service'
import { UserRequest } from 'src/auth/entities/user-context'
import { ApiAuthGuard } from 'src/auth/api-auth-guard'
import { ShopifyProxyAuthGuard } from 'src/shopify/shopify-proxy-guard'
import {
  GetDiscountRequestDto,
  GetDiscountResponseDto,
} from '../dto/intentnow.dto'
import { plainToInstance } from 'class-transformer'
import { GetDiscountService } from './get-discount.service'

@Controller('api/intentnow')
export class IntentnowController {
  private readonly logger = new Logger(IntentnowController.name)

  constructor(
    private readonly apiAuthGuard: ApiAuthGuard,
    private readonly intentnowService: IntentnowService,
    private readonly getDiscountService: GetDiscountService
  ) {}

  //Shopify app-proxy call
  @UseGuards(ShopifyProxyAuthGuard)
  @Post('shopify-app-proxy/:appHandle/get-discount')
  @HttpCode(200)
  async getDiscount(
    @Req() req: UserRequest,
    @Param('appHandle') appHandle: string,
    @Query('shop') shop: string,
    @Body()
    body: GetDiscountRequestDto
  ): Promise<GetDiscountResponseDto> {
    const response = await this.intentnowService.getClientDiscount(
      req.user,
      body.clientId,
      body.preview,
      body.previewWidgetId
    )
    return plainToInstance(GetDiscountResponseDto, response)
  }

  @Post('shopify-app-proxy-test/:appHandle/get-discount')
  @HttpCode(200)
  async getDiscountForPreview(
    @Param('appHandle') appHandle: string,
    @Query('shop') shop: string,
    @Body()
    body: GetDiscountRequestDto
  ): Promise<GetDiscountResponseDto> {
    if (!body.preview) {
      throw new BadRequestException('Preview required')
    }
    const robotUser = await this.apiAuthGuard.robotUserForShop(shop)
    const response = await this.getDiscountService.getDiscountAndWidgetPreview(
      robotUser,
      body.previewWidgetId
    )
    return plainToInstance(GetDiscountResponseDto, response)
  }
}
