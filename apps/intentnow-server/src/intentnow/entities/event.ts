import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose'
import { HydratedDocument } from 'mongoose'

export class EventCustomer {
  @Prop()
  customerId?: string

  @Prop()
  hash?: string
}

@Schema()
export class Event {
  @Prop()
  id?: string

  @Prop()
  clientId: string

  @Prop()
  mergedClientId?: string

  @Prop()
  eventSource: string

  @Prop()
  name: string

  @Prop()
  shop: string

  @Prop()
  type: string

  @Prop()
  timestamp: Date

  @Prop()
  serverTimestamp: Date

  @Prop({ type: {} })
  context?: any

  @Prop({ type: {} })
  data?: any

  @Prop({ type: {} })
  customData?: any

  @Prop()
  hashedIp?: string

  @Prop({
    type: EventCustomer,
  })
  customer?: EventCustomer
}

export type EventDocument = HydratedDocument<Event>
export const EventSchema = SchemaFactory.createForClass(Event)

@Schema()
export class HashedIp {
  @Prop()
  _id: string

  @Prop()
  client_id?: string

  @Prop()
  hashed_ip: string

  @Prop({ type: {} })
  location_data: any

  @Prop()
  created_at: Date
}

export type HashedIpDocument = HydratedDocument<HashedIp>
export const HashedIpSchema = SchemaFactory.createForClass(HashedIp)

@Schema()
export class Client {
  @Prop()
  _id: string

  @Prop()
  clientId: string

  //@deprecated
  // @Prop()
  // mergedClientId?: string

  @Prop()
  mergedClientId2?: string

  @Prop()
  createdAt: Date

  @Prop()
  updatedAt: Date

  @Prop()
  lastSeenAt: Date

  @Prop()
  shop: string

  //@deprecated
  // @Prop()
  // customerId?: string

  @Prop()
  customerId2?: string

  @Prop()
  emailHash?: string

  @Prop()
  hashedIps?: string[]

  @Prop()
  lastCheckoutAt?: Date
}

export type ClientDocument = HydratedDocument<Client>
export const ClientSchema = SchemaFactory.createForClass(Client)

@Schema()
export class RawEvent {
  @Prop()
  name: string

  @Prop()
  shop: string

  @Prop()
  eventSource: string

  @Prop()
  serverTimestamp: Date

  @Prop({ type: {} })
  event: any
}

export type RawEventDocument = HydratedDocument<RawEvent>
export const RawEventSchema = SchemaFactory.createForClass(RawEvent)

@Schema()
export class IpLocation {
  @Prop()
  _id: string

  @Prop()
  hashedIp: string

  @Prop()
  createdAt: Date

  @Prop({ type: {} })
  locationData: any
}
export type IpLocationDocument = HydratedDocument<IpLocation>
export const IpLocationSchema = SchemaFactory.createForClass(IpLocation)
