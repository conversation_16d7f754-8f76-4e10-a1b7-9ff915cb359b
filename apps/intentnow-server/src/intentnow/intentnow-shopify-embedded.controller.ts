import {
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common'
import {
  LinkStoreRequestDto,
  UserRequest,
} from 'src/auth/entities/user-context'
import { ShopifyEmbeddedAuthGuard } from 'src/shopify/shopify-auth-guard'
import { GetDiscountService } from './get-discount.service'
import { StoreConfigService } from './store-config.service'
import {
  CreateWebPixelResponseDto,
  DeleteWebPixelResponseDto,
  GeneratedDiscountsPageDto,
  ShopifyAppSettingsResponseDto,
  StoreAnalyticsSettingsDto,
  WebPixelDto,
} from '../dto/intentnow.dto'
import { plainToInstance } from 'class-transformer'
import { ApiQuery } from '@nestjs/swagger'
import { ShopifyStoreService } from 'src/stores/shopify-store.service'

@Controller('api/shopify')
export class IntentnowShopifyEmbeddedController {
  constructor(
    private readonly shopifyStoreService: ShopifyStoreService,
    private readonly getDiscountService: GetDiscountService,
    private readonly storeConfigService: StoreConfigService
  ) {}

  @UseGuards(ShopifyEmbeddedAuthGuard)
  @Get(':appHandle/intentnow/web-pixel')
  async getWebPixel(
    @Param('appHandle') appHandle: string, //keep this for the Swagger API specs
    @Req() request: UserRequest
  ): Promise<WebPixelDto> {
    const resp = await this.shopifyStoreService.getWebPixel(request.user)
    return plainToInstance(WebPixelDto, resp)
  }

  @UseGuards(ShopifyEmbeddedAuthGuard)
  @Post(':appHandle/intentnow/web-pixel')
  async createWebPixel(
    @Param('appHandle') appHandle: string, //keep this for the Swagger API specs
    @Req() request: UserRequest
  ): Promise<CreateWebPixelResponseDto> {
    const resp = await this.shopifyStoreService.createWebPixel(request.user)
    return plainToInstance(CreateWebPixelResponseDto, resp)
  }

  @UseGuards(ShopifyEmbeddedAuthGuard)
  @Delete(':appHandle/intentnow/web-pixel')
  async deleteWebPixel(
    @Param('appHandle') appHandle: string, //keep this for the Swagger API specs
    @Req() request: UserRequest
  ): Promise<DeleteWebPixelResponseDto> {
    const resp = await this.shopifyStoreService.deleteWebPixel(request.user)
    return plainToInstance(DeleteWebPixelResponseDto, resp)
  }

  @UseGuards(ShopifyEmbeddedAuthGuard)
  @Get(':appHandle/intentnow/app-settings')
  async getAppStatus(
    @Param('appHandle') appHandle: string, //keep this for the Swagger API specs
    @Req() request: UserRequest
  ): Promise<ShopifyAppSettingsResponseDto> {
    const resp = await this.shopifyStoreService.getAppSettings(
      request.user,
      true
    )
    return plainToInstance(ShopifyAppSettingsResponseDto, resp)
  }

  @UseGuards(ShopifyEmbeddedAuthGuard)
  @Get(':appHandle/intentnow/analytics-settings')
  async getAnalyticsSettings(
    @Param('appHandle') appHandle: string, //keep this for the Swagger API specs
    @Req() request: UserRequest
  ): Promise<StoreAnalyticsSettingsDto> {
    const resp = await this.storeConfigService.getAnalyticsSettings(
      request.user
    )
    return plainToInstance(StoreAnalyticsSettingsDto, resp)
  }

  @UseGuards(ShopifyEmbeddedAuthGuard)
  @Get(':appHandle/intentnow/generated-discounts')
  @ApiQuery({
    name: 'startAfter',
    required: false,
    type: 'string',
  })
  @ApiQuery({
    name: 'endBefore',
    required: false,
    type: 'string',
  })
  @ApiQuery({
    name: 'pageSize',
    required: false,
    type: 'number',
  })
  async getDiscounts(
    @Req() request: UserRequest,
    @Param('appHandle') appHandle: string, //keep this for the Swagger API specs
    @Query('startAfter') startAfter: string | undefined,
    @Query('endBefore') endBefore: string | undefined,
    @Query('pageSize') pageSize: string | undefined
  ): Promise<GeneratedDiscountsPageDto> {
    const pageSizeNum = +(pageSize || '0')
    const resp = await this.getDiscountService.getGeneratedDiscounts(
      request.user,
      pageSizeNum,
      startAfter,
      endBefore
    )
    return plainToInstance(GeneratedDiscountsPageDto, resp)
  }

  @UseGuards(ShopifyEmbeddedAuthGuard)
  @Post(':appHandle/intentnow/create-link-store-request')
  async createLinkStoreRequest(
    @Param('appHandle') appHandle: string, //keep this for the Swagger API specs
    @Req() request: UserRequest
  ): Promise<LinkStoreRequestDto> {
    return await this.shopifyStoreService.createLinkStoreRequest(request.user)
  }
}
