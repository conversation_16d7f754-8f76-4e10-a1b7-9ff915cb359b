import { buildMongoUpdate } from './data-helper'

describe('buildMongoUpdate', () => {
  it('should build a valid mongo update', () => {
    let update = buildMongoUpdate({
      name: 'test name',
      description: 'test desc',
    })
    expect(update).toEqual({
      name: 'test name',
      description: 'test desc',
      $unset: {},
    })

    update = buildMongoUpdate(
      {
        storeId: 'store-123',
        name: 'test name',
        description: null,
        nested: {
          field0: 'value0',
          field1: 'value1',
          field2: null,
        },
        nonNested: {
          field3: 'value3',
        },
      },
      {
        nested: {},
      },
      {
        storeId: {},
        nested: {
          field0: {},
        },
      }
    )
    expect(update).toEqual({
      name: 'test name',
      'nested.field1': 'value1',
      nonNested: {
        field3: 'value3',
      },
      $unset: {
        description: true,
        'nested.field2': true,
      },
    })
  })
})


