import {
  BadRequestException,
  createParamDecorator,
  ExecutionContext,
  Type,
} from '@nestjs/common'
import { plainToInstance } from 'class-transformer'
import { Document, Model, PaginateResult } from 'mongoose'
import { Request } from 'express'
import { <PERSON><PERSON><PERSON><PERSON>y, <PERSON>lterOp, Fi<PERSON>Query, SortQuery } from 'src/dto/common.dto'
import { SchemaOptions } from 'mongoose'

export const mongooseSchemaOptions: SchemaOptions = {
  minimize: false,
  toObject: {
    transform: (doc, ret) => {
      if (ret._id) {
        ret._id = ret._id.toString()
      }
      return ret
    },
  },
  toJSON: {
    transform: (doc, ret) => {
      if (ret._id) {
        ret._id = ret._id.toString()
      }
      return ret
    },
  },
}
export interface NestedFields {
  [key: string]: NestedFields
}

export function mapPaginatedMongoResultsToDto<EntityDto, PageDto>(
  mongoResults: PaginateResult<Document>,
  entityDto: Type<EntityDto>,
  pageDto: Type<PageDto>
) {
  const data = mongoResults.docs.map((doc) => {
    return plainToInstance(entityDto, doc.toObject())
  })

  return plainToInstance(pageDto, {
    data,
    meta: {
      count: data.length,
      total: mongoResults.totalDocs,
      page: mongoResults.page,
      pageSize: mongoResults.limit,
      pageCount: mongoResults.totalPages,
    },
  })
}

function constructMongoDataUpdate(
  path: string,
  updateDto: any,
  updateObj: any,
  unset: Record<string, boolean>,
  nestedFields: NestedFields | undefined,
  blockedFields: NestedFields | undefined
) {
  Object.keys(updateDto).forEach((key) => {
    if (blockedFields?.[key] && !Object.keys(blockedFields[key]).length) {
      return
    }

    if (updateDto[key] === null) {
      unset[`${path}${key}`] = true
    } else if (nestedFields && nestedFields[key]) {
      constructMongoDataUpdate(
        `${path}${key}.`,
        updateDto[key],
        updateObj,
        unset,
        nestedFields[key],
        blockedFields?.[key]
      )
    } else {
      updateObj[`${path}${key}`] = updateDto[key]
    }
  })
}

export function buildMongoUpdate<UpdateDto>(
  updateDto: UpdateDto,
  nestedFields?: NestedFields,
  blockedFields?: NestedFields
) {
  const unset: Record<string, boolean> = {}
  const updateObj: any = {}
  constructMongoDataUpdate(
    '',
    updateDto,
    updateObj,
    unset,
    nestedFields,
    blockedFields
  )

  return {
    ...updateObj,
    $unset: unset,
  }
}

export function parseCrudQueryFunc(
  params: {
    sortFields?: string[]
    filterFields?: string[]
  },
  ctx: ExecutionContext
): CrudQuery {
  const req: Request = ctx.switchToHttp().getRequest()

  let page: number | undefined
  let limit: number | undefined
  let sorts: SortQuery[] | undefined
  let filters: FilterQuery[] | undefined

  if (req.query.page) {
    page = +req.query.page
  }
  if (req.query.limit) {
    limit = +req.query.limit
  }
  if (params.sortFields?.length && req.query.sort) {
    // ?sort=name,asc&sort=createdAt,desc
    // ?sort=name
    const sortQueries = Array.isArray(req.query.sort)
      ? (req.query.sort as string[])
      : [req.query.sort as string]
    sorts = sortQueries.map((x) => {
      const splits = x.split(',')
      const field = splits[0]
      let order = splits[1]

      if (!field || !params.sortFields!.includes(field)) {
        throw new BadRequestException(`Invalid sort field: ${field}`)
      }
      if (order) {
        order = order.toLocaleLowerCase()
      } else {
        order = 'asc'
      }
      if (order !== 'asc' && order !== 'desc') {
        throw new BadRequestException(`Invalid sort order: ${order}`)
      }
      return {
        field,
        order: order as 'asc' | 'desc',
      }
    })
  }
  if (params.filterFields?.length && req.query.filter) {
    // ?filter=field,eq,123
    // TODO: Uri decode the split values
    const filterQueries = Array.isArray(req.query.filter)
      ? (req.query.filter as string[])
      : [req.query.filter as string]
    filters = filterQueries.map((x) => {
      const splits = x.split(',')
      const field = splits[0]
      const op = splits[1]
      const value = splits[2]

      if (!field || !params.filterFields!.includes(field)) {
        throw new BadRequestException(`Invalid filter field: ${field}`)
      }
      if (!op || !Object.values(FilterOp).includes(op as FilterOp)) {
        throw new BadRequestException(`Invalid filter op: ${op}`)
      }
      return {
        field,
        op: op as FilterOp,
        value,
      }
    })
  }

  return {
    page,
    limit,
    sorts,
    filters,
  }
}

export const ParsedCrudQuery = createParamDecorator(parseCrudQueryFunc)

export function buildMongoQuery<DocType>(
  model: Model<DocType>,
  crudQuery: CrudQuery
) {
  const filter: any = {}
  const options: any = {
    page: crudQuery.page ?? 1,
    limit: crudQuery.limit ?? 10,
  }

  crudQuery.sorts?.forEach((x) => {
    options.sort = options.sort || {}
    options.sort[x.field] = x.order === 'asc' ? 1 : -1
  })

  crudQuery.filters?.forEach((f) => {
    //TODO: add more operators
    if (f.op === FilterOp.eq) {
      filter[f.field] = {
        $eq: f.value,
      }
    }
  })

  return {
    filter,
    options,
  }
}
