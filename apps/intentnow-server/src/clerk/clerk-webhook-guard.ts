import {
  CanActivate,
  ExecutionContext,
  Injectable,
  Logger,
  UnauthorizedException,
} from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { WebhookEvent } from '@clerk/backend'
import { VerifyWebhookOptions } from '@clerk/backend/webhooks'
import { ClerkConfig } from '../config/config'
import { AuthType, UserContext } from '@packages/shared-entities'
import { ClsService } from 'nestjs-cls'
import { ClerkWebhookRequest } from './entities/clerk-webhook-request'
import { verifyWebhook } from '@clerk/express/webhooks'

@Injectable()
export class ClerkWebhookGuard implements CanActivate {
  private readonly logger = new Logger(ClerkWebhookGuard.name)
  private readonly verifyingWebhookOptions: VerifyWebhookOptions

  constructor(
    private readonly configService: ConfigService,
    private readonly cls: ClsService
  ) {
    const clerkConfig = this.configService.get<ClerkConfig>('clerk')
    if (!clerkConfig) {
      throw new Error('Clerk configuration is not set')
    }
    this.verifyingWebhookOptions = clerkConfig.verifyWebhookOptions
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest() as ClerkWebhookRequest
    const originalBody = request.body
    request.body = request.rawBody // ensure raw body is set for verification
    try {
      const event: WebhookEvent = await verifyWebhook(
        request,
        this.verifyingWebhookOptions
      )
      request['clerkWebhookEvent'] = event
      const user: UserContext = {
        authType: AuthType.clerkWebhook,
        userId: event.data.id || 'unknown',
        roles: {
          admin: true,
        },
      }
      this.cls.set('user', user)

      return true
    } catch (error) {
      this.logger.error(
        `Clerk webhook verification failed: ${error.message}`,
        error.stack
      )
      throw new UnauthorizedException('Invalid webhook signature')
    } finally {
      request.body = originalBody
    }
  }
}
