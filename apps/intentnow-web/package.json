{"name": "@apps/intentnow-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "shopify:dev": "caddy reverse-proxy --from localhost:3001 --to localhost:3000 & pnpm run dev", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write .", "typecheck": "tsc --noEmit"}, "dependencies": {"@clerk/nextjs": "^6.21.0", "@emotion/cache": "^11.14.0", "@emotion/core": "^11.0.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@jsonforms/core": "^3.5.1", "@jsonforms/material-renderers": "^3.5.1", "@jsonforms/react": "^3.5.1", "@measured/puck": "^0.18.3", "@measured/puck-plugin-emotion-cache": "^0.18.3", "@mui/icons-material": "^6.4.12", "@mui/material": "^6.4.12", "@mui/material-nextjs": "^6.4.12", "@mui/x-charts": "^8.8.0", "@mui/x-data-grid": "^7.29.7", "@mui/x-date-pickers": "^8.9.0", "@packages/intentnow-tag": "workspace:^", "@packages/shared-entities": "workspace:^", "@refinedev/core": "^4.57.9", "@refinedev/mui": "^6.2.1", "@refinedev/nextjs-router": "^6.2.3", "@refinedev/react-hook-form": "^4.10.2", "@shopify/app": "^3.58.2", "@shopify/app-bridge": "^3.7.10", "@shopify/app-bridge-react": "^3.7.10", "@shopify/app-bridge-utils": "^3.5.1", "@statsig/react-bindings": "^3.17.2", "@statsig/web-analytics": "^3.17.2", "@toolpad/core": "^0.15.0", "date-fns": "^4.1.0", "firebase": "^11.3.1", "firebaseui": "^6.1.0", "lodash": "^4.17.21", "mui-color-input": "^6.0.0", "next": "15.3.3", "react": "^18", "react-dom": "^18", "react-firebase-hooks": "^5.1.1", "react-hook-form": "^7.60.0", "react-json-tree": "^0.19.0", "recharts": "^3.1.0", "swr": "^2.2.5", "usehooks-ts": "^3.1.0"}, "devDependencies": {"@refinedev/cli": "^2.16.46", "@shopify/app-bridge-types": "^0.0.14", "@shopify/cli": "^3.65.0", "@types/lodash": "^4.17.16", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^7.0.0", "eslint": "^8", "eslint-config-next": "15.3.3", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "prettier-eslint": "^16.3.0", "typescript": "^5"}}