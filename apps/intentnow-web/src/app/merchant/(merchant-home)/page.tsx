'use client'

import { useAuth } from '@/modules/auth/auth'
import {
  Box,
  Button,
  CircularProgress,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Typography,
} from '@mui/material'
import { Store as StoreIcon } from '@mui/icons-material'
import { StoreDto } from '@packages/shared-entities'
import { useList } from '@refinedev/core'
import { redirect, RedirectType, useSearchParams } from 'next/navigation'
import { EmbeddedDialog } from '@/components/embedded-dialog'

export default function MerchantHomePage() {
  const searchParams = useSearchParams()
  const { user } = useAuth()
  const { data: { data: stores } = {}, isLoading } = useList<StoreDto>({
    resource: user ? `users/${user.userId}/stores` : undefined,
    pagination: {
      current: 1,
      pageSize: 1000,
    },
  })

  const openFrom = searchParams.get('open-from')

  //Auto redirect to the only store
  if (stores && stores?.length == 1 && openFrom !== 'store-not-found') {
    redirect(`/merchant/store/${stores[0]._id}`, RedirectType.push)
  }

  if (isLoading) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100%',
        }}
      >
        <CircularProgress />
      </Box>
    )
  }

  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100%',
      }}
    >
      <EmbeddedDialog>
        {!!stores?.length && (
          <>
            <Typography
              variant="h5"
              sx={{
                marginBottom: '20px',
              }}
            >
              Select a store to manage
            </Typography>

            <List
              sx={{
                width: 'auto',
                maxHeight: '400px',
                overflow: 'auto',
              }}
            >
              {stores?.map((store) => (
                <ListItem key={store._id}>
                  <ListItemButton
                    onClick={() => {
                      redirect(
                        `/merchant/store/${store._id}`,
                        RedirectType.push
                      )
                    }}
                  >
                    <ListItemIcon>
                      <StoreIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary={`${store.name} [${store.shopifyConfig?.myshopifyDomain}]`}
                    />
                  </ListItemButton>
                </ListItem>
              ))}
            </List>
          </>
        )}
        {!stores?.length && (
          <>
            <Typography
              variant="h5"
              sx={{
                marginBottom: '20px',
              }}
            >
              No store is linked to this account.
            </Typography>
            <Typography>
              Please install the IntentNow Shopify App, and link a store from
              inside your Shopify Admin.
            </Typography>
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
              }}
            >
              <Box
                sx={{
                  marginTop: '40px',
                  display: 'flex',
                  flexDirection: 'row',
                  justifyContent: 'center',
                  gap: '40px',
                }}
              >
                <Button
                  variant="contained"
                  sx={{
                    margin: '10px',
                  }}
                >
                  Install IntentNow App
                </Button>
                <Button
                  variant="contained"
                  sx={{
                    margin: '10px',
                  }}
                  onClick={() => {
                    window.open(`https://admin.shopify.com`, '_blank')
                  }}
                >
                  Go to Shopify Admin
                </Button>
              </Box>
            </Box>
          </>
        )}
      </EmbeddedDialog>
    </Box>
  )
}
