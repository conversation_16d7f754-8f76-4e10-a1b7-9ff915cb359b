'use client'

import { RefineSnackbarProvider, useNotificationProvider } from '@refinedev/mui'
import routerProvider from '@refinedev/nextjs-router'
import { GlobalStyles, Stack } from '@mui/material'
import { Home as HomeIcon } from '@mui/icons-material'
import { NextAppProvider } from '@toolpad/core/nextjs'
import { AppRouterCacheProvider } from '@mui/material-nextjs/v15-appRouter'
import { DashboardLayout, ThemeSwitcher } from '@toolpad/core/DashboardLayout'
import type { Session } from '@toolpad/core/AppProvider'
import theme from '@/components/admin-theme'
import { useAuth as useClerkAuth, UserButton } from '@clerk/nextjs'
import { useEffect, useMemo } from 'react'
import { useAuth } from '@/modules/auth/auth'
import { Refine } from '@refinedev/core'
import { useIntentnowDataProvider } from '@/modules/refine/data-provider'

function CustomActions() {
  return (
    <Stack direction="row" alignItems="center">
      <ThemeSwitcher />
    </Stack>
  )
}

function CustomAccount() {
  return <UserButton />
}

export default function MerchantHomeLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  const { dataProvider } = useIntentnowDataProvider()
  const clerkAuth = useClerkAuth()
  const { user } = useAuth()
  const session: Session | undefined = useMemo(() => {
    return user
      ? {
          user: {
            id: user.userId,
            name: user.userInfo?.displayName,
            email: user.userInfo?.email,
          },
        }
      : undefined
  }, [user])

  useEffect(() => {
    document.title = `Merchant Portal`
  }, [])

  return (
    <AppRouterCacheProvider options={{ enableCssLayer: true }}>
      <NextAppProvider
        theme={theme}
        navigation={[]}
        branding={{
          logo: <HomeIcon color="primary" />,
          title: 'Merchant Portal',
          homeUrl: `/merchant`,
        }}
        session={session}
        authentication={{
          signIn: () => {},
          signOut: clerkAuth.signOut,
        }}
      >
        <GlobalStyles styles={{ html: { WebkitFontSmoothing: 'auto' } }} />
        <RefineSnackbarProvider>
          <Refine
            dataProvider={dataProvider}
            routerProvider={routerProvider}
            notificationProvider={useNotificationProvider}
          >
            <DashboardLayout
              hideNavigation={true}
              slots={{
                toolbarActions: CustomActions,
                toolbarAccount: CustomAccount,
              }}
            >
              {children}
            </DashboardLayout>
          </Refine>
        </RefineSnackbarProvider>
      </NextAppProvider>
    </AppRouterCacheProvider>
  )
}
