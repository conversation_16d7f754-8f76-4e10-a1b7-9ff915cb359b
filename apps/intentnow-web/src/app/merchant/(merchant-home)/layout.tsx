'use client'

import { RefineSnackbarProvider, useNotificationProvider } from '@refinedev/mui'
import routerProvider from '@refinedev/nextjs-router'
import { Box, GlobalStyles, Stack, Typography } from '@mui/material'
import { Home as HomeIcon } from '@mui/icons-material'
import { NextAppProvider } from '@toolpad/core/nextjs'
import { AppRouterCacheProvider } from '@mui/material-nextjs/v15-appRouter'
import { DashboardLayout } from '@toolpad/core/DashboardLayout'
import type { Session } from '@toolpad/core/AppProvider'
import theme from '@/components/admin-theme'
import { useAuth as useClerkAuth, UserButton } from '@clerk/nextjs'
import { useEffect, useMemo } from 'react'
import { useAuth } from '@/modules/auth/auth'
import { Refine } from '@refinedev/core'
import { useRefineDataProvider } from '@/modules/intentnow/refine-data-provider'

function CustomActions() {
  return (
    <Stack direction="row" alignItems="center">
      {/* <ThemeSwitcher /> */}
    </Stack>
  )
}

function CustomAccount() {
  return <UserButton />
}

function CustomAppTitle() {
  return (
    <Box sx={{ display: 'flex' }}>
      <HomeIcon
        //color={currentStoreId === store._id ? 'primary' : undefined}
        sx={{ marginRight: '10px' }}
      />
      <Typography variant="h6">Merchant Portal</Typography>
    </Box>
  )
}

export default function MerchantHomeLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  const { dataProvider } = useRefineDataProvider()
  const clerkAuth = useClerkAuth()
  const { user } = useAuth()
  const session: Session | undefined = useMemo(() => {
    return user
      ? {
          user: {
            id: user.userId,
            name: user.userInfo?.displayName,
            email: user.userInfo?.email,
          },
        }
      : undefined
  }, [user])

  useEffect(() => {
    document.title = `Merchant Portal`
  }, [])

  return (
    <AppRouterCacheProvider options={{ enableCssLayer: true }}>
      <NextAppProvider
        theme={theme}
        navigation={[]}
        branding={{
          logo: <HomeIcon color="primary" />,
          title: 'Merchant Portal',
          homeUrl: `/merchant`,
        }}
        session={session}
        authentication={{
          signIn: () => {},
          signOut: clerkAuth.signOut,
        }}
      >
        <GlobalStyles styles={{ html: { WebkitFontSmoothing: 'auto' } }} />
        <RefineSnackbarProvider>
          <Refine
            dataProvider={dataProvider}
            routerProvider={routerProvider}
            notificationProvider={useNotificationProvider}
          >
            <DashboardLayout
              hideNavigation={true}
              slots={{
                appTitle: CustomAppTitle,
                toolbarActions: CustomActions,
                toolbarAccount: CustomAccount,
              }}
              sx={{
                '& .MuiAppBar-root': {
                  backgroundColor: '#073035',
                  color: '#fff',
                },
              }}
            >
              {children}
            </DashboardLayout>
          </Refine>
        </RefineSnackbarProvider>
      </NextAppProvider>
    </AppRouterCacheProvider>
  )
}
