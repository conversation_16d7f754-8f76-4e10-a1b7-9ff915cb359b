'use client'

import { refineConfig } from '@/modules/config/refine'
import {
  Box,
  Button,
  Card,
  CardContent,
  CardHeader,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Grid2 as Grid,
  IconButton,
  TextField,
  Typography,
} from '@mui/material'
import {
  Delete as DeleteIcon,
  Edit as EditIcon,
  Check as CheckIcon,
  Clear as ClearIcon,
} from '@mui/icons-material'
import {
  DataGrid,
  GridActionsCellItem,
  GridColDef,
  GridValidRowModel,
} from '@mui/x-data-grid'
import {
  PublicUserDto,
  StoreAddUserDto,
  StoreDto,
} from '@packages/shared-entities'
import { List, SaveButton, useDataGrid } from '@refinedev/mui'
import { useParams } from 'next/navigation'
import { useMemo, useState } from 'react'
import { ConfirmDialog2 } from '@/components/confirm-dialog'
import { useDialogs } from '@toolpad/core/useDialogs'
import { useAuth } from '@/modules/auth/auth'
import { HttpError, useDelete } from '@refinedev/core'
import {
  useForm,
  useModalForm,
  UseModalFormReturnType,
} from '@refinedev/react-hook-form'
import { OptionalSwitch } from '@/components/optional-switch'
import { Controller } from 'react-hook-form'

export default function StoreSettingsStoreAccessPage() {
  const { storeId }: { storeId: string } = useParams()
  const { user } = useAuth()
  const dialogs = useDialogs()
  const [editingConfig, setEditingConfig] = useState(false)

  const {
    register,
    reset,
    control,
    getValues,
    formState: { isDirty },
    refineCore: {
      query,
      mutation: { mutateAsync: updateStore },
    },
  } = useForm<StoreDto>({
    refineCoreProps: {
      resource: 'stores',
      id: storeId,
      action: 'edit',
      queryOptions: refineConfig.defaultUseOneQueryOptions,
    },
  })
  const store = query?.data?.data

  const { mutateAsync: deleteUser } = useDelete()
  const usersResource = `stores/${storeId}/users`

  const createUserAccessModalFormProps = useModalForm<
    StoreAddUserDto,
    HttpError,
    StoreAddUserDto
  >({
    mode: 'onSubmit',
    reValidateMode: 'onSubmit',
    modalProps: {},
    refineCoreProps: { resource: usersResource, action: 'create' },
    syncWithLocation: false,
    warnWhenUnsavedChanges: true,
    defaultValues: {
      email: '',
    },
  })
  const {
    modal: { show: showCreateModal },
  } = createUserAccessModalFormProps

  const { dataGridProps } = useDataGrid<PublicUserDto>({
    resource: usersResource,
    sorters: {},
    syncWithLocation: false,
    queryOptions: refineConfig.defaultUseListQueryOptions,
  })
  const columns = useMemo<GridColDef<GridValidRowModel>[]>(
    () => [
      {
        field: 'email',
        headerName: 'Email',
        sortable: false,
        filterable: false,
        minWidth: 300,
      },
      {
        field: 'displayName',
        headerName: 'Name',
        flex: 1,
        sortable: false,
        filterable: false,
        minWidth: 200,
      },
      {
        field: 'actions',
        type: 'actions',
        headerName: 'Action',
        width: 150,
        sortable: false,
        filterable: false,
        getActions: ({ row }) => [
          <GridActionsCellItem
            key={2}
            icon={<DeleteIcon />}
            sx={{ padding: '2px 6px' }}
            label="Revoke"
            disabled={
              !user?.userInfo?.email || row.email === user.userInfo.email
            }
            onClick={async () => {
              const { answer } = await dialogs.open(ConfirmDialog2, {
                title: 'Revoke User Access',
                description: `You are about to revoke access for user "${row.email}". Do you want to continue?`,
              })
              if (answer) {
                await deleteUser({
                  resource: usersResource,
                  id: row._id,
                })
              }
            }}
          />,
        ],
      },
    ],
    [deleteUser, dialogs, usersResource, user]
  )

  return (
    <Box
      sx={{
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      {store && (
        <>
          <Card
            sx={{
              marginBottom: '20px',
            }}
          >
            <CardHeader title="Store Info" />
            <CardContent>
              <Grid container spacing={2}>
                <Grid size={4}>
                  <Typography>Name</Typography>
                </Grid>
                <Grid size={8}>
                  <Typography>{store.name}</Typography>
                </Grid>
                <Grid size={4}>
                  <Typography>Website</Typography>
                </Grid>
                <Grid size={8}>
                  <Typography>
                    <a
                      href={store.website}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      {store.website}
                    </a>
                  </Typography>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </>
      )}
      <Box
        sx={{
          marginBottom: '20px',
        }}
      >
        <List
          title="Users with Access"
          headerButtons={() => (
            <Box display="flex" gap={2} alignItems="center">
              <Button
                variant="contained"
                onClick={() => {
                  showCreateModal()
                }}
              >
                Add User
              </Button>
            </Box>
          )}
        >
          <DataGrid {...dataGridProps} columns={columns} />
        </List>
      </Box>
      <AddUserAccessModal {...createUserAccessModalFormProps} />

      {store && user?.roles?.admin && store.effectiveConfig && (
        <Card
          sx={{
            marginBottom: '20px',
          }}
        >
          <CardHeader
            title="[Internal] Store Config"
            action={
              editingConfig ? (
                <>
                  <IconButton
                    disabled={!isDirty}
                    onClick={async () => {
                      await updateStore({
                        resource: 'stores',
                        id: store._id,
                        values: {
                          config: {
                            receiveEvents:
                              getValues('config.receiveEvents') ?? undefined,
                            sendAmplitude:
                              getValues('config.sendAmplitude') ?? undefined,
                            showOffers:
                              getValues('config.showOffers') ?? undefined,
                            predictOffers:
                              getValues('config.predictOffers') ?? undefined,
                            isLegacy: getValues('config.isLegacy') ?? undefined,
                          },
                        },
                      })
                      setEditingConfig(false)
                    }}
                  >
                    <CheckIcon />
                  </IconButton>
                  <IconButton
                    onClick={() => {
                      reset(query?.data?.data, {
                        keepDirty: false,
                        keepDefaultValues: false,
                        keepErrors: false,
                        keepTouched: false,
                        keepIsValid: false,
                        keepIsSubmitted: false,
                        keepSubmitCount: false,
                      })
                      setEditingConfig(false)
                    }}
                  >
                    <ClearIcon />
                  </IconButton>
                </>
              ) : (
                <IconButton
                  value={'edit'}
                  onClick={() => {
                    setEditingConfig(true)
                  }}
                >
                  <EditIcon />
                </IconButton>
              )
            }
          />
          <CardContent>
            <Grid container spacing={2}>
              <Grid size={4}>
                <Typography>Receive Events</Typography>
              </Grid>
              <Grid size={8}>
                <Box
                  sx={{
                    flex: 1,
                    display: 'flex',
                    justifyContent: 'center',
                  }}
                >
                  <Controller
                    {...register('config.receiveEvents', {
                      required: false,
                    })}
                    control={control}
                    render={({ field: { onChange, value, ...restField } }) => {
                      return store.effectiveConfig ? (
                        <OptionalSwitch
                          value={value}
                          effectiveValue={store.effectiveConfig.receiveEvents}
                          editing={editingConfig}
                          onChange={(value) => {
                            onChange(value)
                          }}
                          {...restField}
                        />
                      ) : (
                        <> </>
                      )
                    }}
                  />
                </Box>
              </Grid>
              <Grid size={4}>
                <Typography>Send Amplitude</Typography>
              </Grid>
              <Grid size={8}>
                <Box
                  sx={{
                    flex: 1,
                    display: 'flex',
                    justifyContent: 'center',
                  }}
                >
                  <Controller
                    {...register('config.sendAmplitude', {
                      required: false,
                    })}
                    control={control}
                    render={({ field: { onChange, value, ...restField } }) => {
                      return (
                        <OptionalSwitch
                          value={value}
                          effectiveValue={store.effectiveConfig!.sendAmplitude}
                          editing={editingConfig}
                          onChange={(value) => {
                            onChange(value)
                          }}
                          {...restField}
                        />
                      )
                    }}
                  />
                </Box>
              </Grid>
              <Grid size={4}>
                <Typography>Show Offers</Typography>
              </Grid>
              <Grid size={8}>
                <Box
                  sx={{
                    flex: 1,
                    display: 'flex',
                    justifyContent: 'center',
                  }}
                >
                  <Controller
                    {...register('config.showOffers', {
                      required: false,
                    })}
                    control={control}
                    render={({ field: { onChange, value, ...restField } }) => {
                      return store.effectiveConfig ? (
                        <OptionalSwitch
                          value={value}
                          effectiveValue={store.effectiveConfig.showOffers}
                          editing={editingConfig}
                          onChange={(value) => {
                            onChange(value)
                          }}
                          {...restField}
                        />
                      ) : (
                        <> </>
                      )
                    }}
                  />
                </Box>
              </Grid>
              <Grid size={4}>
                <Typography>Predict Offers</Typography>
              </Grid>
              <Grid size={8}>
                <Box
                  sx={{
                    flex: 1,
                    display: 'flex',
                    justifyContent: 'center',
                  }}
                >
                  <Controller
                    {...register('config.predictOffers', {
                      required: false,
                    })}
                    control={control}
                    render={({ field: { onChange, value, ...restField } }) => {
                      return store.effectiveConfig ? (
                        <OptionalSwitch
                          value={value}
                          effectiveValue={store.effectiveConfig.predictOffers}
                          editing={editingConfig}
                          onChange={(value) => {
                            onChange(value)
                          }}
                          {...restField}
                        />
                      ) : (
                        <> </>
                      )
                    }}
                  />
                </Box>
              </Grid>
              <Grid size={4}>
                <Typography>Legacy Store</Typography>
              </Grid>
              <Grid size={8}>
                <Box
                  sx={{
                    flex: 1,
                    display: 'flex',
                    justifyContent: 'center',
                  }}
                >
                  <Controller
                    {...register('config.isLegacy', {
                      required: false,
                    })}
                    control={control}
                    render={({ field: { onChange, value, ...restField } }) => {
                      return store.effectiveConfig ? (
                        <OptionalSwitch
                          value={value}
                          effectiveValue={store.effectiveConfig.isLegacy}
                          editing={editingConfig}
                          onChange={(value) => {
                            onChange(value)
                          }}
                          {...restField}
                        />
                      ) : (
                        <> </>
                      )
                    }}
                  />
                </Box>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      )}
    </Box>
  )
}

export function AddUserAccessModal({
  saveButtonProps,
  modal: { visible, close },
  register,
  formState: { errors },
}: UseModalFormReturnType<StoreAddUserDto, HttpError, StoreAddUserDto>) {
  return (
    <Dialog
      open={visible}
      //onClose={close}
      maxWidth={false}
      sx={{
        '& .MuiDialog-paper': {
          maxWidth: '90vw',
          maxHeight: '90vw',
        },
      }}
    >
      <DialogTitle>Add User</DialogTitle>
      <DialogContent>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            padding: '20px',
          }}
        >
          <Box
            component="form"
            autoComplete="off"
            sx={{
              display: 'flex',
              flexDirection: 'column',
              width: '600px',
              gap: '10px',
            }}
          >
            <Grid
              container
              spacing={2}
              sx={{
                justifyItems: 'center',
                alignItems: 'center',
              }}
            >
              <Grid
                size={4}
                sx={{
                  alignItems: 'center',
                }}
              >
                <Typography>User email</Typography>
              </Grid>
              <Grid
                size={8}
                sx={{
                  alignItems: 'center',
                }}
              >
                <TextField
                  id="name"
                  autoFocus
                  {...register('email', {
                    required: 'Email is required',
                    pattern: {
                      value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                      message: 'Please enter a valid email',
                    },
                  })}
                  error={!!errors.email}
                  helperText={errors.email?.message}
                  margin="normal"
                  fullWidth
                />
              </Grid>
            </Grid>
          </Box>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button variant="outlined" onClick={close}>
          Cancel
        </Button>
        <SaveButton {...saveButtonProps} variant="outlined">
          Add
        </SaveButton>
      </DialogActions>
    </Dialog>
  )
}
