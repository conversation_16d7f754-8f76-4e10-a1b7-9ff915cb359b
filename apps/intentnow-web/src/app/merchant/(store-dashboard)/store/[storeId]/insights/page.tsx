'use client'

import { AggregatedEventChart, CustomFunnelChart } from '@/components/charts'
import { useAuthedApiFetch } from '@/modules/auth/fetch'
import { Box, MenuItem, Select } from '@mui/material'
import { ChartDto, ChartPeriod } from '@packages/shared-entities'
import { useParams } from 'next/navigation'
import { useForm } from 'react-hook-form'
import useSWR from 'swr'

const chartPeriodOptions = [
  { label: 'Last 7 days', value: '7d' },
  { label: 'Last 30 days', value: '30d' },
  { label: 'Last 90 days', value: '90d' },
]

interface ChartSettings {
  chartPeriod: ChartPeriod
}

export default function InsightsPage() {
  const { authedApiFetch } = useAuthedApiFetch()
  const { storeId }: { storeId: string } = useParams()
  const { register, watch } = useForm<ChartSettings>({
    defaultValues: {
      chartPeriod: '7d' as any,
    },
  })

  const { data: { funnelChart: userConversionChart } = {} } = useSWR<ChartDto>(
    `/api/intentnow/stores/${storeId}/charts/user-conversion?period=${watch(
      'chartPeriod'
    )}`,
    authedApiFetch
  )
  const { data: { funnelChart: otfUserConversionChart } = {} } =
    useSWR<ChartDto>(
      `/api/intentnow/stores/${storeId}/charts/otf-user-conversion?period=${watch(
        'chartPeriod'
      )}`,
      authedApiFetch
    )
  const { data: { aggregatedEventChart: userRevenueChart } = {} } =
    useSWR<ChartDto>(
      `/api/intentnow/stores/${storeId}/charts/user-revenue?period=${watch(
        'chartPeriod'
      )}`,
      authedApiFetch
    )

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      <Box sx={{ height: '50px', display: 'flex', padding: '5px' }}>
        <Select
          sx={{
            marginLeft: '10px',
            marginTop: '0px',
            marginBottom: '0px',
            border: 'none',
            boxShadow: 'none',
          }}
          defaultValue={'7d'}
          {...register('chartPeriod')}
        >
          {chartPeriodOptions.map((option) => (
            <MenuItem key={option.value} value={option.value}>
              {option.label}
            </MenuItem>
          ))}
        </Select>
      </Box>
      <Box
        sx={{
          width: '100%',
          display: 'flex',
          flexWrap: 'wrap',
        }}
      >
        <Box
          sx={{
            padding: '5px',
            width: '400px',
            height: '300px',
          }}
        >
          <CustomFunnelChart chartData={userConversionChart} />
        </Box>

        <Box
          sx={{
            padding: '5px',
            width: '400px',
            height: '300px',
          }}
        >
          <CustomFunnelChart chartData={otfUserConversionChart} />
        </Box>
        <Box
          sx={{
            padding: '5px',
            width: '400px',
            height: '300px',
          }}
        >
          <AggregatedEventChart chartData={userRevenueChart} />
        </Box>
      </Box>
    </Box>
  )
}
