'use client'

import { ConfirmDialog2 } from '@/components/confirm-dialog'
import {
  <PERSON>,
  Card,
  CardContent,
  CardHeader,
  CircularProgress,
  Grid2 as Grid,
  Switch,
  Typography,
} from '@mui/material'
import {
  ShopifyStoreIntegrationDto,
  ShopifyStoreIntegrationUpdateDto,
} from '@packages/shared-entities'
import { useOne, useUpdate } from '@refinedev/core'
import { useDialogs } from '@toolpad/core/useDialogs'
import { useParams } from 'next/navigation'

export default function StoreSettingsIntegrationsPage() {
  const { storeId }: { storeId: string } = useParams()
  const dialogs = useDialogs()
  const {
    data: { data: shopifyIntegration } = {},
    isLoading: getLoading,
    isFetching: getFetching,
    error,
  } = useOne<ShopifyStoreIntegrationDto>({
    resource: `stores/${storeId}`,
    id: 'shopify-integration',
    queryOptions: {
      enabled: true,
      staleTime: 1000 * 60,
      refetchOnWindowFocus: 'always',
      refetchOnReconnect: 'always',
      refetchOnMount: 'always',
      retry: 3,
    },
  })
  const { mutateAsync: updateShopifyIntegration, isPending: updatePending } =
    useUpdate<ShopifyStoreIntegrationUpdateDto>({
      resource: `stores/${storeId}`,
      id: 'shopify-integration',
    })

  const isLoading = getLoading || getFetching || updatePending

  if (getLoading) {
    return (
      <Box
        sx={{
          height: '100px',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <CircularProgress />
      </Box>
    )
  }

  return (
    <Box
      sx={{
        flex: 1,
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      {!shopifyIntegration?.linked && (
        <Card
          sx={{
            marginBottom: '20px',
          }}
        >
          <CardHeader
            title={
              shopifyIntegration?.linked
                ? 'Shopify Extensions'
                : 'No Shopify Integration found'
            }
          />
        </Card>
      )}
      {shopifyIntegration?.shopifyStore && (
        <Card
          sx={{
            marginBottom: '20px',
          }}
        >
          <CardHeader title="Shopify Store Info" />
          <CardContent>
            <Grid container spacing={2}>
              <Grid size={4}>
                <Typography>Name</Typography>
              </Grid>
              <Grid size={8}>
                <Typography>
                  {shopifyIntegration?.shopifyStore?.name}
                </Typography>
              </Grid>
              <Grid size={4}>
                <Typography>Myshopify Domain</Typography>
              </Grid>
              <Grid size={8}>
                <Typography>
                  {shopifyIntegration?.shopifyStore?.myshopifyDomain}
                </Typography>
              </Grid>
              <Grid size={4}>
                <Typography>URL</Typography>
              </Grid>
              <Grid size={8}>
                <Typography>
                  <a
                    href={shopifyIntegration?.shopifyStore?.url}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    {shopifyIntegration?.shopifyStore?.url}
                  </a>
                </Typography>
              </Grid>
              <Grid size={4}>
                <Typography>IntentNow App</Typography>
              </Grid>
              <Grid size={8}>
                <Typography>
                  {shopifyIntegration?.shopifyStore?.appHandle}
                </Typography>
              </Grid>
              <Grid size={4}>
                <Typography>Shopify Plus</Typography>
              </Grid>
              <Grid size={8}>
                <Typography>
                  {shopifyIntegration?.shopifyStore?.shopifyPlus ? 'Yes' : 'No'}
                </Typography>
              </Grid>
              <Grid size={4}>
                <Typography>Currency</Typography>
              </Grid>
              <Grid size={8}>
                <Typography>
                  {shopifyIntegration?.shopifyStore?.currencyCode}
                </Typography>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      )}
      {shopifyIntegration?.shopifyExtensions && (
        <Card
          sx={{
            marginBottom: '20px',
          }}
        >
          <CardHeader title="Shopify Extensions" />
          <CardContent>
            <Grid container spacing={2}>
              <Grid size={4}>
                <Typography>Anonymous Data Access</Typography>
              </Grid>
              <Grid size={8}>
                <Box
                  sx={{
                    flex: 1,
                    display: 'flex',
                    justifyContent: 'center',
                  }}
                >
                  {isLoading && <CircularProgress />}
                  {!isLoading && (
                    <Switch
                      disabled={Boolean(error) || !shopifyIntegration?.linked}
                      checked={
                        shopifyIntegration?.shopifyExtensions?.webPixelOn
                      }
                      onClick={async () => {
                        if (shopifyIntegration?.linked) {
                          const newWebPixel =
                            !shopifyIntegration.shopifyExtensions?.webPixelOn

                          const { answer } = await dialogs.open(
                            ConfirmDialog2,
                            newWebPixel
                              ? {
                                  title: 'Turn on Web Pixel',
                                  description: `You are about to turn on the web pixel extension on the Shopify store. Do you want to continue?`,
                                }
                              : {
                                  title: 'Turn off Web Pixel',
                                  description: `You are about to turn off the web pixel extension on the Shopify store. We will lose access to the annonymous user activity data, and the Offer Popups will stop working. Do you want to continue?`,
                                }
                          )
                          if (!answer) {
                            return
                          }
                          await updateShopifyIntegration({
                            values: {
                              ['shopifyExtensions.webPixelOn']: newWebPixel,
                            },
                          } satisfies {
                            values: ShopifyStoreIntegrationUpdateDto
                          })
                        }
                      }}
                    />
                  )}
                </Box>
              </Grid>
              <Grid size={4}>
                <Typography>Offer Popups</Typography>
              </Grid>
              <Grid size={8}>
                <Box
                  sx={{
                    flex: 1,
                    display: 'flex',
                    justifyContent: 'center',
                  }}
                >
                  {isLoading && <CircularProgress />}
                  {!isLoading && (
                    <Switch
                      disabled={
                        Boolean(error) ||
                        !shopifyIntegration?.linked ||
                        !shopifyIntegration?.shopifyExtensions
                          ?.promoWidgetToggleLink
                      }
                      checked={
                        shopifyIntegration?.shopifyExtensions?.promoWidgetOn
                      }
                      onClick={async () => {
                        if (
                          shopifyIntegration?.linked &&
                          shopifyIntegration?.shopifyExtensions
                            ?.promoWidgetToggleLink
                        ) {
                          window.open(
                            shopifyIntegration.shopifyExtensions
                              ?.promoWidgetToggleLink,
                            '_blank'
                          )
                        }
                      }}
                    />
                  )}
                </Box>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      )}
    </Box>
  )
}
