'use client'

import { refineConfig } from '@/modules/config/refine'
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Grid2 as Grid,
  IconButton,
  TextField,
  Typography,
} from '@mui/material'
import {
  Edit as EditIcon,
  Check as CheckIcon,
  Clear as ClearIcon,
} from '@mui/icons-material'
import { StoreDto } from '@packages/shared-entities'
import { useState } from 'react'
import { useForm } from '@refinedev/react-hook-form'
import { OptionalSwitch } from '@/components/optional-switch'
import { Controller } from 'react-hook-form'
import { useAuth } from '@/modules/auth/auth'

export function StoreConfigSettingsCard({ storeId }: { storeId: string }) {
  const { user } = useAuth()
  const [editingConfig, setEditingConfig] = useState(false)

  const {
    register,
    reset,
    control,
    getValues,
    formState: { isDirty },
    refineCore: {
      query,
      mutation: { mutateAsync: updateStore },
    },
  } = useForm<StoreDto>({
    refineCoreProps: {
      resource: 'stores',
      id: storeId,
      action: 'edit',
      queryOptions: refineConfig.defaultUseOneQueryOptions,
    },
  })
  const store = query?.data?.data

  if (!user?.roles?.admin || !store) {
    return <></>
  }

  return (
    <Card>
      <CardHeader
        title="[Internal] Store Config"
        action={
          editingConfig ? (
            <>
              <IconButton
                disabled={!isDirty}
                onClick={async () => {
                  await updateStore({
                    resource: 'stores',
                    id: store._id,
                    values: {
                      config: {
                        receiveEvents:
                          getValues('config.receiveEvents') ?? undefined,
                        sendAmplitude:
                          getValues('config.sendAmplitude') ?? undefined,
                        showOffers: getValues('config.showOffers') ?? undefined,
                        predictOffers:
                          getValues('config.predictOffers') ?? undefined,
                        isLegacy: getValues('config.isLegacy') ?? undefined,
                        modelOverrides:
                          getValues('config.modelOverrides') ?? undefined,
                      },
                    },
                  })
                  setEditingConfig(false)
                }}
              >
                <CheckIcon />
              </IconButton>
              <IconButton
                onClick={() => {
                  reset(query?.data?.data, {
                    keepDirty: false,
                    keepDefaultValues: false,
                    keepErrors: false,
                    keepTouched: false,
                    keepIsValid: false,
                    keepIsSubmitted: false,
                    keepSubmitCount: false,
                  })
                  setEditingConfig(false)
                }}
              >
                <ClearIcon />
              </IconButton>
            </>
          ) : (
            <IconButton
              onClick={() => {
                setEditingConfig(true)
              }}
            >
              <EditIcon />
            </IconButton>
          )
        }
      />
      <CardContent>
        <Grid container spacing={2}>
          <Grid size={4}>
            <Typography>Receive Events</Typography>
          </Grid>
          <Grid size={8}>
            <Box
              sx={{
                flex: 1,
                display: 'flex',
                justifyContent: 'center',
              }}
            >
              <Controller
                {...register('config.receiveEvents', {
                  required: false,
                })}
                control={control}
                render={({ field: { onChange, value, ...restField } }) => {
                  return store.effectiveConfig ? (
                    <OptionalSwitch
                      value={value}
                      effectiveValue={store.effectiveConfig.receiveEvents}
                      editing={editingConfig}
                      onChange={(value) => {
                        onChange(value)
                      }}
                      {...restField}
                    />
                  ) : (
                    <> </>
                  )
                }}
              />
            </Box>
          </Grid>
          <Grid size={4}>
            <Typography>Send Amplitude</Typography>
          </Grid>
          <Grid size={8}>
            <Box
              sx={{
                flex: 1,
                display: 'flex',
                justifyContent: 'center',
              }}
            >
              <Controller
                {...register('config.sendAmplitude', {
                  required: false,
                })}
                control={control}
                render={({ field: { onChange, value, ...restField } }) => {
                  return (
                    <OptionalSwitch
                      value={value}
                      effectiveValue={store.effectiveConfig!.sendAmplitude}
                      editing={editingConfig}
                      onChange={(value) => {
                        onChange(value)
                      }}
                      {...restField}
                    />
                  )
                }}
              />
            </Box>
          </Grid>
          <Grid size={4}>
            <Typography>Show Offers</Typography>
          </Grid>
          <Grid size={8}>
            <Box
              sx={{
                flex: 1,
                display: 'flex',
                justifyContent: 'center',
              }}
            >
              <Controller
                {...register('config.showOffers', {
                  required: false,
                })}
                control={control}
                render={({ field: { onChange, value, ...restField } }) => {
                  return store.effectiveConfig ? (
                    <OptionalSwitch
                      value={value}
                      effectiveValue={store.effectiveConfig.showOffers}
                      editing={editingConfig}
                      onChange={(value) => {
                        onChange(value)
                      }}
                      {...restField}
                    />
                  ) : (
                    <> </>
                  )
                }}
              />
            </Box>
          </Grid>
          <Grid size={4}>
            <Typography>Predict Offers</Typography>
          </Grid>
          <Grid size={8}>
            <Box
              sx={{
                flex: 1,
                display: 'flex',
                justifyContent: 'center',
              }}
            >
              <Controller
                {...register('config.predictOffers', {
                  required: false,
                })}
                control={control}
                render={({ field: { onChange, value, ...restField } }) => {
                  return store.effectiveConfig ? (
                    <OptionalSwitch
                      value={value}
                      effectiveValue={store.effectiveConfig.predictOffers}
                      editing={editingConfig}
                      onChange={(value) => {
                        onChange(value)
                      }}
                      {...restField}
                    />
                  ) : (
                    <> </>
                  )
                }}
              />
            </Box>
          </Grid>
          <Grid size={4}>
            <Typography>Legacy Store</Typography>
          </Grid>
          <Grid size={8}>
            <Box
              sx={{
                flex: 1,
                display: 'flex',
                justifyContent: 'center',
              }}
            >
              <Controller
                {...register('config.isLegacy', {
                  required: false,
                })}
                control={control}
                render={({ field: { onChange, value, ...restField } }) => {
                  return store.effectiveConfig ? (
                    <OptionalSwitch
                      value={value}
                      effectiveValue={store.effectiveConfig.isLegacy}
                      editing={editingConfig}
                      onChange={(value) => {
                        onChange(value)
                      }}
                      {...restField}
                    />
                  ) : (
                    <> </>
                  )
                }}
              />
            </Box>
          </Grid>
          <Grid size={4}>
            <Typography>Model Overrides</Typography>
          </Grid>
          <Grid size={8}>
            <Box
              sx={{
                flex: 1,
                display: 'flex',
                justifyContent: 'center',
              }}
            >
              <Controller
                {...register('config.modelOverrides', {
                  required: false,
                })}
                control={control}
                render={({ field: { value, onChange } }) => {
                  if (editingConfig) {
                    return (
                      <TextField
                        fullWidth
                        defaultValue={value ? JSON.stringify(value) : ''}
                        onChange={(event) => {
                          try {
                            const newValue = event.target.value
                              ? JSON.parse(event.target.value)
                              : undefined
                            onChange(newValue)
                          } catch (e) {
                            //ignore
                          }
                        }}
                      />
                    )
                  } else {
                    return (
                      <Typography>
                        {value ? JSON.stringify(value) : ''}
                      </Typography>
                    )
                  }
                }}
              />
            </Box>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}
