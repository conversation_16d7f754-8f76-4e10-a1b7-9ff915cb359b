'use client'

import { Refine, useList, useOne } from '@refinedev/core'
import { RefineSnackbarProvider, useNotificationProvider } from '@refinedev/mui'
import routerProvider from '@refinedev/nextjs-router'
import {
  Box,
  Button,
  CircularProgress,
  GlobalStyles,
  MenuItem,
  Select,
  Stack,
  Typography,
} from '@mui/material'
import {
  Home as HomeIcon,
  Store as StoreIcon,
  Widgets as WidgetsIcon,
  Settings as SettingsIcon,
  Analytics as AnalyticsIcon,
} from '@mui/icons-material'
import { NextAppProvider } from '@toolpad/core/nextjs'
import { AppRouterCacheProvider } from '@mui/material-nextjs/v15-appRouter'
import { DashboardLayout, ThemeSwitcher } from '@toolpad/core/DashboardLayout'
import { PageContainer } from '@toolpad/core/PageContainer'
import type { Navigation, Session } from '@toolpad/core/AppProvider'
import theme from '@/components/admin-theme'
import { useAuth as useClerkAuth, UserButton } from '@clerk/nextjs'
import { RedirectType, useParams } from 'next/navigation'
import { StoreDto } from '@packages/shared-entities'
import { useEffect, useMemo } from 'react'
import { useAuth } from '@/modules/auth/auth'
import { redirect } from 'next/navigation'
import { refineConfig } from '@/modules/config/refine'
import { useIntentnowDataProvider } from '@/modules/refine/data-provider'

function CustomActions() {
  return (
    <Stack direction="row" alignItems="center">
      <ThemeSwitcher />
    </Stack>
  )
}

function CustomAccount() {
  return <UserButton />
}

function CustomSidebarFooter({ mini }: { mini: boolean }) {
  return (
    <Box
      sx={{
        display: 'flex',
      }}
    >
      <Button
        sx={{
          flex: 1,
          margin: '5px',
          justifyContent: 'left',
        }}
      >
        <SettingsIcon
          sx={{
            margin: '15px',
          }}
        />
        {!mini && <>Settings</>}
      </Button>
    </Box>
  )
}

function CustomAppTitle({
  storeId: currentStoreId,
  stores,
}: {
  storeId: string
  stores?: StoreDto[]
}) {
  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
      }}
    >
      <Select
        value={currentStoreId}
        sx={{
          marginLeft: '10px',
          marginTop: '0px',
          marginBottom: '0px',
          border: 'none',
          boxShadow: 'none',

          // Show box shadow on hover
          transition: 'box-shadow 0.3s',
          '&:hover': {
            boxShadow: 2,
          },
        }}
        onChange={(e) => {
          const newStoreId = e.target.value
          if (newStoreId !== currentStoreId) {
            redirect(`/merchant/store/${newStoreId}`, RedirectType.push)
          }
        }}
      >
        {stores?.map((store) => (
          <MenuItem key={store._id} value={store._id}>
            <StoreIcon
              color={currentStoreId === store._id ? 'primary' : undefined}
              sx={{ marginRight: '10px' }}
            />
            <Typography
              color={currentStoreId === store._id ? 'primary' : undefined}
              variant={currentStoreId === store._id ? 'h6' : undefined}
            >
              {store.name}
            </Typography>
          </MenuItem>
        ))}
      </Select>
    </Box>
  )
}

export function StoreDashboardLayoutInner({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  const { user } = useAuth()
  const { storeId }: { storeId: string } = useParams()
  const { data: { data: store } = {}, isLoading } = useOne<StoreDto>({
    resource: 'stores',
    id: storeId,
    queryOptions: refineConfig.defaultUseOneQueryOptions,
  })
  const { data: { data: stores } = {}, isLoading: storesLoading } =
    useList<StoreDto>({
      resource: user ? `users/${user.userId}/stores` : undefined,
      pagination: {
        current: 1,
        pageSize: 1000,
      },
    })

  useEffect(() => {
    if (store) {
      document.title = `Merchant Portal - ${store.name}`
    } else {
      document.title = `Merchant Portal`
    }
  }, [store])

  if (isLoading || storesLoading) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
        }}
      >
        <CircularProgress />
      </Box>
    )
  }

  if (!store) {
    redirect(`/merchant?open-from=store-not-found`, RedirectType.replace)
  }

  return (
    <>
      <DashboardLayout
        sidebarExpandedWidth={200}
        slots={{
          appTitle:
            (stores?.length ?? 0) > 0
              ? () => <CustomAppTitle storeId={storeId} stores={stores} />
              : undefined,
          toolbarActions: CustomActions,
          toolbarAccount: CustomAccount,
          sidebarFooter: CustomSidebarFooter,
        }}
      >
        <PageContainer maxWidth={false}>{children}</PageContainer>
      </DashboardLayout>
    </>
  )
}

export default function StoreDashboardLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  const { dataProvider } = useIntentnowDataProvider()
  const { storeId }: { storeId: string } = useParams()

  const clerkAuth = useClerkAuth()
  const { user } = useAuth()
  const session: Session | undefined = useMemo(() => {
    return user
      ? {
          user: {
            id: user.userId,
            name: user.userInfo?.displayName,
            email: user.userInfo?.email,
          },
        }
      : undefined
  }, [user])

  const navigation: Navigation = [
    {
      segment: `merchant/store/${storeId}`,
      title: 'Home',
      icon: <HomeIcon />,
    },
    {
      segment: `merchant/store/${storeId}/widgets`,
      title: 'Widgets',
      icon: <WidgetsIcon />,
    },
    {
      segment: `merchant/store/${storeId}/insights`,
      title: 'Insights',
      icon: <AnalyticsIcon />,
    },
  ]

  return (
    <AppRouterCacheProvider options={{ enableCssLayer: true }}>
      <NextAppProvider
        theme={theme}
        navigation={navigation}
        branding={{
          title: `Store`,
          homeUrl: `/merchant/store/${storeId}`,
        }}
        session={session}
        authentication={{
          signIn: () => {},
          signOut: clerkAuth.signOut,
        }}
      >
        <GlobalStyles styles={{ html: { WebkitFontSmoothing: 'auto' } }} />
        <RefineSnackbarProvider>
          <Refine
            dataProvider={dataProvider}
            routerProvider={routerProvider}
            notificationProvider={useNotificationProvider}
          >
            <StoreDashboardLayoutInner>{children}</StoreDashboardLayoutInner>
          </Refine>
        </RefineSnackbarProvider>
      </NextAppProvider>
    </AppRouterCacheProvider>
  )
}
