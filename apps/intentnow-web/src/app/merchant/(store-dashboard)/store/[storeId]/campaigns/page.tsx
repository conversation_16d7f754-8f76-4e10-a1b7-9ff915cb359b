'use client'

import { refineConfig } from '@/modules/config/refine'
import {
  Box,
  Button,
  darken,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Grid2 as Grid,
  Input,
  lighten,
  styled,
  Switch,
  TextField,
  Theme,
  Typography,
} from '@mui/material'
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  ToggleOn as ToggleOnIcon,
  ToggleOff as ToggleOffIcon,
  ContentCopy as CopyIcon,
} from '@mui/icons-material'
import {
  DataGrid,
  GridActionsCellItem,
  GridColDef,
  GridValidRowModel,
} from '@mui/x-data-grid'
import {
  StoreCampaignCreateDto,
  StoreCampaignDto,
} from '@packages/shared-entities'
import { List, SaveButton, useDataGrid } from '@refinedev/mui'
import { useParams } from 'next/navigation'
import { useMemo } from 'react'
import { storeCampaignStatusLabels } from '@/modules/intentnow/stores'
import { useDialogs } from '@toolpad/core/useDialogs'
import { ConfirmDialog2 } from '@/components/confirm-dialog'
import { HttpError, useDelete, useUpdate } from '@refinedev/core'
import {
  useModalForm,
  UseModalFormReturnType,
} from '@refinedev/react-hook-form'

const getBackgroundColor = (
  color: string,
  theme: Theme,
  coefficient: number
) => ({
  backgroundColor: darken(color, coefficient),
  ...theme.applyStyles('light', {
    backgroundColor: lighten(color, coefficient),
  }),
})

const StyledDataGrid = styled(DataGrid)(({ theme }) => ({
  '& .campaign-row-theme--Acive': {
    ...getBackgroundColor(theme.palette.success.main, theme, 0.7),
    '&:hover': {
      ...getBackgroundColor(theme.palette.success.main, theme, 0.6),
    },
    '&.Mui-selected': {
      ...getBackgroundColor(theme.palette.success.main, theme, 0.5),
      '&:hover': {
        ...getBackgroundColor(theme.palette.success.main, theme, 0.4),
      },
    },
  },
}))

export default function StoreCampaignsPage() {
  const { storeId }: { storeId: string } = useParams()
  const resource = `stores/${storeId}/campaigns`

  const { mutateAsync: deleteItem } = useDelete<StoreCampaignDto>()
  const { mutateAsync: updateItem } = useUpdate<StoreCampaignDto>()
  const dialogs = useDialogs()

  const createModalFormProps = useModalForm<
    StoreCampaignDto,
    HttpError,
    StoreCampaignCreateDto
  >({
    modalProps: {},
    refineCoreProps: { resource, action: 'create' },
    syncWithLocation: false,
    defaultValues: {
      name: 'New Campaign',
      startDate: new Date(),
      endDate: undefined,
      launchRatio: 50,
      enabled: false,
    },
  })
  const {
    modal: { show: showCreateModal },
  } = createModalFormProps

  const { dataGridProps } = useDataGrid<StoreCampaignDto>({
    resource,
    sorters: { initial: [{ field: 'startDate', order: 'desc' }] },
    syncWithLocation: true,
    queryOptions: refineConfig.defaultUseListQueryOptions,
  })
  const columns = useMemo<GridColDef<GridValidRowModel>[]>(
    () => [
      {
        field: 'name',
        headerName: 'Name',
        flex: 1,
        sortable: true,
        filterable: false,
      },
      {
        field: 'startDate',
        headerName: 'Start',
        width: 180,
        sortable: true,
        filterable: false,
        valueFormatter: (value: string) => {
          return new Date(value).toLocaleString()
        },
      },
      {
        field: 'endDate',
        headerName: 'End',
        width: 180,
        sortable: true,
        filterable: false,
        valueFormatter: (value: string) => {
          return value ? new Date(value).toLocaleString() : 'Never Ends'
        },
      },
      {
        field: 'launchRatio',
        headerName: 'Ratio',
        width: 100,
        sortable: false,
        filterable: false,
        valueFormatter: (value: number) => {
          return `${value}%`
        },
      },
      {
        field: 'status',
        headerName: 'Status',
        width: 100,
        sortable: false,
        filterable: false,
        valueFormatter: (value: string) => {
          return storeCampaignStatusLabels[value]
        },
      },
      {
        field: 'actions',
        type: 'actions',
        headerName: 'Action',
        width: 70,
        sortable: false,
        filterable: false,
        getActions: ({ row }) => [
          <GridActionsCellItem
            key={2}
            icon={row.enabled ? <ToggleOffIcon /> : <ToggleOnIcon />}
            sx={{ padding: '2px 6px' }}
            label={row.enabled ? 'Disable' : 'Enable'}
            showInMenu
            onClick={async () => {
              if (row.enabled) {
                const { answer } = await dialogs.open(ConfirmDialog2, {
                  title: 'Disable Campaign',
                  description: `You are about to disable the campaign "${row.name}". Do you want to continue?`,
                })
                if (answer) {
                  //disable the campaign
                  await updateItem({
                    resource,
                    id: row.id,
                    values: {
                      enabled: false,
                    },
                  })
                }
              } else {
                const { answer } = await dialogs.open(ConfirmDialog2, {
                  title: 'Enable Campaign',
                  description: `You are about to enable the campaign "${row.name}". Do you want to continue?`,
                })
                if (answer) {
                  //enable the campaign
                  await updateItem({
                    resource,
                    id: row.id,
                    values: {
                      enabled: true,
                    },
                  })
                }
              }
            }}
          />,
          <GridActionsCellItem
            key={2}
            icon={<EditIcon />}
            sx={{ padding: '2px 6px' }}
            label="Edit"
            showInMenu
            onClick={() => {}}
          />,
          <GridActionsCellItem
            key={2}
            icon={<CopyIcon />}
            sx={{ padding: '2px 6px' }}
            label="Duplicate"
            showInMenu
            onClick={() => {}}
          />,
          <GridActionsCellItem
            key={2}
            icon={<DeleteIcon />}
            sx={{ padding: '2px 6px' }}
            label="Delete"
            showInMenu
            onClick={async () => {
              const { answer } = await dialogs.open(ConfirmDialog2, {
                title: 'Delete Campaign',
                description: `You are about to delete the campaign "${row.name}". Deleted campaign can't be recovered. Do you want to continue?`,
              })
              if (answer) {
                //Delete the campaign
                await deleteItem({
                  resource,
                  id: row._id,
                })
              }
            }}
          />,
        ],
      },
    ],
    [dialogs, resource, updateItem, deleteItem]
  )

  return (
    <Box>
      <List
        createButtonProps={{
          onClick: () => {
            showCreateModal()
          },
          variant: 'outlined',
        }}
      >
        <StyledDataGrid
          {...dataGridProps}
          columns={columns}
          getRowClassName={(params) =>
            params.row.status === 'active' ? `campaign-row-theme--Acive` : ''
          }
        />
      </List>
      <CreateCampaignModal {...createModalFormProps} />
    </Box>
  )
}

export const CreateCampaignModal: React.FC<
  UseModalFormReturnType<StoreCampaignDto, HttpError, StoreCampaignCreateDto>
> = ({
  saveButtonProps,
  modal: { visible, close },
  register,
  formState: { errors },
}) => {
  return (
    <Dialog
      open={visible}
      onClose={close}
      maxWidth={false}
      sx={{
        '& .MuiDialog-paper': {
          maxWidth: '90vw',
          maxHeight: '90vw',
        },
      }}
    >
      <DialogTitle>Create Campaign</DialogTitle>
      <DialogContent>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            padding: '20px',
          }}
        >
          <Box
            component="form"
            autoComplete="off"
            sx={{
              display: 'flex',
              flexDirection: 'column',
              width: '500px',
              gap: '10px',
            }}
          >
            <Grid
              container
              spacing={2}
              sx={{
                justifyItems: 'center',
                alignItems: 'center',
              }}
            >
              <Grid
                size={4}
                sx={{
                  alignItems: 'center',
                }}
              >
                <Typography>Campaign Name</Typography>
              </Grid>
              <Grid
                size={8}
                sx={{
                  alignItems: 'center',
                }}
              >
                <TextField
                  id="name"
                  {...register('name', {
                    required: 'This field is required',
                  })}
                  error={!!errors.name}
                  helperText={errors.name?.message}
                  margin="normal"
                  fullWidth
                />
              </Grid>
              <Grid
                size={4}
                sx={{
                  alignItems: 'center',
                }}
              >
                <Typography
                  sx={{
                    alignContent: 'center',
                  }}
                >
                  Launch Ratio
                </Typography>
              </Grid>
              <Grid
                size={8}
                sx={{
                  alignItems: 'center',
                }}
              >
                <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
                  <Input
                    id="launchRatio"
                    {...register('launchRatio', {
                      required: 'This field is required',
                      min: 1,
                      max: 100,
                      valueAsNumber: true,
                    })}
                    size="medium"
                    error={!!errors.launchRatio}
                    sx={{
                      width: '50px',
                    }}
                    inputProps={{
                      sx: {
                        textAlign: 'right',
                        '&::placeholder': {
                          textAlign: 'right',
                        },
                      },
                    }}
                  />
                  <Typography>%</Typography>
                </Box>
              </Grid>
              <Grid
                size={4}
                sx={{
                  alignItems: 'center',
                }}
              >
                <Typography
                  sx={{
                    alignContent: 'center',
                  }}
                >
                  Enabled
                </Typography>
              </Grid>
              <Grid
                size={8}
                sx={{
                  alignItems: 'center',
                }}
              >
                <Switch
                  id="enabled"
                  {...register('enabled')}
                  sx={{
                    boxShadow: 'none',
                  }}
                />
              </Grid>
            </Grid>
          </Box>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button variant="outlined" onClick={close}>
          Cancel
        </Button>
        <SaveButton {...saveButtonProps} variant="outlined" />
      </DialogActions>
    </Dialog>
  )
}
