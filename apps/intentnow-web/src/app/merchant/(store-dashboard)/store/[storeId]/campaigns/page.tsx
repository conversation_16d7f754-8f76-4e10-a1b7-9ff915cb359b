'use client'

import { refineConfig } from '@/modules/config/refine'
import {
  Box,
  Button,
  darken,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Grid2 as Grid,
  lighten,
  MenuItem,
  Select,
  styled,
  Switch,
  TextField,
  Theme,
  Typography,
} from '@mui/material'
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  ToggleOn as ToggleOnIcon,
  ToggleOff as ToggleOffIcon,
  ContentCopy as CopyIcon,
  Add as AddIcon,
} from '@mui/icons-material'
import {
  DataGrid,
  GridActionsCellItem,
  GridColDef,
  GridValidRowModel,
} from '@mui/x-data-grid'
import {
  StoreCampaignDto,
  StoreCampaignScheduleDto,
  StoreCampaignUpdateDto,
  StoreCampaignVariantDto,
  StoreOfferDto,
} from '@packages/shared-entities'
import { List, SaveButton, useDataGrid } from '@refinedev/mui'
import { useParams } from 'next/navigation'
import { useMemo, useState } from 'react'
import { storeCampaignStatusLabels } from '@/modules/intentnow/stores'
import { useDialogs } from '@toolpad/core/useDialogs'
import { ConfirmDialog2 } from '@/components/confirm-dialog'
import { HttpError, useDelete, useList, useUpdate } from '@refinedev/core'
import {
  useModalForm,
  UseModalFormReturnType,
} from '@refinedev/react-hook-form'
import { Controller, useFieldArray } from 'react-hook-form'
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns'
import { StoreCampaignEditor } from '@/components/store-campaign-editor'

const getBackgroundColor = (
  color: string,
  theme: Theme,
  coefficient: number
) => ({
  backgroundColor: darken(color, coefficient),
  ...theme.applyStyles('light', {
    backgroundColor: lighten(color, coefficient),
  }),
})

const StyledDataGrid = styled(DataGrid)(({ theme }) => ({
  '& .campaign-row-theme--Acive': {
    ...getBackgroundColor(theme.palette.success.main, theme, 0.7),
    '&:hover': {
      ...getBackgroundColor(theme.palette.success.main, theme, 0.6),
    },
    '&.Mui-selected': {
      ...getBackgroundColor(theme.palette.success.main, theme, 0.5),
      '&:hover': {
        ...getBackgroundColor(theme.palette.success.main, theme, 0.4),
      },
    },
  },
}))

export default function StoreCampaignsPage() {
  const { storeId }: { storeId: string } = useParams()
  const resource = `stores/${storeId}/campaigns`

  const { mutateAsync: deleteItem } = useDelete<StoreCampaignDto>()
  const { mutateAsync: updateItem } = useUpdate<StoreCampaignDto>()
  const dialogs = useDialogs()

  const { data: { data: offers } = {} } = useList<StoreOfferDto>({
    resource: `stores/${storeId}/offers`,
    queryOptions: refineConfig.defaultUseListQueryOptions,
    pagination: {
      mode: 'off',
    },
  })

  const createModalFormProps = useModalForm<
    StoreCampaignUpdateDto,
    HttpError,
    StoreCampaignUpdateDto
  >({
    mode: 'onBlur',
    modalProps: {},
    refineCoreProps: { resource, action: 'create' },
    syncWithLocation: false,
    warnWhenUnsavedChanges: true,
    defaultValues: {
      name: 'New Campaign',
      schedule: {
        start: new Date(),
        end: undefined,
      },
      variants: [
        {
          allocation: 50,
          offerId: offers?.[0]._id,
        },
      ],
      enabled: false,
    },
  })
  const {
    modal: { show: showCreateModal },
  } = createModalFormProps

  const cloneModalFormProps = useModalForm<
    StoreCampaignUpdateDto,
    HttpError,
    StoreCampaignUpdateDto
  >({
    mode: 'onBlur',
    modalProps: {},
    refineCoreProps: { resource, action: 'clone' },
    syncWithLocation: false,
    warnWhenUnsavedChanges: true,
  })
  const {
    modal: { show: showCloneModel },
  } = cloneModalFormProps

  const editModalFormProps = useModalForm<
    StoreCampaignUpdateDto,
    HttpError,
    StoreCampaignUpdateDto
  >({
    mode: 'onBlur',
    modalProps: {},
    refineCoreProps: { resource, action: 'edit' },
    syncWithLocation: false,
    warnWhenUnsavedChanges: true,
  })
  const {
    modal: { show: showEditModal },
  } = editModalFormProps

  const [openCampaignEditor, setOpenCampaignEditor] = useState(false)
  const [editCampaignId, setEditCampaignId] = useState<string>()

  const { dataGridProps } = useDataGrid<StoreCampaignDto>({
    resource,
    sorters: { initial: [{ field: 'schedule.start', order: 'desc' }] },
    syncWithLocation: false,
    queryOptions: refineConfig.defaultUseListQueryOptions,
  })
  const columns = useMemo<GridColDef<GridValidRowModel>[]>(
    () => [
      {
        field: 'name',
        headerName: 'Name',
        flex: 1,
        sortable: true,
        filterable: false,
      },
      {
        field: 'schedule',
        headerName: 'Schedule',
        width: 250,
        sortable: true,
        filterable: false,
        renderCell: (params) => {
          const schedule = params.row.schedule as StoreCampaignScheduleDto
          return (
            <Box
              sx={{
                flex: 1,
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                overflow: 'auto',
              }}
            >
              <Typography>
                Start: {new Date(schedule.start).toLocaleString()}
              </Typography>
              {Boolean(schedule.end) && (
                <Typography>
                  End: {new Date(schedule.end!).toLocaleString()}
                </Typography>
              )}
            </Box>
          )
        },
      },
      {
        field: 'variants',
        headerName: 'Variants',
        minWidth: 200,
        sortable: false,
        filterable: false,
        renderCell: (params) => {
          let controlAlloc = 100
          params.row.variants?.forEach((variant: StoreCampaignVariantDto) => {
            controlAlloc -= variant.allocation
          })
          return (
            <Box
              sx={{
                flex: 1,
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                overflow: 'auto',
              }}
            >
              <Typography key={`control}`}>
                {controlAlloc}% [Control]
              </Typography>
              {params.row.variants?.map(
                (variant: StoreCampaignVariantDto, index: number) => {
                  const offer = offers?.find(
                    (offer) => offer._id === variant.offerId
                  )
                  return (
                    <Typography key={`variant-${index}`}>
                      {variant.allocation}% [{offer?.name}]
                    </Typography>
                  )
                }
              )}
            </Box>
          )
        },
      },
      {
        field: 'status',
        headerName: 'Status',
        width: 100,
        sortable: false,
        filterable: false,
        valueFormatter: (value: string) => {
          return storeCampaignStatusLabels[value]
        },
      },
      {
        field: 'actions',
        type: 'actions',
        headerName: 'Action',
        width: 70,
        sortable: false,
        filterable: false,
        getActions: ({ row }) => [
          <GridActionsCellItem
            key={2}
            icon={row.enabled ? <ToggleOffIcon /> : <ToggleOnIcon />}
            sx={{ padding: '2px 6px' }}
            label={row.enabled ? 'Disable' : 'Enable'}
            showInMenu
            onClick={async () => {
              if (row.enabled) {
                const { answer } = await dialogs.open(ConfirmDialog2, {
                  title: 'Disable Campaign',
                  description: `You are about to disable the campaign "${row.name}". Do you want to continue?`,
                })
                if (answer) {
                  //disable the campaign
                  await updateItem({
                    resource,
                    id: row.id,
                    values: {
                      enabled: false,
                    },
                  })
                }
              } else {
                const { answer } = await dialogs.open(ConfirmDialog2, {
                  title: 'Enable Campaign',
                  description: `You are about to enable the campaign "${row.name}". Do you want to continue?`,
                })
                if (answer) {
                  //enable the campaign
                  await updateItem({
                    resource,
                    id: row.id,
                    values: {
                      enabled: true,
                    },
                  })
                }
              }
            }}
          />,
          <GridActionsCellItem
            key={2}
            icon={<EditIcon />}
            sx={{ padding: '2px 6px' }}
            label="Edit"
            showInMenu
            onClick={() => {
              //showEditModal(row._id)
              if (!openCampaignEditor) {
                setEditCampaignId(row._id)
                setOpenCampaignEditor(true)
              }
            }}
          />,
          <GridActionsCellItem
            key={2}
            icon={<CopyIcon />}
            sx={{ padding: '2px 6px' }}
            label="Duplicate"
            showInMenu
            onClick={() => {
              showCloneModel(row._id)
            }}
          />,
          <GridActionsCellItem
            key={2}
            icon={<DeleteIcon />}
            sx={{ padding: '2px 6px' }}
            label="Delete"
            showInMenu
            onClick={async () => {
              const { answer } = await dialogs.open(ConfirmDialog2, {
                title: 'Delete Campaign',
                description: `You are about to delete the campaign "${row.name}". Deleted campaign can't be recovered. Do you want to continue?`,
              })
              if (answer) {
                //Delete the campaign
                await deleteItem({
                  resource,
                  id: row._id,
                })
              }
            }}
          />,
        ],
      },
    ],
    [
      offers,
      dialogs,
      updateItem,
      resource,
      showEditModal,
      showCloneModel,
      deleteItem,
    ]
  )

  return (
    <Box>
      <List
        createButtonProps={{
          onClick: () => {
            showCreateModal()
          },
          variant: 'outlined',
        }}
      >
        <StyledDataGrid
          {...dataGridProps}
          columns={columns}
          getRowClassName={(params) =>
            params.row.status === 'active' ? `campaign-row-theme--Acive` : ''
          }
          rowHeight={80}
        />
      </List>
      <EditCampaignModal
        {...createModalFormProps}
        offers={offers}
        mode="create"
      />
      <EditCampaignModal
        {...cloneModalFormProps}
        offers={offers}
        mode="clone"
      />
      {editModalFormProps.modal.visible && (
        <EditCampaignModal
          key={`edit-campaign-modal-${editModalFormProps.modal.visible}`}
          {...editModalFormProps}
          offers={offers}
          mode="edit"
        />
      )}
      {openCampaignEditor && (
        <StoreCampaignEditor
          key={`campaign-editor-${openCampaignEditor}`}
          open={openCampaignEditor}
          onClose={() => {
            setOpenCampaignEditor(false)
            setEditCampaignId(undefined)
          }}
          storeId={storeId}
          campaignId={editCampaignId}
          offers={offers}
          mode="edit"
        />
      )}
    </Box>
  )
}

export function EditCampaignModal({
  saveButtonProps,
  modal: { visible, close },
  register,
  control,
  watch,
  formState: { errors, isLoading, isValidating, isReady },
  offers,
  mode,
}: UseModalFormReturnType<
  StoreCampaignUpdateDto,
  HttpError,
  StoreCampaignUpdateDto
> & {
  offers: StoreOfferDto[] | undefined
  mode: 'create' | 'clone' | 'edit'
}) {
  const {
    fields: variantFields,
    append: variantAppend,
    remove: variantRemove,
  } = useFieldArray({
    name: 'variants',
    control,
    rules: {
      required: 'Minimum 1 variant is required',
      minLength: {
        value: 1,
        message: 'Minimum 1 variant is required',
      },
      validate: (variants) => {
        let totalAlloc = 0
        variants?.forEach((variant) => {
          totalAlloc += variant.allocation ?? 0
        })
        if (totalAlloc < 1) {
          return 'Total allocation must be >= 1'
        }
        if (totalAlloc > 100) {
          return 'Total allocation must be <= 100'
        }
        return true
      },
    },
  })

  if (!visible || !isReady || isLoading || isValidating) {
    return <></>
  }

  console.debug('watch', watch())
  console.debug('errors', errors)

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Dialog
        open={visible}
        onClose={close}
        maxWidth={false}
        sx={{
          '& .MuiDialog-paper': {
            maxWidth: '90vw',
            maxHeight: '90vw',
          },
        }}
      >
        <DialogTitle>
          {mode === 'edit' ? 'Edit Campaign' : 'Create Campaign'}
        </DialogTitle>
        <DialogContent>
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              padding: '20px',
            }}
          >
            <Box
              component="form"
              autoComplete="off"
              sx={{
                display: 'flex',
                flexDirection: 'column',
                width: '600px',
                gap: '10px',
              }}
            >
              <Grid
                container
                spacing={2}
                sx={{
                  justifyItems: 'center',
                  alignItems: 'center',
                }}
              >
                <Grid
                  size={4}
                  sx={{
                    alignItems: 'center',
                  }}
                >
                  <Typography>Campaign Name</Typography>
                </Grid>
                <Grid
                  size={8}
                  sx={{
                    alignItems: 'center',
                  }}
                >
                  <TextField
                    id="name"
                    {...register('name', {
                      required: 'Campaign name is required',
                    })}
                    error={!!errors.name}
                    helperText={errors.name?.message}
                    margin="normal"
                    fullWidth
                  />
                </Grid>

                <Grid
                  size={4}
                  sx={{
                    alignItems: 'center',
                  }}
                >
                  <Typography
                    sx={{
                      alignContent: 'center',
                    }}
                  >
                    Enabled
                  </Typography>
                </Grid>
                <Grid
                  size={8}
                  sx={{
                    alignItems: 'center',
                  }}
                >
                  <Switch
                    id="enabled"
                    {...register('enabled')}
                    sx={{
                      boxShadow: 'none',
                    }}
                  />
                </Grid>

                <Grid
                  size={4}
                  sx={{
                    alignItems: 'top',
                  }}
                >
                  <Typography>Schedule</Typography>
                </Grid>
                <Grid
                  size={8}
                  sx={{
                    alignItems: 'center',
                  }}
                >
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      gap: 2,
                    }}
                  >
                    <Controller
                      {...register('schedule.start', {
                        required: false,
                        valueAsDate: true,
                      })}
                      control={control}
                      render={({ field: { onChange, ...restField } }) => {
                        return (
                          <DateTimePicker
                            label="Start Date"
                            value={restField.value ?? new Date()}
                            onChange={(date) => {
                              onChange(date)
                            }}
                            //{...restField}
                            sx={{
                              width: '250px',
                            }}
                          />
                        )
                      }}
                    />
                    <Controller
                      {...register('schedule.end', {
                        required: 'Start date is required',
                        valueAsDate: true,
                      })}
                      control={control}
                      rules={{
                        required: false,
                      }}
                      render={({ field: { onChange, ...restField } }) => {
                        return (
                          <>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <Typography>Continuous</Typography>
                              <Switch
                                checked={!restField.value}
                                onChange={(event) => {
                                  if (event.target.checked) {
                                    onChange(undefined)
                                  } else {
                                    onChange(
                                      new Date(
                                        Date.now() + 1000 * 60 * 60 * 24 * 7
                                      )
                                    )
                                  }
                                }}
                              />
                            </Box>
                            {Boolean(restField.value) && (
                              <DateTimePicker
                                label="End Date"
                                onChange={(date) => {
                                  onChange(date)
                                }}
                                {...restField}
                                sx={{
                                  width: '250px',
                                }}
                              />
                            )}
                          </>
                        )
                      }}
                    />
                  </Box>
                </Grid>

                <Grid
                  size={4}
                  sx={{
                    alignItems: 'top',
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'top' }}>
                    <Typography
                      sx={{
                        alignContent: 'center',
                      }}
                    >
                      Variants
                    </Typography>
                    <Button
                      size="small"
                      onClick={() => {
                        variantAppend({
                          allocation: 1,
                          offerId: offers?.[0]._id as any,
                        })
                      }}
                    >
                      <AddIcon />
                    </Button>
                  </Box>
                </Grid>
                <Grid
                  size={8}
                  sx={{
                    alignItems: 'center',
                  }}
                >
                  {Boolean(errors.variants?.root) && (
                    <Typography color="error">
                      {errors.variants?.root?.message}
                    </Typography>
                  )}
                  {variantFields.map((variant, index) => {
                    return (
                      <Box
                        key={`variant-${index}`}
                        sx={{
                          border: 0.5,
                          borderRadius: 1,
                          margin: '5px',
                          padding: '5px',
                          paddingTop: '10px',
                          display: 'flex',
                          gap: 2,
                          alignItems: 'center',
                        }}
                      >
                        <TextField
                          label="Allocation"
                          type="number"
                          sx={{
                            // border: 'none',
                            // boxShadow: 'none',
                            width: '80px',
                          }}
                          {...register(`variants.${index}.allocation`, {
                            required: 'Required',
                            min: {
                              value: 0,
                              message: '>=0',
                            },
                            max: {
                              value: 100,
                              message: '<=100',
                            },
                            valueAsNumber: true,
                          })}
                          error={!!errors.variants?.[index]?.allocation}
                          helperText={
                            errors.variants?.[index]?.allocation?.message
                          }
                          inputProps={{
                            step: 1,
                          }}
                        />
                        <Typography
                          sx={{ marginLeft: '0px', paddingLeft: '0px' }}
                        >
                          %
                        </Typography>

                        <Controller
                          {...register(`variants.${index}.offerId`, {
                            required: 'This field is required',
                          })}
                          control={control}
                          render={({ field }) => {
                            return (
                              <Select
                                sx={{
                                  marginLeft: '10px',
                                  border: 'none',
                                  boxShadow: 'none',
                                }}
                                {...field}
                                error={!!errors.variants?.[index]?.offerId}
                                defaultValue={variant.offerId}
                              >
                                {offers?.map((offer) => (
                                  <MenuItem key={offer._id} value={offer._id}>
                                    {offer.name}
                                  </MenuItem>
                                ))}
                              </Select>
                            )
                          }}
                        />

                        <Button
                          size="small"
                          onClick={() => {
                            variantRemove(index)
                          }}
                        >
                          <DeleteIcon />
                        </Button>
                      </Box>
                    )
                  })}
                </Grid>
              </Grid>
            </Box>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button variant="outlined" onClick={close}>
            Cancel
          </Button>
          <SaveButton {...saveButtonProps} variant="outlined">
            {mode === 'edit' ? 'Save' : 'Create'}
          </SaveButton>
        </DialogActions>
      </Dialog>
    </LocalizationProvider>
  )
}
