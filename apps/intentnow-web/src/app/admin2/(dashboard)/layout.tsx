'use client'
import * as React from 'react'
import Stack from '@mui/material/Stack'
import { NextAppProvider } from '@toolpad/core/nextjs'
import { AppRouterCacheProvider } from '@mui/material-nextjs/v15-appRouter'
import { DashboardLayout, ThemeSwitcher } from '@toolpad/core/DashboardLayout'
import { PageContainer } from '@toolpad/core/PageContainer'
import type { Navigation, Session } from '@toolpad/core/AppProvider'
import {
  Dashboard as DashboardIcon,
  Store as StoreIcon,
  PlayCircleFilledWhite as PlayIcon,
} from '@mui/icons-material'
import theme from '@/components/admin-theme'
import { useAuth, UserButton, useUser } from '@clerk/nextjs'
import Image from 'next/image'

const navigation: Navigation = [
  {
    segment: 'admin2',
    title: 'Dashboard',
    icon: <DashboardIcon />,
  },
  {
    kind: 'header',
    title: 'Merchants',
  },
  {
    segment: 'admin2/store-configs',
    title: 'Store Configs',
    icon: <StoreIcon />,
  },
  {
    segment: 'admin2/store-widgets',
    title: 'Store Widgets',
    icon: <StoreIcon />,
  },
  {
    segment: 'admin2/store-reports',
    title: 'Store Reports',
    icon: <StoreIcon />,
  },
  {
    segment: 'admin2/store-onboarding',
    title: 'Store Onboarding',
    icon: <PlayIcon />,
  },
  {
    segment: 'admin2/store-debug',
    title: 'Store Debug Tools',
    icon: <StoreIcon />,
  },
  {
    kind: 'header',
    title: 'Consumers',
  },
]

function CustomActions() {
  return (
    <Stack direction="row" alignItems="center">
      <ThemeSwitcher />
    </Stack>
  )
}

function CustomAccount() {
  return <UserButton />
}

export default function Layout(props: { children: React.ReactNode }) {
  const clerkAuth = useAuth()
  const clerkUser = useUser()

  const session: Session | undefined = React.useMemo(() => {
    return clerkUser?.user
      ? {
          user: {
            id: clerkUser.user.id,
            name: clerkUser.user.fullName,
            email: clerkUser.user.primaryEmailAddress?.emailAddress,
          },
        }
      : undefined
  }, [clerkUser])

  return (
    <AppRouterCacheProvider options={{ enableCssLayer: true }}>
      <NextAppProvider
        theme={theme}
        navigation={navigation}
        branding={{
          logo: (
            <Image
              src="https://api.intentnow.com/cdn//intentnow-images/intentnow-logo-1.png"
              alt=""
              width={40}
              height={40}
            />
          ),
          title: 'IntentNow Admin',
          homeUrl: '/admin2',
        }}
        session={session}
        authentication={{
          signIn: () => {},
          signOut: clerkAuth.signOut,
        }}
      >
        <DashboardLayout
          slots={{
            toolbarActions: CustomActions,
            toolbarAccount: CustomAccount,
          }}
        >
          <PageContainer>{props.children}</PageContainer>
        </DashboardLayout>
      </NextAppProvider>
    </AppRouterCacheProvider>
  )
}
