import type {
  BaseRecord,
  CreateParams,
  DataProvider,
  GetListParams,
  GetListResponse,
  GetOneParams,
  GetOneResponse,
  UpdateParams,
  UpdateResponse,
} from '@refinedev/core'
import { useAuthedApiFetch } from '../auth/fetch'
import { PaginationMetaDto } from '@packages/shared-entities'

export function useIntentnowDataProvider() {
  const {
    authedApiFetch,
    authedApiPostFetch,
    authedApiPatchFetch,
    baseApiUrl,
  } = useAuthedApiFetch()

  const dataProvider: DataProvider = {
    getOne: async <TData extends BaseRecord = BaseRecord>({
      resource,
      id,
    }: GetOneParams): Promise<GetOneResponse<TData>> => {
      const response = await authedApiFetch<TData>(
        `/api/intentnow/${resource}/${id}`
      )
      return {
        data: {
          ...response,
          id: response._id,
        },
      }
    },
    update: async <TData extends BaseRecord = BaseRecord, TVariables = any>({
      resource,
      id,
      variables,
    }: UpdateParams<TVariables>): Promise<UpdateResponse<TData>> => {
      const response = await authedApiPatchFetch<TData>([
        `/api/intentnow/${resource}/${id}`,
        variables,
      ])
      return {
        data: {
          ...response,
          id: response._id,
        },
      }
    },
    getList: async <TData extends BaseRecord = BaseRecord>({
      resource,
      pagination,
    }: GetListParams): Promise<GetListResponse<TData>> => {
      const params = new URLSearchParams()
      if (pagination?.current) {
        params.append('page', pagination?.current?.toString())
      }
      if (pagination?.pageSize) {
        params.append('limit', pagination?.pageSize?.toString())
      }

      const response = await authedApiFetch<{
        data: TData[]
        meta: PaginationMetaDto
      }>(`/api/intentnow/${resource}?${params.toString()}`)

      return {
        data: response.data.map((x): TData => {
          return {
            ...x,
            id: x._id,
          } as any as TData
        }),
        total: response.meta.total,
      }
    },
    create: async <TData extends BaseRecord = BaseRecord, TVariables = any>({
      resource,
      variables,
    }: CreateParams<TVariables>) => {
      const response = await authedApiPostFetch<TData>([
        `/api/intentnow/${resource}`,
        variables,
      ])
      return {
        data: {
          ...response,
          id: response._id,
        },
      }
    },
    deleteOne: () => {
      throw new Error('Not implemented')
    },
    getApiUrl: () => baseApiUrl,
    // Optional methods:
    // getMany: () => { /* ... */ },
    // createMany: () => { /* ... */ },
    // deleteMany: () => { /* ... */ },
    // updateMany: () => { /* ... */ },
    // custom: () => { /* ... */ },
  }

  return {
    dataProvider,
  }
}
