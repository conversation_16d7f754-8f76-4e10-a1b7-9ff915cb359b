import useSWR from 'swr'
import { useAuth } from '../auth/auth'
import { useAuthedApiFetch } from '../auth/fetch'
import { useShopifyApp } from '../shopify/app'
import { shopifyApiFetch } from '../shopify/fetch'
import {
  ActiveShopifyStoreConfig,
  ShopifyAppSettingsResponseDto,
  ShopifyStore,
  ShopifyStoreImage,
  StoreAnalyticsSettingsDto,
  StoreLaunchConfig,
  WidgetType,
} from '@packages/shared-entities'

export function useStoreAppSettings() {
  const shopifyApp = useShopifyApp()

  return useSWR(
    shopifyApp ? '/intentnow/app-settings' : null,
    shopifyApiFetch<ShopifyAppSettingsResponseDto>(shopifyApp)
  )
}

export function useStoreAnalyticsSettings() {
  const shopifyApp = useShopifyApp()

  return useSWR(
    shopifyApp ? '/intentnow/analytics-settings' : null,
    shopifyApiFetch<StoreAnalyticsSettingsDto>(shopifyApp)
  )
}

// Admin only
export function useStoreInfo(
  appHandle: string | undefined,
  shop: string | undefined
) {
  const { authedApiPostFetch } = useAuthedApiFetch()

  return useSWR(
    shop
      ? [
          '/api/admin/intentnow/store-info',
          {
            appHandle,
            shop,
          },
        ]
      : null,
    authedApiPostFetch
  )
}

// Admin only
export function useStoreAppSettingsForShop(
  appHandle: string | undefined,
  shop: string | undefined
) {
  const { authedApiPostFetch } = useAuthedApiFetch()

  return useSWR(
    shop
      ? [
          '/api/admin/intentnow/app-settings',
          {
            appHandle,
            shop,
            update: false,
          },
        ]
      : null,
    authedApiPostFetch<{
      webPixel: any
      promoEmbedActivationLink: string | undefined
      promoEmbedActivated: boolean
    }>
  )
}

// Admin only
export function useFeatureSettingsForShop(
  appHandle: string | undefined,
  shop: string | undefined
) {
  const { authedApiPostFetch } = useAuthedApiFetch()

  return useSWR(
    shop
      ? [
          '/api/admin/intentnow/feature-settings',
          {
            appHandle,
            shop,
          },
        ]
      : null,
    authedApiPostFetch<{
      storeConfig: {
        id: string
        name: string
        isEnabled: boolean
        defaultValue: {
          predictWithEvents?: boolean
          getDiscount?: boolean
        }
      }
      modelExperiment: {
        id: string
        name: string
        status: string
        groups: any[]
      }
      storeAdminConfig: {
        debug: boolean
        discounts: boolean
        analytics: boolean
      }
    }>
  )
}

// Admin only
export function useStoreConfigs() {
  const { user } = useAuth()
  const { authedApiFetch } = useAuthedApiFetch()

  const {
    data: storeConfigs,
    isLoading,
    error,
    mutate,
  } = useSWR<ShopifyStore[]>(
    user ? `/api/admin/intentnow/store-configs` : undefined,
    authedApiFetch
  )

  return { storeConfigs, isLoading, error, mutate }
}

export function useStoreConfigByShop(
  shop: string | undefined,
  loadOnce = false
) {
  const { authedApiFetch, authedApiPostFetch } = useAuthedApiFetch()
  const { user } = useAuth()
  const {
    data: storeConfig,
    isLoading,
    error,
    mutate,
    isValidating,
  } = useSWR<ShopifyStore>(
    shop && user ? `/api/admin/intentnow/store-configs/${shop}` : undefined,
    authedApiFetch,
    loadOnce
      ? {
          revalidateOnFocus: false,
          revalidateOnReconnect: false,
          refreshInterval: 0,
          dedupingInterval: Infinity,
        }
      : undefined
  )

  async function updateStoreConfig(data: any) {
    if (!shop) {
      return
    }

    return await authedApiPostFetch([
      `/api/admin/intentnow/update-store-config`,
      {
        shop,
        data,
      },
    ])
  }

  async function createStoreWidget(widgetType: WidgetType) {
    if (!shop) {
      return
    }

    return await authedApiPostFetch<{
      widgetId: string
    }>([
      `/api/admin/intentnow/create-store-widget`,
      {
        shop,
        widgetType,
      },
    ])
  }

  async function updateStoreWidget(widgetId: string, data: any) {
    if (!shop) {
      return
    }

    return await authedApiPostFetch([
      `/api/admin/intentnow/update-store-widget`,
      {
        shop,
        widgetId,
        data,
      },
    ])
  }

  async function deleteStoreWidget(widgetId: string) {
    if (!shop) {
      return
    }

    return await authedApiPostFetch([
      `/api/admin/intentnow/delete-store-widget`,
      {
        shop,
        widgetId,
      },
    ])
  }

  async function duplicateStoreWidget(fromWidgetId: string) {
    if (!shop) {
      return
    }

    return await authedApiPostFetch([
      `/api/admin/intentnow/duplicate-store-widget`,
      {
        shop,
        fromWidgetId,
      },
    ])
  }

  return {
    storeConfig,
    isLoading,
    isValidating,
    error,
    mutate,
    updateStoreConfig,
    createStoreWidget,
    updateStoreWidget,
    deleteStoreWidget,
    duplicateStoreWidget,
  }
}

//Admin only
export function useStoreImagesAdmin(shop: string | undefined) {
  const { authedApiPostFetch } = useAuthedApiFetch()
  const { user } = useAuth()
  const {
    data: storeImages,
    isLoading,
    error,
    mutate,
  } = useSWR<ShopifyStoreImage[]>(
    user && shop
      ? [`/api/admin/intentnow/store-images/list`, { shop }]
      : undefined,
    authedApiPostFetch
  )

  async function deleteStoreImages(imageId: string) {
    if (!shop) {
      return
    }

    return await authedApiPostFetch([
      `/api/admin/intentnow/store-images/delete`,
      {
        shop,
        imageId,
      },
    ])
  }

  return {
    storeImages,
    isLoading,
    error,
    mutate,
    deleteStoreImages,
  }
}

//Admin only
export function useLaunchConfigs(shop: string | undefined) {
  const { authedApiPostFetch } = useAuthedApiFetch()
  const { user } = useAuth()
  const {
    data: launchConfigs,
    isLoading,
    error,
    mutate,
  } = useSWR<StoreLaunchConfig[]>(
    user && shop
      ? [`/api/admin/intentnow/launch-configs/list`, { shop }]
      : undefined,
    authedApiPostFetch
  )

  async function createLaunchConfig(
    data: Omit<
      StoreLaunchConfig,
      '_id' | 'experimentKey' | 'storeId' | 'createdAt'
    >
  ) {
    if (!shop) {
      return
    }

    return await authedApiPostFetch([
      `/api/admin/intentnow/launch-configs/create`,
      {
        shop,
        data,
      },
    ])
  }

  return { launchConfigs, isLoading, error, mutate, createLaunchConfig }
}

export function useActiveStoreConfig(shop: string | undefined) {
  const { authedApiPostFetch } = useAuthedApiFetch()
  const { user } = useAuth()
  const {
    data: activeStoreConfig,
    isLoading,
    error,
    mutate,
  } = useSWR<ActiveShopifyStoreConfig>(
    user && shop
      ? [`/api/admin/intentnow/store-configs/active`, { shop }]
      : undefined,
    authedApiPostFetch
  )

  return { activeStoreConfig, isLoading, error, mutate }
}
