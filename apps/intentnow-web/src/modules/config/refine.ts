import { UseOneProps, useTableProps } from '@refinedev/core'

export const refineConfig = {
  defaultUseOneQueryOptions: {
    enabled: true,
    staleTime: 1000 * 60,
    refetchOnWindowFocus: true,
    refetchOnReconnect: true,
    retry: 0,
  } satisfies UseOneProps<any, any, any>['queryOptions'],
  defaultUseTableQueryOptions: {
    enabled: true,
    retry: 0,
  } as useTableProps<any, any, any>['queryOptions'],
}
