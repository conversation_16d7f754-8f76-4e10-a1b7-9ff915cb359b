'use client'

import {
  <PERSON>,
  Button,
  Dialog,
  <PERSON>alogActions,
  Dialog<PERSON>ontent,
  DialogTitle,
  Grid2 as Grid,
  MenuItem,
  Select,
  Switch,
  TextField,
  Typography,
} from '@mui/material'
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  ToggleOn as ToggleOnIcon,
  ToggleOff as ToggleOffIcon,
  ContentCopy as CopyIcon,
  Add as AddIcon,
} from '@mui/icons-material'
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns'
import { DatePicker } from '@mui/x-date-pickers/DatePicker'
import { TimePicker } from '@mui/x-date-pickers/TimePicker'
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { StoreCampaignDto, StoreOfferDto } from '@packages/shared-entities'
import { HttpError, useOne, useUpdate } from '@refinedev/core'
import { Controller, useFieldArray } from 'react-hook-form'
import { rest } from 'lodash'
import { useDialogs } from '@toolpad/core/useDialogs'
import { SaveButton } from '@refinedev/mui'
import { useForm } from '@refinedev/react-hook-form'
import { ConfirmDialog2 } from './confirm-dialog'

export function StoreCampaignEditor({
  mode,
  open,
  onClose,
  storeId,
  campaignId,
  offers,
}: {
  mode: 'create' | 'clone' | 'edit'
  open: boolean
  onClose: () => void
  storeId: string
  campaignId?: string | undefined
  offers: StoreOfferDto[] | undefined
}) {
  const resource = `stores/${storeId}/campaigns`

  const dialogs = useDialogs()
  const { mutateAsync: updateItem } = useUpdate<StoreCampaignDto>()

  // const {
  //   data: { data: existingCampaign } = {},
  //   isLoading: campaignLoading,
  //   error: campaignError,
  // } = useOne<StoreCampaignDto>({
  //   resource,
  //   id: campaignId,
  //   queryOptions: {
  //     enabled: Boolean(storeId && campaignId),
  //   },
  // })
  // const {
  //   register,
  //   control,
  //   //handleSubmit,
  //   watch,
  //   setValue,
  //   formState: { errors, isValid, disabled },
  // } = useForm<StoreCampaignDto>({
  //   mode: 'onBlur',
  //   values: existingCampaign,
  //   disabled: !existingCampaign,
  // })

  const {
    saveButtonProps,
    register,
    control,
    watch,
    setValue,
    getValues,
    reset,
    formState: {
      errors,
      isDirty,
      disabled,
      isLoading,
      isReady,
      isValidating,
      dirtyFields,
    },
  } = useForm<StoreCampaignDto, HttpError, StoreCampaignDto>({
    mode: 'onSubmit',
    reValidateMode: 'onSubmit',
    refineCoreProps: {
      resource,
      action: 'edit',
      id: campaignId,
      queryOptions: {
        enabled: Boolean(campaignId),
        staleTime: 1,
      },
      onMutationSuccess: () => {
        onClose()
      },
    },
    // resetOptions: {
    //   keepDirty: false,
    //   keepErrors: false,
    //   keepValues: true,
    //   keepDefaultValues: true,
    //   keepTouched: false,
    //   keepSubmitCount: false,
    //   keepIsValid: false,
    //   keepIsSubmitted: false,
    //   keepDirtyValues: false,
    //   keepIsSubmitSuccessful: false,
    //   keepFieldsRef: false,
    //   keepIsValidating: false,
    // },
  })

  const {
    fields: variantFields,
    append: variantAppend,
    remove: variantRemove,
  } = useFieldArray({
    name: 'variants',
    control,
    rules: {
      required: 'Minimum 1 variant is required',
      minLength: {
        value: 1,
        message: 'Minimum 1 variant is required',
      },
      validate: (variants) => {
        let totalAlloc = 0
        variants?.forEach((variant) => {
          totalAlloc += variant.allocation ?? 0
        })
        if (totalAlloc < 1) {
          return 'Total allocation must be >= 1'
        }
        if (totalAlloc > 100) {
          return 'Total allocation must be <= 100'
        }
        return true
      },
    },
  })

  // if (!existingCampaign) {
  //   return <></>
  // }

  if (!open || disabled || !isReady || isLoading || isValidating) {
    return <></>
  }

  console.debug('watch', watch())
  console.debug('errors', errors)
  console.debug('isDirty', isDirty)
  console.debug('dirtyFields', dirtyFields)

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Dialog
        open={open}
        //onClose={onClose}
        maxWidth={false}
        sx={{
          '& .MuiDialog-paper': {
            maxWidth: '90vw',
            maxHeight: '90vw',
          },
        }}
        disableEscapeKeyDown={true}
      >
        <DialogTitle>
          {mode === 'edit' ? 'New Edit Campaign' : 'Create Campaign'}
        </DialogTitle>
        <DialogContent>
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              padding: '20px',
            }}
          >
            <Box
              component="form"
              autoComplete="off"
              sx={{
                display: 'flex',
                flexDirection: 'column',
                width: '600px',
                gap: '10px',
              }}
            >
              <Grid
                container
                spacing={2}
                sx={{
                  justifyItems: 'center',
                  alignItems: 'center',
                }}
              >
                <Grid
                  size={4}
                  sx={{
                    alignItems: 'center',
                  }}
                >
                  <Typography>Campaign Name</Typography>
                </Grid>
                <Grid
                  size={8}
                  sx={{
                    alignItems: 'center',
                  }}
                >
                  <TextField
                    id="name"
                    {...register('name', {
                      required: 'Campaign name is required',
                    })}
                    error={!!errors.name}
                    helperText={errors.name?.message}
                    margin="normal"
                    fullWidth
                  />
                </Grid>

                <Grid
                  size={4}
                  sx={{
                    alignItems: 'center',
                  }}
                >
                  <Typography
                    sx={{
                      alignContent: 'center',
                    }}
                  >
                    Enabled
                  </Typography>
                </Grid>
                <Grid
                  size={8}
                  sx={{
                    alignItems: 'center',
                  }}
                >
                  <Switch
                    id="enabled"
                    {...register('enabled')}
                    sx={{
                      boxShadow: 'none',
                    }}
                  />
                </Grid>

                <Grid
                  size={4}
                  sx={{
                    alignItems: 'top',
                  }}
                >
                  <Typography>Schedule</Typography>
                </Grid>
                <Grid
                  size={8}
                  sx={{
                    alignItems: 'center',
                  }}
                >
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      gap: 2,
                    }}
                  >
                    <Controller
                      {...register('schedule.start', {
                        required: 'Start date is required',
                        valueAsDate: true,
                      })}
                      control={control}
                      render={({ field: { onChange, ...restField } }) => {
                        return (
                          <DateTimePicker
                            label="Start Date"
                            //{...restField}
                            value={
                              restField.value
                                ? new Date(restField.value)
                                : new Date()
                            }
                            onChange={(date) => {
                              onChange(date)
                            }}
                            sx={{
                              width: '250px',
                            }}
                          />
                        )
                      }}
                    />
                    <Controller
                      {...register('schedule.end', {
                        required: false,
                        valueAsDate: true,
                      })}
                      control={control}
                      rules={{
                        required: false,
                      }}
                      render={({ field: { onChange, ...restField } }) => {
                        return (
                          <>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <Typography>Continuous</Typography>
                              <Switch
                                defaultChecked={true}
                                checked={!watch('schedule.end')}
                                onChange={(event) => {
                                  if (event.target.checked) {
                                    if (getValues('schedule.end')) {
                                      setValue('schedule.end', undefined)
                                    }
                                  } else {
                                    if (!getValues('schedule.end')) {
                                      onChange(
                                        new Date(
                                          Date.now() + 1000 * 60 * 60 * 24 * 7
                                        )
                                      )
                                    }
                                  }
                                }}
                              />
                            </Box>
                            {Boolean(watch('schedule.end')) && (
                              <DateTimePicker
                                label="End Date"
                                //{...restField}
                                value={
                                  restField.value
                                    ? new Date(restField.value)
                                    : undefined
                                }
                                onChange={(date) => {
                                  onChange(date)
                                }}
                                sx={{
                                  width: '250px',
                                }}
                              />
                            )}
                          </>
                        )
                      }}
                    />
                  </Box>
                </Grid>

                <Grid
                  size={4}
                  sx={{
                    alignItems: 'top',
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'top' }}>
                    <Typography
                      sx={{
                        alignContent: 'center',
                      }}
                    >
                      Variants
                    </Typography>
                    <Button
                      size="small"
                      onClick={() => {
                        variantAppend({
                          allocation: 1,
                          offerId: offers?.[0]._id as any,
                        })
                      }}
                    >
                      <AddIcon />
                    </Button>
                  </Box>
                </Grid>
                <Grid
                  size={8}
                  sx={{
                    alignItems: 'center',
                  }}
                >
                  {Boolean(errors.variants?.root) && (
                    <Typography color="error">
                      {errors.variants?.root?.message}
                    </Typography>
                  )}
                  {variantFields.map((variant, index) => {
                    return (
                      <Box
                        key={`variant-${index}`}
                        sx={{
                          border: 0.5,
                          borderRadius: 1,
                          margin: '5px',
                          padding: '5px',
                          paddingTop: '10px',
                          display: 'flex',
                          gap: 2,
                          alignItems: 'center',
                        }}
                      >
                        <TextField
                          label="Allocation"
                          type="number"
                          sx={{
                            // border: 'none',
                            // boxShadow: 'none',
                            width: '80px',
                          }}
                          {...register(`variants.${index}.allocation`, {
                            required: 'Required',
                            min: {
                              value: 0,
                              message: '>=0',
                            },
                            max: {
                              value: 100,
                              message: '<=100',
                            },
                            valueAsNumber: true,
                          })}
                          error={!!errors.variants?.[index]?.allocation}
                          helperText={
                            errors.variants?.[index]?.allocation?.message
                          }
                          inputProps={{
                            step: 1,
                          }}
                        />
                        <Typography
                          sx={{ marginLeft: '0px', paddingLeft: '0px' }}
                        >
                          %
                        </Typography>

                        <Controller
                          {...register(`variants.${index}.offerId`, {
                            required: 'This field is required',
                          })}
                          control={control}
                          render={({ field }) => {
                            return (
                              <Select
                                sx={{
                                  marginLeft: '10px',
                                  border: 'none',
                                  boxShadow: 'none',
                                }}
                                {...field}
                                error={!!errors.variants?.[index]?.offerId}
                                defaultValue={variant.offerId}
                              >
                                {offers?.map((offer) => (
                                  <MenuItem key={offer._id} value={offer._id}>
                                    {offer.name}
                                  </MenuItem>
                                ))}
                              </Select>
                            )
                          }}
                        />

                        <Button
                          size="small"
                          onClick={() => {
                            variantRemove(index)
                          }}
                        >
                          <DeleteIcon />
                        </Button>
                      </Box>
                    )
                  })}
                </Grid>
              </Grid>
            </Box>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button
            variant="outlined"
            onClick={async () => {
              if (Object.keys(dirtyFields).length) {
                const { answer } = await dialogs.open(ConfirmDialog2, {
                  title: 'Discard Changes',
                  description:
                    'You have unsaved changes. Are you sure you want to close without saving?',
                })
                if (answer) {
                  onClose()
                }
              } else {
                onClose()
              }
            }}
          >
            Cancel
          </Button>
          <SaveButton variant="outlined" {...saveButtonProps}>
            {mode === 'edit' ? 'Save' : 'Create'}
          </SaveButton>
        </DialogActions>
      </Dialog>
    </LocalizationProvider>
  )
}
