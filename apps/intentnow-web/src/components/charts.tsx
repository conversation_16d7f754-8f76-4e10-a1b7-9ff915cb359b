import {
  AggregatedEventChartDto,
  FunnelChartDto,
} from '@packages/shared-entities'
import { BarChart, BarItem, BarLabelContext } from '@mui/x-charts/BarChart'
import { Box, CircularProgress, Typography } from '@mui/material'

export function AggregatedEventChart({
  chartData,
}: {
  chartData?: AggregatedEventChartDto
}) {
  if (!chartData) {
    return (
      <Box
        sx={{
          width: '100%',
          height: '100%',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <CircularProgress />
      </Box>
    )
  }

  const noData = !chartData.groups.length

  const data = chartData.groups.map((g, index) => ({
    data: g.aggregatedValues.map((v) => Math.floor(v.value * 100) / 100),
    label: g.groupName,
    id: index,
  }))

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        height: '100%',
      }}
    >
      <Typography
        variant="h6"
        sx={{
          textAlign: 'center',
        }}
      >
        {chartData.chartName}
      </Typography>
      {noData && (
        <Box
          sx={{
            flex: 1,
            display: 'flex',
            justifyContent: 'center',
          }}
        >
          <Typography sx={{ textAlign: 'center' }}>No data</Typography>
        </Box>
      )}
      {!noData && (
        <BarChart
          sx={{
            flex: 1,
          }}
          title="User Revenue"
          layout="vertical"
          xAxis={[
            {
              data: chartData.groups[0].aggregatedValues.map((v) => v.name),
            },
          ]}
          yAxis={[{ label: chartData.unit }]}
          series={data}
          barLabel={(item: BarItem) => {
            return String(item.value)
          }}
        />
      )}
    </Box>
  )
}

export function CustomFunnelChart({
  chartData,
}: {
  chartData?: FunnelChartDto
}) {
  if (!chartData) {
    return (
      <Box
        sx={{
          width: '100%',
          height: '100%',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <CircularProgress />
      </Box>
    )
  }

  const noData =
    !chartData.groups.length || chartData.groups[0].funnel.length < 2

  const steps = chartData.groups[0]?.funnel.map((f) => f.funnelStep).slice(1)

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        height: '100%',
      }}
    >
      <Typography
        variant="h6"
        sx={{
          textAlign: 'center',
        }}
      >
        {chartData.chartName}
      </Typography>
      {noData && (
        <Box
          sx={{
            flex: 1,
            display: 'flex',
            justifyContent: 'center',
          }}
        >
          <Typography sx={{ textAlign: 'center' }}>No data</Typography>
        </Box>
      )}
      {!noData && (
        <BarChart
          sx={{
            flex: 1,
          }}
          title="User Conversion"
          layout="vertical"
          xAxis={[
            {
              data: steps,
              // tickLabelStyle: {
              //   angle: -90,
              // },
            },
          ]}
          yAxis={[{ label: 'conversion %' }]}
          series={chartData.groups.map((g, index) => ({
            data: g.funnel
              .slice(1)
              .map((f) =>
                f.conversionRate === undefined
                  ? 100
                  : Math.floor(f.conversionRate * 10000) / 100
              ),
            label: g.groupName,
            id: index,
          }))}
          barLabel={(item: BarItem, context: BarLabelContext) => {
            if (context.bar.width < 50) {
              return ''
            }
            return chartData.groups[item.seriesId as number].funnel[
              item.dataIndex + 1
            ].conversionRate !== undefined
              ? String(`${item.value}%`)
              : String(``)
          }}
        />
      )}
    </Box>
  )
}
