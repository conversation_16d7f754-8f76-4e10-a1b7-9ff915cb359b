import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
} from '@mui/material'

export function ConfirmDialog({
  open,
  title,
  description,
  confirm,
  defaultAnswer,
}: {
  open: boolean
  title: string
  description: string
  confirm: (answer: boolean) => void
  defaultAnswer?: boolean
}) {
  function handleConfirm() {
    confirm(true)
  }

  function handleCancel() {
    confirm(false)
  }

  return (
    <>
      <Dialog
        open={open}
        onClose={handleCancel}
        aria-labelledby="confirm-dialog-title"
        aria-describedby="confirm-dialog-description"
      >
        <DialogTitle id="confirm-dialog-title">{title}</DialogTitle>
        <DialogContent>
          <DialogContentText id="confirm-dialog-description">
            {description}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCancel} autoFocus={Boolean(!defaultAnswer)}>
            Cancel
          </Button>
          <Button onClick={handleConfirm} autoFocus={Boolean(defaultAnswer)}>
            Continue
          </Button>
        </DialogActions>
      </Dialog>
    </>
  )
}
