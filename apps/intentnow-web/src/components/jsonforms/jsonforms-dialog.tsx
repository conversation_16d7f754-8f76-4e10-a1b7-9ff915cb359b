import { JsonSchema, UISchemaElement } from '@jsonforms/core'
import { materialCells } from '@jsonforms/material-renderers'
import { JsonForms } from '@jsonforms/react'
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
} from '@mui/material'
import { useEffect, useState } from 'react'
import { customRenderers } from './renderers'

export interface ErrorObject {
  keyword: string
  instancePath: string
  schemaPath: string
  params: any
  propertyName?: string
  message?: string
}

export function JsonFormsDialog({
  open,
  title,
  description,
  dataSchema,
  formData,
  uiSchema,
  width,
  onValidate,
  onSave,
  onCancel,
}: {
  open: boolean
  title: string
  description: string
  dataSchema: JsonSchema
  uiSchema: UISchemaElement
  formData: any
  width?: string
  onValidate?: (data: any) => ErrorObject[] | undefined
  onSave: (data: any) => Promise<void>
  onCancel: () => void
}) {
  const [data, setData] = useState<any>()
  const [errors, setErrors] = useState<any>()
  const [addtionalErrors, setAddtionalErrors] = useState<ErrorObject[]>()

  useEffect(() => {
    setData(formData)
  }, [formData])

  if (!open) {
    return <></>
  }

  return (
    <>
      <Dialog
        open={open}
        onClose={(event, reason) => {
          if (reason === 'escapeKeyDown') {
            onCancel()
          }
        }}
        aria-labelledby="dialog-title"
        aria-describedby="dialog-description"
        maxWidth={false}
        slotProps={{
          paper: {
            style: {
              maxWidth: '80%',
              width: width || '700px',
              maxHeight: '80%',
            },
          },
        }}
      >
        <DialogTitle id="dialog-title">{title}</DialogTitle>
        <DialogContent>
          <DialogContentText id="dialog-description">
            {description}
          </DialogContentText>
          <Box
            sx={{
              width: '100%',
              overflow: 'hidden',
              overflowY: 'auto',
            }}
          >
            <JsonForms
              schema={dataSchema}
              uischema={uiSchema}
              data={data}
              cells={materialCells}
              renderers={customRenderers}
              additionalErrors={addtionalErrors ?? []}
              onChange={({ data, errors }) => {
                setErrors(errors)

                if (!errors?.length) {
                  const validationErrors = onValidate?.(data)
                  setAddtionalErrors(validationErrors)
                } else {
                  setAddtionalErrors(undefined)
                }

                setData(data)
              }}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={onCancel} autoFocus={true}>
            Cancel
          </Button>
          <Button
            onClick={() => {
              if (errors?.length || addtionalErrors?.length) {
                throw new Error('Validation errors')
              }
              onSave(data)
            }}
            disabled={Boolean(errors?.length || addtionalErrors?.length)}
          >
            Save
          </Button>
        </DialogActions>
      </Dialog>
    </>
  )
}
