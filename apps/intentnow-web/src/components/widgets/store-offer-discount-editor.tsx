import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Card<PERSON>ontent,
  CardHeader,
  CircularProgress,
  Container,
  TextField,
  ToggleButton,
  ToggleButtonGroup,
  Typography,
} from '@mui/material'
import PercentIcon from '@mui/icons-material/Percent'
import MoneyOffIcon from '@mui/icons-material/MoneyOff'
import { OfferDiscountCodeConfigDto } from '@packages/shared-entities'
import { Controller, useForm } from 'react-hook-form'
import { useEffect } from 'react'
import _ from 'lodash'

export function StoreOfferDiscountEditor({
  visible,
  isLoading,
  discountConfig,
  initialDiscountConfig,
  createDiscountConfig,
  setDiscountConfig,
  outsideHeight,
  setDiscountConfigValid,
}: {
  visible: boolean
  isLoading: boolean
  discountConfig: OfferDiscountCodeConfigDto | undefined
  initialDiscountConfig: OfferDiscountCodeConfigDto | undefined
  createDiscountConfig: () => void
  setDiscountConfig: (discountConfig: OfferDiscountCodeConfigDto) => void
  outsideHeight?: number
  setDiscountConfigValid: (valid: boolean) => void
}) {
  const {
    register,
    control,
    //handleSubmit,
    watch,
    setValue,
    formState: {
      errors: formErrors,
      isValid: formIsValid,
      disabled: formDisabled,
    },
  } = useForm<OfferDiscountCodeConfigDto>({
    mode: 'onChange',
    values: initialDiscountConfig,
    disabled: !initialDiscountConfig,
  })

  const changedDiscountConfig = watch()
  useEffect(() => {
    if (!formDisabled) {
      let newTitle = changedDiscountConfig.discount.title
      if (changedDiscountConfig.discount.type === 'percent') {
        newTitle = `${changedDiscountConfig.discount.percentOff}% Off`
      } else {
        newTitle = `$${changedDiscountConfig.discount.amountOff} Off`
      }

      if (newTitle !== changedDiscountConfig.discount.title) {
        changedDiscountConfig.discount.title = newTitle
        setValue('discount.title', newTitle)
        setDiscountConfig(changedDiscountConfig)
      } else if (!_.isEqual(discountConfig, changedDiscountConfig)) {
        setDiscountConfig(changedDiscountConfig)
      }
    }
    // if (
    //   changedDiscountConfig?.discount &&
    //   !changedDiscountConfig.discount.type
    // ) {
    //   setValue('discount.type', 'percent')
    // }
  }, [
    changedDiscountConfig,
    formIsValid,
    formDisabled,
    discountConfig,
    setDiscountConfig,
    setValue,
  ])

  useEffect(() => {
    if (!formDisabled) {
      setDiscountConfigValid(formIsValid)
    }
  }, [formDisabled, formIsValid, setDiscountConfigValid])

  if (!visible) {
    return <></>
  }

  if (isLoading) {
    return (
      <Box
        sx={{
          flex: 1,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <CircularProgress />
      </Box>
    )
  }

  if (!initialDiscountConfig) {
    return (
      <Box
        id="discount-editor"
        sx={{
          flex: 1,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <Button
          variant="contained"
          onClick={() => {
            if (!initialDiscountConfig) {
              createDiscountConfig()
            }
          }}
        >
          Create Discount Config
        </Button>
      </Box>
    )
  }

  return (
    <Box
      id="discount-editor"
      sx={{
        flex: 1,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        width: '100%',
        overflowY: 'auto',
        height: `calc(100vh - ${outsideHeight ?? 0}px)`,
      }}
    >
      <Container
        sx={{
          width: '600px',
          maxWidth: '600px',
          margin: '20px',
        }}
      >
        <Card
          sx={{
            paddingBottom: '10px',
          }}
        >
          <CardHeader title="Discount Code" />
          <CardContent>
            <Box
              sx={{
                marginBottom: '20px',
              }}
            >
              <TextField
                {...register('codeGen.length', {
                  required: 'This field is required',
                  min: {
                    value: 4,
                    message: 'Must be at least 4 characters',
                  },
                  max: {
                    value: 20,
                    message: 'Must be at most 20 characters',
                  },
                  valueAsNumber: true,
                })}
                label="Character Length"
                type="number"
                error={!!formErrors.codeGen?.length}
                helperText={formErrors.codeGen?.length?.message}
                size="medium"
                inputProps={{
                  step: 1,
                }}
              />
            </Box>
            <Box
              sx={{
                marginBottom: '20px',
              }}
            >
              <TextField
                {...register('codeGen.alphabet', {
                  required: 'Code alphabet is required',
                })}
                error={!!formErrors.codeGen?.alphabet}
                helperText={formErrors.codeGen?.alphabet?.message}
                label="Alphabet"
                type="text"
                sx={{
                  width: '350px',
                }}
              />
            </Box>
            <Box sx={{}}>
              <TextField
                {...register('codeGen.prefix', {
                  validate: (value) => {
                    if (value && value.length > watch('codeGen.length') - 4) {
                      return 'Code prefix must be shorter than code length - 4'
                    }
                    return true
                  },
                })}
                error={!!formErrors.codeGen?.prefix}
                helperText={formErrors.codeGen?.prefix?.message}
                label="Prefix"
                type="text"
                sx={{
                  width: '200px',
                }}
              />
            </Box>
          </CardContent>
        </Card>
        <Card
          sx={{
            marginTop: '10px',
          }}
        >
          <CardHeader title="Discount Type" />
          <CardContent>
            <Controller
              name="discount.type"
              control={control}
              render={({ field }) => {
                return (
                  <ToggleButtonGroup {...field} exclusive>
                    <ToggleButton value="percent">
                      <PercentIcon />
                      Percent Off
                    </ToggleButton>
                    <ToggleButton value="amount">
                      <MoneyOffIcon />
                      Amount Off
                    </ToggleButton>
                  </ToggleButtonGroup>
                )
              }}
            />
            {watch('discount.type') === 'percent' && (
              <Card
                sx={{
                  marginTop: '20px',
                }}
              >
                <CardContent>
                  <Box
                    sx={{
                      display: 'flex',
                      gap: 1,
                      alignItems: 'center',
                    }}
                  >
                    <TextField
                      {...register('discount.percentOff', {
                        required: true,
                        min: {
                          value: 1,
                          message: 'Must be at least 1%',
                        },
                        max: {
                          value: 100,
                          message: 'Must be at most 100%',
                        },
                        valueAsNumber: true,
                      })}
                      error={!!formErrors.discount?.percentOff}
                      helperText={formErrors.discount?.percentOff?.message}
                      label="Percent Off"
                      type="number"
                      variant="outlined"
                      sx={{
                        margin: '5px',
                        width: '200px',
                      }}
                      inputProps={{
                        step: 1,
                      }}
                      // onChange={(e) => {
                      //   const value = Math.floor(Number(e.target.value))
                      //   // if (value < 1) {
                      //   //   value = 1
                      //   // }
                      //   // if (value > 100) {
                      //   //   value = 100
                      //   // }
                      //   setValue('discount.percentOff', value)
                      // }}
                    />
                    <Typography>%</Typography>
                  </Box>
                </CardContent>
              </Card>
            )}
            {watch('discount.type') === 'amount' && (
              <Card
                sx={{
                  marginTop: '20px',
                }}
              >
                <CardContent>
                  <Box
                    sx={{
                      display: 'flex',
                      gap: 1,
                      alignItems: 'center',
                    }}
                  >
                    <TextField
                      {...register('discount.amountOff', {
                        required: true,
                        min: {
                          value: 0.01,
                          message: 'Must be at least 0.01',
                        },
                        valueAsNumber: true,
                      })}
                      error={!!formErrors.discount?.amountOff}
                      helperText={formErrors.discount?.amountOff?.message}
                      label="Amount Off"
                      type="number"
                      variant="outlined"
                      inputProps={{
                        step: 0.01,
                      }}
                      sx={{
                        margin: '5px',
                        width: '200px',
                      }}
                      // onChange={(e) => {
                      //   const value =
                      //     Math.floor(Number(e.target.value) * 100) / 100
                      //   setValue('discount.amountOff', value)
                      // }}
                    />
                    <Typography>USD</Typography>
                  </Box>
                </CardContent>
              </Card>
            )}
          </CardContent>
        </Card>
        <Card
          sx={{
            marginTop: '10px',
          }}
        >
          <CardHeader title="Limits" />
          <CardContent>
            <TextField
              {...register('discount.minimumOrder', {
                required: 'Minimum order amount is required',
                min: {
                  value: 0,
                  message: 'Must be at least 0',
                },
                valueAsNumber: true,
              })}
              error={!!formErrors.discount?.minimumOrder}
              helperText={formErrors.discount?.minimumOrder?.message}
              label="Minimum Order"
              type="number"
              variant="outlined"
              sx={{
                margin: '5px',
              }}
            />
          </CardContent>
        </Card>
        <Card
          sx={{
            marginTop: '10px',
          }}
        >
          <CardHeader title="Durations" />
          <CardContent>
            <TextField
              {...register('discountDurationInMinutes', {
                required: 'This field is required',
                min: {
                  value: 1,
                  message: 'Must be at least 1 minute',
                },
                valueAsNumber: true,
              })}
              error={!!formErrors.discountDurationInMinutes}
              helperText={formErrors.discountDurationInMinutes?.message}
              label="Valid-to-Redeem (minutes)"
              type="number"
              variant="outlined"
              sx={{
                margin: '5px',
              }}
            />
            <TextField
              {...register('cacheValidDurationInMinutes', {
                required: 'This field is required',
                min: {
                  value: 1,
                  message: 'Must be at least 1 minute',
                },
                valueAsNumber: true,
              })}
              error={!!formErrors.cacheValidDurationInMinutes}
              helperText={formErrors.cacheValidDurationInMinutes?.message}
              label="Continue-to-Show (minutes)"
              type="number"
              variant="outlined"
              sx={{
                margin: '5px',
              }}
            />
          </CardContent>
        </Card>
      </Container>
    </Box>
  )
}
