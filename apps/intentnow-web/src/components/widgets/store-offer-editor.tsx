'use client'

import {
  <PERSON>,
  Button,
  CircularProgress,
  TextField,
  ToggleButton,
  ToggleButtonGroup,
  Typography,
} from '@mui/material'
import { useEffect, useMemo, useState } from 'react'
import WidgetsIcon from '@mui/icons-material/Widgets'
import DiscountIcon from '@mui/icons-material/Discount'
import SaveIcon from '@mui/icons-material/Save'
import CloseIcon from '@mui/icons-material/Close'
import CloseFullscreenIcon from '@mui/icons-material/CloseFullscreen'
//import { DiscountEditor } from './discount-editor'
import { Widget3DialogTeaserEditor } from './widget3-dialog-teaser-editor'
import {
  defaultStoreOfferDiscountCodeConfig,
  DiscountContent,
  OfferDiscountCodeConfigDto,
  OfferWidgetConfigDto,
  StoreDto,
  StoreOfferDto,
} from '@packages/shared-entities'
import { useCreate, useOne, useUpdate } from '@refinedev/core'
import { refineConfig } from '@/modules/config/refine'
import { generatePreviewDiscount } from '@/modules/intentnow/stores'
import { useForm } from 'react-hook-form'
import { StoreOfferDiscountEditor } from './store-offer-discount-editor'
import { useDialogs } from '@toolpad/core/useDialogs'
import { ConfirmDialog2 } from '../confirm-dialog'

export function StoreOfferEditor({
  storeId,
  offerId,
  initialOfferData,
  outsideHeight,
  onClose,
}: {
  storeId: string
  offerId: string | undefined
  initialOfferData?: StoreOfferDto
  outsideHeight?: number
  onClose: (result: any) => Promise<void>
}) {
  const dialogs = useDialogs()
  const { mutateAsync: updateStoreOffer } = useUpdate<StoreOfferDto>({
    resource: `stores/${storeId}/offers`,
  })
  const { mutateAsync: createStoreOffer } = useCreate<StoreOfferDto>({
    resource: `stores/${storeId}/offers`,
  })
  const {
    //data: { data: store } = {},
    isLoading: storeLoading,
    error: storeError,
  } = useOne<StoreDto>({
    resource: 'stores',
    id: storeId,
    queryOptions: refineConfig.defaultUseOneQueryOptions,
  })
  const {
    data: { data: existingOffer } = {},
    isLoading: offerLoading,
    error: offerError,
    //refetch: offerRefetch,
  } = useOne<StoreOfferDto>({
    resource: `stores/${storeId}/offers`,
    id: offerId,
    queryOptions: {
      ...refineConfig.defaultUseOneQueryOptions,
      enabled: Boolean(storeId && offerId),
    },
  })
  const isLoading = storeLoading || Boolean(offerId && offerLoading)
  const error = storeError || (offerId ? offerError : undefined)

  const [discount, setDiscount] = useState<DiscountContent>()

  const [dialogData, setDialogData] = useState<
    OfferWidgetConfigDto['dialog'] | undefined
  >()
  const [initialDialogData, setInitialDialogData] = useState<
    OfferWidgetConfigDto['dialog'] | undefined
  >()
  const [teaserData, setTeaserData] = useState<
    OfferWidgetConfigDto['teaser'] | undefined
  >()
  const [initialTeaserData, setInitialTeaserData] = useState<
    OfferWidgetConfigDto['teaser'] | undefined
  >()
  const [discountConfig, setDiscountConfig] =
    useState<OfferDiscountCodeConfigDto>()
  const [initialDiscountConfig, setInitialDiscountConfig] =
    useState<OfferDiscountCodeConfigDto>()
  const [discountConfigValid, setDiscountConfigValid] = useState(true)

  const [edit, setEdit] = useState<'dialog' | 'teaser' | 'discount'>('discount')
  const [dialogChanged, setDialogChanged] = useState(false)
  const [teaserChanged, setTeaserChanged] = useState(false)
  const [discountChanged, setDiscountChanged] = useState(false)
  const [ready, setReady] = useState(false)

  const {
    register,
    //handleSubmit,
    //watch,
    getValues,
    formState: {
      errors: formErrors,
      isValid: formIsValid,
      isDirty: formIsDirty,
    },
  } = useForm<{ name: string }>({
    mode: 'onChange',
    values: {
      name:
        existingOffer?.name ??
        (offerId ? undefined : `${initialOfferData?.name} - Copy`) ??
        '',
    },
  })

  const changed = useMemo(() => {
    const changed =
      dialogChanged || teaserChanged || discountChanged || formIsDirty
    return changed
  }, [dialogChanged, teaserChanged, discountChanged, formIsDirty])

  useEffect(() => {
    if (!ready) {
      if (!isLoading && !error) {
        if (existingOffer?.widgetConfig) {
          setDialogData(existingOffer.widgetConfig.dialog)
          setInitialDialogData(existingOffer.widgetConfig.dialog)
          setTeaserData(existingOffer.widgetConfig.teaser)
          setInitialTeaserData(existingOffer.widgetConfig.teaser)
        } else {
          setDialogData(
            offerId ? undefined : initialOfferData?.widgetConfig?.dialog
          )
          setInitialDialogData(undefined)
          setTeaserData(
            offerId ? undefined : initialOfferData?.widgetConfig?.teaser
          )
          setInitialTeaserData(undefined)
        }

        if (existingOffer?.discountConfig) {
          setInitialDiscountConfig(existingOffer.discountConfig)
          setDiscountConfig(existingOffer.discountConfig)
        } else {
          setInitialDiscountConfig(
            offerId ? undefined : initialOfferData?.discountConfig
          )
          setDiscountConfig(undefined)
        }

        setReady(true)
      }
    }
  }, [ready, isLoading, error, existingOffer, initialOfferData, offerId])

  useEffect(() => {
    if (discountConfig) {
      setDiscount(generatePreviewDiscount(discountConfig))
    } else {
      setDiscount(undefined)
    }
  }, [discountConfig])

  // function reloadData() {
  //   setDialogData(undefined)
  //   setInitialDialogData(undefined)
  //   setTeaserData(undefined)
  //   setInitialTeaserData(undefined)
  //   setDiscountConfig(undefined)
  //   setDiscount(undefined)

  //   setDialogChanged(false)
  //   setTeaserChanged(false)
  //   setDiscountChanged(false)
  //   setWidgetDataChanged(false)

  //   setReady(false)

  //   offerRefetch()
  // }

  if (isLoading) {
    return (
      <Box
        sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}
      >
        <CircularProgress />
      </Box>
    )
  }

  if (error) {
    return (
      <Box
        sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}
      >
        <Typography>Unexpected Error</Typography>
      </Box>
    )
  }

  return (
    <>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          height: '100vh',
          //overflowY: 'auto',
        }}
      >
        <Box
          id="top-bar"
          sx={
            {
              //boxShadow: 1,
            }
          }
        >
          <Box sx={{ display: 'flex' }}>
            <Box sx={{ display: 'inline-flex', padding: '8px' }}>
              <ToggleButtonGroup
                disabled={!ready}
                value={edit}
                exclusive
                onChange={(_, newEdit) => {
                  newEdit && setEdit(newEdit)
                }}
                sx={{
                  maxHeight: '50px',
                }}
              >
                <ToggleButton value="discount">
                  <DiscountIcon />
                  Discount
                </ToggleButton>
                <ToggleButton value="dialog" disabled={!discount}>
                  <WidgetsIcon />
                  Dialog
                </ToggleButton>
                <ToggleButton value="teaser" disabled={!discount}>
                  <CloseFullscreenIcon />
                  Teaser
                </ToggleButton>
              </ToggleButtonGroup>

              <Box
                sx={{
                  marginTop: '5px',
                  marginLeft: '20px',
                }}
              >
                <TextField
                  {...register('name', {
                    required: `Offer name can't be empty`,
                  })}
                  error={Boolean(formErrors.name)}
                  helperText={formErrors.name?.message}
                  sx={{
                    width: '200px',
                  }}
                  label="Offer Name"
                />
              </Box>
            </Box>
            <Box
              sx={{
                flex: 1,
                display: 'inline-flex',
                flexDirection: 'row-reverse',
                alignItems: 'center',
              }}
            >
              <Button
                variant="text"
                color="inherit"
                disabled={
                  !changed || !ready || !formIsValid || !discountConfigValid
                }
                onClick={async () => {
                  if (ready && changed && formIsValid) {
                    if (existingOffer) {
                      await updateStoreOffer({
                        id: existingOffer._id,
                        values: {
                          name: getValues('name'),
                          ...(dialogData || teaserData
                            ? {
                                widgetConfig: {
                                  type: 'widget3',
                                  dialog: dialogData,
                                  teaser: teaserData,
                                },
                              }
                            : {}),
                          discountConfig,
                        },
                      })
                      await onClose({})
                    } else {
                      await createStoreOffer({
                        values: {
                          name: getValues('name'),
                          ...(dialogData || teaserData
                            ? {
                                widgetConfig: {
                                  type: 'widget3',
                                  dialog: dialogData,
                                  teaser: teaserData,
                                },
                              }
                            : {}),
                          discountConfig,
                        },
                      })
                      await onClose({})
                    }
                  }
                }}
              >
                <SaveIcon />
                {existingOffer ? 'Save' : 'Create'}
              </Button>
              <Button
                variant="text"
                color="inherit"
                //disabled={!changed || !ready}
                onClick={async () => {
                  if (changed) {
                    const { answer } = await dialogs.open(ConfirmDialog2, {
                      title: 'Discard Changes',
                      description: `${existingOffer?.name ? `Offer popup "${existingOffer.name}"` : 'This new offer popup'} has unsaved changes. Are you sure you want to discard these changes?`,
                    })
                    if (answer) {
                      onClose({})
                    }
                  } else {
                    onClose({})
                  }
                }}
              >
                <CloseIcon />
                Close
              </Button>
            </Box>
          </Box>
        </Box>
        <Box
          sx={{
            flex: 1,
            display: 'flex',
          }}
        >
          {['dialog', 'teaser'].includes(edit) && (
            <Widget3DialogTeaserEditor
              isLoading={isLoading || !ready}
              edit={edit}
              dialogData={dialogData}
              setDialogData={setDialogData}
              initialDialogData={initialDialogData}
              teaserData={teaserData}
              initialTeaserData={initialTeaserData}
              setTeaserData={setTeaserData}
              setDialogChanged={setDialogChanged}
              setTeaserChanged={setTeaserChanged}
              discount={discount}
              storeImages={[]}
              outsideHeight={75 + (outsideHeight ?? 0)}
            />
          )}

          <StoreOfferDiscountEditor
            visible={edit === 'discount'}
            isLoading={isLoading || !ready}
            discountConfig={discountConfig}
            setDiscountConfig={(dc) => {
              setDiscountChanged(true)
              setDiscountConfig(dc)
            }}
            initialDiscountConfig={initialDiscountConfig}
            createDiscountConfig={() => {
              if (!initialDiscountConfig) {
                setInitialDiscountConfig(defaultStoreOfferDiscountCodeConfig)
              }
            }}
            setDiscountConfigValid={setDiscountConfigValid}
            outsideHeight={75 + (outsideHeight ?? 0)}
          />
        </Box>
      </Box>
    </>
  )
}
