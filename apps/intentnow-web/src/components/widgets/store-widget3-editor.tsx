import {
  DiscountConfig,
  DiscountContent,
  legacyWidgetStores,
  ShopifyStoreWidget,
  Widget3DialogConfig,
  Widget3TeaserConfig,
} from '@packages/shared-entities'
import {
  Box,
  Button,
  TextField,
  ToggleButton,
  ToggleButtonGroup,
  Typography,
} from '@mui/material'
import { useEffect, useMemo, useState } from 'react'
import WidgetsIcon from '@mui/icons-material/Widgets'
import DiscountIcon from '@mui/icons-material/Discount'
import SaveIcon from '@mui/icons-material/Save'
import CloseIcon from '@mui/icons-material/Close'
import PreviewIcon from '@mui/icons-material/Preview'
import CloseFullscreenIcon from '@mui/icons-material/CloseFullscreen'
import StorefrontIcon from '@mui/icons-material/Storefront'
import PlayArrowIcon from '@mui/icons-material/PlayArrow'
import LinkIcon from '@mui/icons-material/Link'
import LinkOffIcon from '@mui/icons-material/LinkOff'
import {
  useStoreConfigByShop,
  useStoreImagesAdmin,
} from '@/modules/intentnow/settings'
import { DiscountEditor } from './discount-editor'
import { Widget3DialogTeaserEditor } from './widget3-dialog-teaser-editor'
import {
  generatePreviewDiscount,
  getPreviewWidgetUrl,
} from '@/modules/intentnow/widgets'

export function StoreWidget3Editor({
  shop,
  widgetId,
  outsideHeight,
}: {
  shop: string
  widgetId: string | undefined
  outsideHeight?: number
}) {
  const isLegacyStore = legacyWidgetStores.includes(shop)
  const {
    storeConfig,
    isLoading,
    isValidating,
    error,
    mutate,
    updateStoreConfig,
    updateStoreWidget,
  } = useStoreConfigByShop(shop)
  const { storeImages } = useStoreImagesAdmin(shop)
  const [widgetData, setWidgetData] = useState<ShopifyStoreWidget>()
  const [discount, setDiscount] = useState<DiscountContent>()

  const [dialogData, setDialogData] = useState<
    Widget3DialogConfig | undefined
  >()
  const [initialDialogData, setInitialDialogData] = useState<
    Widget3DialogConfig | undefined
  >()
  const [teaserData, setTeaserData] = useState<
    Widget3TeaserConfig | undefined
  >()
  const [initialTeaserData, setInitialTeaserData] = useState<
    Widget3TeaserConfig | undefined
  >()
  const [discountConfig, setDiscountConfig] = useState<DiscountConfig>()

  const [edit, setEdit] = useState<'dialog' | 'teaser' | 'discount'>('dialog')
  const [dialogChanged, setDialogChanged] = useState(false)
  const [teaserChanged, setTeaserChanged] = useState(false)
  const [discountChanged, setDiscountChanged] = useState(false)
  const [widgetDataChanged, setWidgetDataChanged] = useState(false)
  const [ready, setReady] = useState(false)

  const changed = useMemo(
    () =>
      dialogChanged || teaserChanged || discountChanged || widgetDataChanged,
    [dialogChanged, teaserChanged, discountChanged, widgetDataChanged]
  )

  const isSelected = useMemo(() => {
    return (
      storeConfig &&
      widgetData &&
      storeConfig?.promoConfig?.selectedWidgetId === widgetData?._id
    )
  }, [storeConfig, widgetData])

  useEffect(() => {
    if (!ready) {
      const widgetData = storeConfig?._widgets?.find((w) => w._id === widgetId)

      if (!isLoading && !isValidating && !error && widgetData) {
        setWidgetData(widgetData)
        setDialogData(widgetData.widget3?.dialog)
        setInitialDialogData(widgetData.widget3?.dialog)
        setTeaserData(widgetData.widget3?.teaser)
        setInitialTeaserData(widgetData.widget3?.teaser)
        setDiscountConfig(widgetData.discount)

        if (widgetData.discount) {
          generatePreviewDiscount(widgetData.discount)
        } else {
          setEdit('discount')
        }

        setReady(true)
      }
    }
  }, [
    ready,
    widgetId,
    storeConfig,
    isLoading,
    isValidating,
    error,
    setWidgetData,
  ])

  useEffect(() => {
    if (discountConfig) {
      setDiscount(generatePreviewDiscount(discountConfig))
    } else {
      setDiscount(undefined)
    }
  }, [discountConfig])

  function reloadData() {
    setDialogData(undefined)
    setInitialDialogData(undefined)
    setTeaserData(undefined)
    setInitialTeaserData(undefined)
    setDiscountConfig(undefined)
    setWidgetData(undefined)
    setDiscount(undefined)

    setDialogChanged(false)
    setTeaserChanged(false)
    setDiscountChanged(false)
    setWidgetDataChanged(false)

    setReady(false)

    mutate()
  }

  if (isLegacyStore) {
    return <></>
  }

  return (
    <>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          height: '100vh',
        }}
      >
        <Box
          id="top-bar"
          sx={{
            boxShadow: 1,
          }}
        >
          <Box>
            <Box sx={{ display: 'inline-flex' }}>
              <Box sx={{ display: 'inline-flex', paddingLeft: '10px' }}>
                <StorefrontIcon />
                <Typography
                  sx={{
                    fontWeight: 'bolder',
                  }}
                >
                  {storeConfig?.name
                    ? `${storeConfig?.name} [${storeConfig.shop}]`
                    : shop}
                </Typography>
              </Box>
              {widgetData && (
                <>
                  <Box sx={{ display: 'inline-flex', paddingLeft: '10px' }}>
                    <WidgetsIcon />
                    <Typography
                      sx={{
                        fontWeight: 'bolder',
                      }}
                    >
                      {widgetData._id}
                    </Typography>
                  </Box>
                </>
              )}
              {widgetData && isSelected && (
                <>
                  <Box sx={{ display: 'inline-flex', paddingLeft: '10px' }}>
                    <LinkIcon />
                    <Typography sx={{}}>In Use</Typography>
                  </Box>
                </>
              )}
              {widgetData && !isSelected && (
                <>
                  <Box sx={{ display: 'inline-flex', paddingLeft: '10px' }}>
                    <LinkOffIcon />
                    <Typography sx={{}}>Unselected</Typography>
                  </Box>
                </>
              )}
            </Box>
          </Box>
          <Box sx={{ display: 'flex' }}>
            <Box sx={{ display: 'inline-flex', padding: '8px' }}>
              <ToggleButtonGroup
                disabled={!ready}
                value={edit}
                exclusive
                onChange={(_, newEdit) => {
                  newEdit && setEdit(newEdit)
                }}
                aria-label="text alignment"
              >
                <ToggleButton value="dialog" aria-label="left aligned">
                  <WidgetsIcon />
                  Dialog
                </ToggleButton>
                <ToggleButton value="teaser" aria-label="left aligned">
                  <CloseFullscreenIcon />
                  Teaser
                </ToggleButton>
                <ToggleButton value="discount" aria-label="centered">
                  <DiscountIcon />
                  Discount
                </ToggleButton>
              </ToggleButtonGroup>
              {widgetData && (
                <Box
                  sx={{
                    marginLeft: '20px',
                  }}
                >
                  <TextField
                    sx={{
                      width: '200px',
                    }}
                    value={widgetData?.name}
                    label="Widget Name"
                    onChange={(e) => {
                      setWidgetData({
                        ...widgetData,
                        name: e.target.value,
                      })
                      setWidgetDataChanged(true)
                    }}
                  />
                </Box>
              )}
            </Box>
            <Box
              sx={{
                flex: 1,
                display: 'inline-flex',
                flexDirection: 'row-reverse',
              }}
            >
              <Button
                variant="text"
                color="inherit"
                disabled={!changed || !ready}
                onClick={async () => {
                  if (changed && widgetData) {
                    await updateStoreWidget(widgetData._id, {
                      ...(widgetDataChanged
                        ? {
                            name: widgetData.name,
                          }
                        : {}),

                      ...(discountChanged
                        ? {
                            discount: discountConfig,
                          }
                        : {}),

                      ...((dialogChanged || teaserChanged) &&
                      dialogData &&
                      teaserData
                        ? {
                            widget3: {
                              dialog: dialogData,
                              teaser: teaserData,
                            },
                          }
                        : {}),
                    })

                    reloadData()
                  }
                }}
              >
                <SaveIcon />
                Save
              </Button>
              <Button
                variant="text"
                color="inherit"
                disabled={!changed || !ready}
                onClick={async () => {
                  reloadData()
                }}
              >
                <CloseIcon />
                Discard Changes
              </Button>

              <Button
                variant="text"
                color="inherit"
                disabled={!ready || changed}
                onClick={() => {
                  window.open(
                    getPreviewWidgetUrl(
                      storeConfig?.shop,
                      storeConfig?.website,
                      widgetData
                    ),
                    '_blank'
                  )
                }}
              >
                <PreviewIcon />
                Live Preview
              </Button>

              <Button
                variant="text"
                color="inherit"
                disabled={!ready || changed || isSelected}
                onClick={async () => {
                  if (widgetData) {
                    await updateStoreConfig({
                      promoConfig: {
                        selectedWidgetId: widgetData?._id,
                      },
                    })
                    mutate()
                  }
                }}
              >
                <PlayArrowIcon />
                Go Live
              </Button>
            </Box>
          </Box>
        </Box>
        <Box
          sx={{
            flex: 1,
            display: 'flex',
          }}
        >
          {['dialog', 'teaser'].includes(edit) && (
            <Widget3DialogTeaserEditor
              isLoading={isLoading || !ready}
              edit={edit}
              dialogData={dialogData}
              setDialogData={setDialogData}
              initialDialogData={initialDialogData}
              teaserData={teaserData}
              initialTeaserData={initialTeaserData}
              setTeaserData={setTeaserData}
              setDialogChanged={setDialogChanged}
              setTeaserChanged={setTeaserChanged}
              discount={discount}
              storeImages={storeImages}
              outsideHeight={112 + (outsideHeight ?? 0)}
            />
          )}

          {edit === 'discount' && (
            <Box
              sx={{
                flex: 1,
                display: 'flex',
                justifyContent: 'center',
              }}
            >
              <DiscountEditor
                isLoading={isLoading || !ready}
                discountConfig={discountConfig}
                setDiscountConfig={(dc) => {
                  setDiscountChanged(true)
                  setDiscountConfig(dc)
                }}
              />
            </Box>
          )}
        </Box>
      </Box>
    </>
  )
}
