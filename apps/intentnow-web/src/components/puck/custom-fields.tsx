import { <PERSON><PERSON><PERSON>, <PERSON>, FieldLabel, ObjectField } from '@measured/puck'
import {
  widget3DialogPuckConfig,
  widget3TeaserPuckConfig,
} from '@packages/intentnow-tag'
import { ShopifyStoreImage } from '@packages/shared-entities'
import { MuiColorInput } from 'mui-color-input'

//Apply custom field editing components here so we don't have to introduce extra UI dependencies into the intentnow-tag package
export function getWidget3DialogPuckConfigWithCustomFields() {
  const newConfig = {
    ...widget3DialogPuckConfig,
  }

  applyColorsCustomFields(
    newConfig.root!.fields!.minimizeButton! as ObjectField,
    ['color']
  )
  applyColorsCustomFields(
    (newConfig.root!.fields!.styles! as ObjectField).objectFields
      .colors as ObjectField,
    ['color', 'backgroundColor']
  )
  applyColorsCustomFields(
    (newConfig.components.Text.fields!.styles! as ObjectField).objectFields
      .colors as ObjectField,
    ['color', 'backgroundColor']
  )
  applyColorsCustomFields(
    (newConfig.components.Button.fields!.styles! as ObjectField).objectFields
      .colors as ObjectField,
    ['color', 'backgroundColor', 'hoverBackgroundColor']
  )

  return newConfig
}

export function applyImageCustomFields(
  config: typeof widget3DialogPuckConfig,
  storeImages?: ShopifyStoreImage[]
) {
  const newConfig = {
    ...config,
  }

  if (!storeImages) {
    storeImages = []
  }

  const imageOptions = [
    {
      label: 'None',
      value: '',
    },
    ...storeImages.map((image) => ({
      label: image.name,
      value: image.imageUrl,
    })),
  ]

  newConfig.components.Image.fields!.imageUrl = {
    label: 'Image',
    type: 'select',
    options: imageOptions,
  }
  ;(
    newConfig.components.DialogContainer.fields!.image as ObjectField
  ).objectFields.imageUrl = {
    label: 'Image',
    type: 'select',
    options: imageOptions,
  }

  return newConfig
}

export function getWidget3TeaserPuckConfigWithCustomFields() {
  const newConfig = {
    ...widget3TeaserPuckConfig,
  }

  applyColorsCustomFields(
    (newConfig.components.Text.fields!.styles! as ObjectField).objectFields
      .colors as ObjectField,
    ['color', 'backgroundColor']
  )

  return newConfig
}

function applyColorsCustomFields(
  colorsField: ObjectField<any> | undefined,
  fields: string[]
) {
  if (!colorsField) {
    return
  }

  for (const field of fields) {
    if (colorsField.objectFields[field]) {
      colorsField.objectFields[field] = {
        ...ColorCustomField,
        label: colorsField.objectFields[field].label,
      }
    }
  }
}

export const ColorCustomField: Field<string | undefined> = {
  type: 'custom',
  label: 'Color',
  render: (props: {
    field: CustomField<string | undefined>
    name: string
    id: string
    value: string | undefined
    onChange: (value: string | undefined) => void
    readOnly?: boolean
  }) => {
    return (
      <div>
        <FieldLabel label={props.field?.label ?? props.name}>
          <MuiColorInput
            sx={{
              paddingTop: '6px',
              paddingBottom: '6px',
            }}
            fullWidth={true}
            format="hex"
            value={props.value ?? ''}
            disabled={props.readOnly}
            onChange={props.onChange}
          />
        </FieldLabel>
      </div>
    )
  },
}
