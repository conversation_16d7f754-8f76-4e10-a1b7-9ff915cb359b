import { DiscountConfig } from './intentnow'
import { Widget2Config, Widget3Config } from './widget'

export const defaultDiscountConfig: DiscountConfig = {
  codeGen: {
    length: 10,
    alphabet: 'ABCDEFGHIJKLMNPQRSTUVWXYZ',
  },
  discountDurationInMinutes: 540,
  cacheValidDurationInMinutes: 360,
  discountInput: {
    appliesOncePerCustomer: true,
    usageLimit: 1,
    customerSelection: {
      all: true,
    },
    customerGets: {
      items: {
        all: true,
      },
      value: {
        percentage: 0.25,
      },
    },
    title: '25% off',
  },
}

export const defaultWidget2Config: Widget2Config = {
  dialog: {
    mainStyles: {
      color: 'black',
      backgroundColor: 'lightblue',
      font: {
        family: 'Arial',
        size: 16,
      },
    },
    mainImage: {
      desktopPosition: 'right',
      mobilePosition: 'top',
      path: 'oofos/oofos-1.jpg',
    },
    texts: [
      {
        text: 'Take another look',
        styles: {
          font: {
            size: 15,
          },
          box: {
            paddingTop: 30,
          },
        },
      },
      {
        text: '{{discountTitle}}!',
        styles: {
          font: {
            size: 74,
          },
        },
      },
      {
        text: 'With any purchase, during this visit only.*',
        styles: {
          font: {
            size: 15,
          },
        },
      },
    ],
    copyCodeBlock: {
      codeTextStyles: {
        font: {
          size: 20,
        },
        box: {},
      },
      cancelText: {
        text: 'NO THANKS',
        styles: {
          font: {
            size: 11,
          },
          box: {},
        },
      },
      copyButton: {
        text: 'COPY CODE',
        styles: {
          color: 'white',
          backgroundColor: 'blue',
          font: {
            size: 12,
          },
          box: {
            paddingTop: 10,
            paddingBottom: 10,
            paddingLeft: 30,
            paddingRight: 30,
          },
          size: {
            width: 200,
            height: 40,
          },
        },
      },
      texts: [
        {
          text: '*Code expires in 3 hours. Exclusions apply.',
          styles: {
            font: {
              size: 13,
            },
          },
        },
      ],
    },
    logoImage: {
      path: 'oofos/oofos-logo-1.png',
      styles: {
        box: {
          paddingTop: 10,
          paddingBottom: 10,
        },
        size: {
          width: 200,
        },
      },
    },
  },
  teaser: {
    position: {
      verticalGap: 5,
      horizontalGap: 5,
    },
    mainStyles: {
      color: 'white',
      backgroundColor: 'black',
      font: {
        family: 'Arial',
        size: 13,
      },
    },
    texts: [
      {
        text: '{{discountTitle}} with code',
      },
      {
        text: '{{discountCode}}',
      },
      {
        text: 'This session only',
      },
    ],
  },
}

export const defaultWidget3Config: Widget3Config = {
  dialog: {
    root: {
      props: {
        styles: {
          colors: { color: 'black', backgroundColor: '#efdada' },
          font: { name: 'Arial', size: 15 },
        },
        minimizeButton: { color: '#6a7fec' },
      },
    },
    content: [
      {
        type: 'DialogContainer',
        props: {
          image: {
            imageUrl: '',
            desktopImagePostion: 'right',
            showMobileImage: true,
          },
          id: 'DialogContainer-f909ae55-48d6-46fc-808c-395fb6d56db0',
        },
      },
    ],
    zones: {
      'DialogContainer-f909ae55-48d6-46fc-808c-395fb6d56db0:content-panel': [
        {
          type: 'Image',
          props: {
            imageUrl: '',
            styles: { size: { width: 200 } },
            id: 'Image-eee8a6b4-8736-4584-8e74-6ec91c704d1c',
          },
        },
        {
          type: 'Text',
          props: {
            text: 'Kickstart Spring with Style',
            id: 'Text-f75a8852-d109-4b0c-89a6-61e8058a2fac',
            styles: {
              font: { size: 26 },
              box: { paddingTop: 0, marginTop: 28, marginBottom: 17.5 },
              align: 'center',
            },
          },
        },
        {
          type: 'Text',
          props: {
            text: '{{discountTitle}}!',
            id: 'Text-785a6dbf-a71b-45d2-8899-b3455490352d',
            styles: {
              font: { size: 64 },
              align: 'center',
            },
          },
        },
        {
          type: 'Text',
          props: {
            text: 'This session only.',
            styles: { align: 'center' },
            id: 'Text-505a3094-2514-47a8-8df8-5ac6f7da113f',
          },
        },
        {
          type: 'Container',
          props: {
            styles: {
              border: { style: 'solid', width: 1 },
              box: { paddingTop: 10, paddingBottom: 10 },
            },
            id: 'Container-57726378-d1d5-466e-ac78-5b9a33d3a50c',
          },
        },
        {
          type: 'Button',
          props: {
            text: 'No thanks',
            styles: {
              variant: 'link',
              box: {
                paddingTop: 15,
                paddingBottom: 15,
                paddingLeft: 25,
                paddingRight: 25,
              },
            },
            id: 'Button-e5997530-611f-4fa5-924f-baa00bb9c70a',
            action: { action: 'minimize-dialog' },
          },
        },
      ],
      'Container-57726378-d1d5-466e-ac78-5b9a33d3a50c:content-zone': [
        {
          type: 'Text',
          props: {
            text: '{{discountCode}}',
            styles: {
              align: 'center',
              font: { size: 30 },
              box: { marginTop: 0, marginBottom: 10 },
            },
            id: 'Text-49836466-119f-4d41-b725-5dc632d8c962',
          },
        },
        {
          type: 'Button',
          props: {
            text: 'COPY CODE',
            styles: {
              colors: {
                color: 'white',
                backgroundColor: '#216aae',
                hoverBackgroundColor: 'black',
              },
              box: {
                paddingTop: 10,
                paddingBottom: 10,
                paddingLeft: 0,
                paddingRight: 0,
              },
              size: { width: 200 },
              variant: 'default',
            },
            id: 'Button-e9b8f39c-feb0-4b89-b472-498aa83a60d5',
            action: { action: 'copy-code', actionTarget: '' },
          },
        },
        {
          type: 'Text',
          props: {
            text: 'This session only',
            styles: { align: 'center', box: { marginTop: 5, marginBottom: 5 } },
            id: 'Text-871fe557-033d-4ae0-9f1c-5bc67e2aa5c9',
          },
        },
      ],
    },
  },
  teaser: {
    root: {
      props: {
        styles: {
          colors: { color: 'white', backgroundColor: 'black' },
          font: { name: 'Arial', size: 13 },
        },
        position: { verticalGap: 5, horizontalGap: 5 },
      },
    },
    content: [
      {
        type: 'Text',
        props: {
          text: '{{discountTitle}} with code',
          styles: { align: 'center' },
          id: 'Text-c1ca9669-47ca-4bd9-b324-db7aae6218e8',
        },
      },
      {
        type: 'Text',
        props: {
          text: '{{discountCode}}',
          styles: { align: 'center' },
          id: 'Text-f9adbc65-3f74-4d95-9aa4-6b99247dc661',
        },
      },
      {
        type: 'Text',
        props: {
          text: 'This session only',
          styles: { align: 'center' },
          id: 'Text-f1e2bdc6-db6d-493b-8063-5424fcbf11a9',
        },
      },
    ],
    zones: {},
  },
}
