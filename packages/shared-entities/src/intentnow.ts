import { DocumentEntity } from './common'
import { DiscountCodeBasicInput } from '../shopify-api/admin.types'
import {
  DialogContent,
  DiscountContent,
  Widget2Config,
  Widget3Config,
} from './widget'

export const shoipifyStoresCollectionPath = 'ShopifyStores'
export const shopifyStoreClientCollectionPath = 'ShopifyStoreClients'
export const shopifyStoreGeneratedDiscountsCollectionPath = (
  shopifyStoreId: string
) => `ShopifyStores/${shopifyStoreId}/ShopifyStoreGeneratedDiscounts`
export const shopifyStoreWidgetsCollectionPath = (shopifyStoreId: string) =>
  `ShopifyStores/${shopifyStoreId}/ShopifyStoreWidgets`
export const shopifyStoreImagesCollectionPath = (shopifyStoreId: string) =>
  `ShopifyStores/${shopifyStoreId}/ShopifyStoreImages`
export const shopifyStoreLaunchConfigsCollectionPath = (
  shopifyStoreId: string
) => `ShopifyStores/${shopifyStoreId}/ShopifyStoreLaunchConfigs`

export interface ModelParameters {
  model: string
  floor: number
  ceiling: number
  start: number
  end: number
}

export interface ModelConfig {
  parameters: ModelParameters
}

export interface LaunchConfigVariant {
  widgetId?: string
  modelConfig: ModelConfig
}

export interface StoreLaunchConfig extends DocumentEntity {
  storeId: string
  createdAt: Date
  name: string

  //Need an experimentKey if we have more than one config
  experimentKey?: string
  configVariants: LaunchConfigVariant[]
}

export interface DiscountConfig {
  codeGen: {
    alphabet: string
    length: number
    prefix?: string
  }
  discountDurationInMinutes: number
  discountInput: DiscountCodeBasicInput
  cacheValidDurationInMinutes?: number
}

export interface ShopifyStorePromoConfig {
  model?: {
    overrides?: {
      promo: boolean
    }
    clientOverrides?: {
      [clientId: string]:
        | {
            promo: boolean
          }
        | undefined
    }
    //@Deprecated
    parameters?: ModelParameters
  }
  //@Deprecated
  dialog?: DialogContent
  //@Deprecated
  discount?: DiscountConfig
  //@Deprecated
  widget2?: Widget2Config

  selectedWidgetId?: string
}

//@deprecated
export interface ShopifyStorePromoConfigVariant {
  name: string
  model?: ModelConfig
  dialog?: DiscountContent
  discount?: DiscountConfig
}

export interface ShopifyStore extends DocumentEntity {
  createdAt: Date
  updatedAt?: Date

  name?: string
  shop: string
  disabled?: boolean
  website?: string
  appHandle?: string
  pendingInstall?: boolean
  promoConfig?: ShopifyStorePromoConfig
  //@Deprecated
  promoConfigVariants?: ShopifyStorePromoConfigVariant[]

  analyticsConfig?: {
    chartIds?: string[]
    internalDashboards?: {
      name: string
      link: string
    }[]
  }

  //derived fields
  _widgets?: ShopifyStoreWidget[]
  _currentLaunchConfig?: StoreLaunchConfig
}

export interface ActiveShopifyStoreConfig {
  storeId: string
  shop: string

  currentLaunchConfig?: StoreLaunchConfig
  launchConfigVariants: {
    modelConfig: ModelConfig
    discountConfig: DiscountConfig
    widget: {
      widgetId?: string
      dialog?: DialogContent
      widget2?: Widget2Config
      widget3?: Widget3Config
    }
  }[]

  modelOverrides?: {
    overrides?: {
      promo: boolean
    }
    clientOverrides?: {
      [clientId: string]:
        | {
            promo: boolean
          }
        | undefined
    }
  }
}

export interface ShopifyStoreClient extends DocumentEntity {
  clientId: string

  //@Deprecated
  gaClientId?: string

  createdAt: Date
  updatedAt: Date

  shops?: string[]

  cachedPromos: {
    [shopBase64: string]: {
      shop: string
      createdAt: Date
      validUntil?: Date
      discountId: string
      discountId2: string
      discount: DiscountContent
    }
  }
}

export interface ShopifyStoreGeneratedDiscount extends DocumentEntity {
  createdAt: Date
  shop: string
  clientId: string
  discountId: string
  discountId2?: string
  discount: DiscountContent
  discountStatus?: {
    syncedAt: Date
    status: string
    usageCount: number
  }
}

export enum WidgetType {
  widget2 = 'widget2',
  widget3 = 'widget3',
}
export interface ShopifyStoreWidget extends DocumentEntity {
  storeId: string
  type: WidgetType
  name: string
  createdAt: Date
  updatedAt: Date

  discount: DiscountConfig

  widget2?: Widget2Config
  widget3?: Widget3Config
}

export interface ShopifyStoreImage extends DocumentEntity {
  storeId: string
  name: string
  createdAt: Date

  fileInfo: {
    filename: string
    mimetype: string
    size: number
    ext: string
  }

  uploadInfo?: {
    cdnBucket: string
    path: string
  }

  imageUrl: string
}
