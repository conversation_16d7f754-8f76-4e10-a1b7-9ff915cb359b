import { DocumentDto } from './common.dto';
export declare class StoreShopifyConfigDto {
    myshopifyDomain: string;
    appHandle?: string;
}
export declare class StoreConfigDto {
    receiveEvents?: boolean;
    sendAmplitude?: boolean;
    showOffers?: boolean;
    predictOffers?: boolean;
    isLegacy?: boolean;
    modelOverrides?: any;
}
export declare class EffectiveStoreConfigDto {
    receiveEvents: boolean;
    sendAmplitude: boolean;
    showOffers: boolean;
    predictOffers: boolean;
    isLegacy: boolean;
    modelOverrides?: any;
}
export declare class MerchantPortalFeaturesDto {
    fullOfferEditor: boolean;
    internalDiscountConfigSection: boolean;
}
export declare class StoreDto extends DocumentDto {
    name: string;
    website: string;
    createdAt: Date;
    config?: StoreConfigDto;
    shopifyConfig?: StoreShopifyConfigDto;
    effectiveConfig?: EffectiveStoreConfigDto;
    merchantPortalFeatures?: MerchantPortalFeaturesDto;
}
export declare class StoreCreateDto {
    name: string;
    website: string;
    config?: StoreConfigDto;
    shopifyConfig?: StoreShopifyConfigDto;
}
declare const StoreUpdateDto_base: import("@nestjs/common").Type<Partial<StoreCreateDto>>;
export declare class StoreUpdateDto extends StoreUpdateDto_base {
}
declare const PaginatedStoresDto_base: {
    new (): {
        data: StoreDto[];
        meta: import("./common.dto").PaginationMetaDto;
    };
};
export declare class PaginatedStoresDto extends PaginatedStoresDto_base {
}
export declare class StoreAddUserDto {
    email: string;
}
export declare class LinkStoreRequestDto extends DocumentDto {
    status: string;
    storeType: string;
    storeRef: string;
    storeName: string;
    storeWebsite: string;
    source: string;
    createdAt: Date;
    expiresAt: Date;
}
export declare class CommitLinkStoreRequestDto {
    storeId: string;
}
export declare class ShopifyStoreIntegrationDto {
    linked: boolean;
    shopifyExtensions?: {
        webPixelOn?: boolean;
        promoWidgetOn?: boolean;
        promoWidgetToggleLink?: string;
    };
    shopifyStore?: {
        name: string;
        myshopifyDomain: string;
        appHandle: string;
        url: string;
        shopifyPlus: boolean;
        currencyCode: string;
    };
}
export declare class ShopifyStoreIntegrationUpdateDto {
    ['shopifyExtensions.webPixelOn']?: boolean;
}
export declare class StoreImageDto extends DocumentDto {
    name: string;
    imageUrl: string;
    createdAt: Date;
    fileInfo: {
        filename: string;
        mimetype: string;
        size: number;
        ext: string;
        format: string;
        width: number;
        height: number;
    };
}
export declare class StoreImageCreateDto {
    name?: string;
}
declare const PaginatedStoreImagesDto_base: {
    new (): {
        data: StoreImageDto[];
        meta: import("./common.dto").PaginationMetaDto;
    };
};
export declare class PaginatedStoreImagesDto extends PaginatedStoreImagesDto_base {
}
export declare class ModelConfigV1Dto {
    model: string;
    floor: number;
    ceiling: number;
    start: number;
    end: number;
}
export declare class StoreLaunchConfigVariantDto {
    modelConfigV1?: ModelConfigV1Dto;
}
export declare class StoreLaunchConfigDto extends DocumentDto {
    name: string;
    createdAt: Date;
    configVariants: StoreLaunchConfigVariantDto[];
}
export declare class StoreLaunchConfigCreateDto {
    name: string;
    configVariants: StoreLaunchConfigVariantDto[];
}
declare const PaginatedStoreLaunchConfigsDto_base: {
    new (): {
        data: StoreLaunchConfigDto[];
        meta: import("./common.dto").PaginationMetaDto;
    };
};
export declare class PaginatedStoreLaunchConfigsDto extends PaginatedStoreLaunchConfigsDto_base {
}
export declare class ActiveStoreLaunchConfigDto {
    activeLaunchConfig?: {
        launchConfig: StoreLaunchConfigDto;
        buckets: {
            allocation: number;
            configVariantIndex: number;
            configVariant: StoreLaunchConfigVariantDto;
        }[];
    };
}
export {};
