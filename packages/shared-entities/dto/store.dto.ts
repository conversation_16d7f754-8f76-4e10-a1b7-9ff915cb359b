import { DocumentDto } from './common.dto';
export declare class StoreShopifyConfigDto {
    myshopifyDomain: string;
    appHandle?: string;
}
export declare class StoreDto extends DocumentDto {
    name: string;
    website: string;
    shopifyConfig?: StoreShopifyConfigDto;
    createdAt: Date;
}
export declare class StoreCreateDto {
    name: string;
    website: string;
    shopifyConfig?: StoreShopifyConfigDto;
}
declare const StoreUpdateDto_base: import("@nestjs/common").Type<Partial<StoreCreateDto>>;
export declare class StoreUpdateDto extends StoreUpdateDto_base {
}
declare const PaginatedStoresDto_base: {
    new (): {
        data: StoreDto[];
        meta: import("./common.dto").PaginationMetaDto;
    };
};
export declare class PaginatedStoresDto extends PaginatedStoresDto_base {
}
export declare class LinkStoreRequestDto extends DocumentDto {
    status: string;
    storeType: string;
    storeRef: string;
    storeName: string;
    storeWebsite: string;
    source: string;
    createdAt: Date;
    expiresAt: Date;
}
export declare class CommitLinkStoreRequestDto {
    storeId: string;
}
export {};
