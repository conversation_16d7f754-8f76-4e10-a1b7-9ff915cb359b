import { DocumentDto } from './common.dto';
export declare class UserDto extends DocumentDto {
    authType: 'clerk' | 'firebase';
    createdAt: Date;
    updatedAt: Date;
    displayName: string;
    email: string;
}
export declare class UserCreateDto {
    _id: string;
    authType: 'clerk';
    displayName: string;
    email: string;
}
declare const UserUpdateDto_base: import("@nestjs/common").Type<Partial<UserCreateDto>>;
export declare class UserUpdateDto extends UserUpdateDto_base {
}
declare const PaginatedUsersDto_base: {
    new (): {
        data: UserDto[];
        meta: import("./common.dto").PaginationMetaDto;
    };
};
export declare class PaginatedUsersDto extends PaginatedUsersDto_base {
}
export declare class UserStoreAccessDto {
    userId: string;
    storeId: string;
    grantedBy: string;
    createdAt: Date;
}
export declare class UserStoreAccessCreateDto {
    storeId: string;
    grantedBy: string;
}
declare const PaginatedUserStoreAccessesDto_base: {
    new (): {
        data: UserStoreAccessDto[];
        meta: import("./common.dto").PaginationMetaDto;
    };
};
export declare class PaginatedUserStoreAccessesDto extends PaginatedUserStoreAccessesDto_base {
}
export {};
