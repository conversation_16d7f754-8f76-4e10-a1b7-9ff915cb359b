export declare enum ChartPeriod {
    last7Days = "7d",
    last30Days = "30d",
    last90Days = "90d"
}
export declare class ChartQueryDto {
    period: ChartPeriod;
}
export declare class FunnelChartDto {
    chartName: string;
    groups: {
        groupName: string;
        funnel: {
            funnelStep: string;
            count: number;
            conversionRate?: number;
        }[];
    }[];
}
export declare class AggregatedEventChartDto {
    chartName: string;
    unit: string;
    groups: {
        groupName: string;
        aggregatedValues: {
            name: string;
            value: number;
        }[];
    }[];
}
export declare class ChartDto {
    funnelChart?: FunnelChartDto;
    aggregatedEventChart?: AggregatedEventChartDto;
}
