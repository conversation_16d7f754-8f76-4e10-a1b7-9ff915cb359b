export declare class EventCustomerDto {
    customerId?: string;
    hash?: string;
}
export declare class EventRequestDto {
    id?: string;
    clientId: string;
    eventSource: string;
    name: string;
    shop: string;
    type: string;
    timestamp?: string;
    context?: any;
    data?: any;
    customData?: any;
    customer?: EventCustomerDto;
}
export declare class EventsRequestDto {
    type: string;
    events: EventRequestDto[];
}
export declare class EventResponseDto {
    message: string;
    prediction?: boolean;
}
export declare class RawEventRequestDto {
    name: string;
    shop: string;
    eventSource: string;
    event: any;
}
export declare class RawEventsRequestDto {
    type: string;
    events: RawEventRequestDto[];
}
export declare class RawEventResponseDto {
    message: string;
}
