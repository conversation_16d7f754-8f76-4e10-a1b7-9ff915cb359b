import { Type } from '@nestjs/common';
export declare class DocumentDto {
    _id: string;
}
export declare class CrudQueryDto {
    page?: number;
    limit?: number;
    sort?: string;
    filter?: string;
}
export declare class PaginationMetaDto {
    count: number;
    total: number;
    page: number;
    pageSize: number;
    pageCount: number;
}
export declare function PaginatedDataDto<T>(itemType: Type<T>): {
    new (): {
        data: T[];
        meta: PaginationMetaDto;
    };
};
export interface SortQuery {
    field: string;
    order: 'asc' | 'desc';
}
export declare enum FilterOp {
    eq = "eq",
    contains = "contains"
}
export interface FilterQuery {
    field: string;
    op: FilterOp;
    value: string;
}
export interface CrudQuery {
    page?: number;
    limit?: number;
    sorts?: SortQuery[];
    filters?: FilterQuery[];
}
